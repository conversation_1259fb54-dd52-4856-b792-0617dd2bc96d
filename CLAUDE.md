# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands
- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run build-no-errors` - Build the project ignoring TypeScript errors
- `npm run lint` - Run ESLint for TypeScript and TSX files
- `npm run preview` - Preview the production build locally
- `npm run types:supabase` - Generate TypeScript types from Supabase schema

## Code Style Guidelines
- **TypeScript**: Use TypeScript for type safety with React. Project uses strict: false.
- **Imports**: Use absolute imports with '@/' prefix (`import Component from '@/components/Component'`)
- **Components**: Use functional components with React hooks
- **Forms**: Utilize react-hook-form with zod schema validation
- **UI Components**: Based on shadcn/ui pattern with Radix UI primitives
- **CSS**: Use Tailwind CSS for styling with custom color scheme defined in tailwind.config.js
- **Path Aliases**: '@/' maps to './src/' directory
- **Error Handling**: Follow React error boundaries pattern
- **Naming**: Use PascalCase for components, camelCase for functions and variables