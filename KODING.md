# KODING.md

## Build/Lint/Test Commands

- Build: `npm run build`
- Lint: `npm run lint`
- Test: `npm run test`
- Single Test: (Check package.json for specific test commands, e.g., `npm test -- <test_file>`)

## Code Style Guidelines

- Imports: Use absolute imports (`src/...`) where possible.
- Formatting: Follow Prettier and ESLint rules. Run `npm run format` to format.
- Types: Use TypeScript types for all variables, function parameters, and return values.
- Naming: Use camelCase for variables and functions, PascalCase for components.
- Error Handling: Use try-catch blocks and proper error logging.
- Components: Create small, reusable components.
- State Management: Use React Context API or other state management libraries when needed.
