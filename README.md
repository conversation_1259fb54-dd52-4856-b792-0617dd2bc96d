# Oh Nanny Blue - Nanny & Tutor Management Platform

A comprehensive CRM and management platform for nanny and tutor placement services. This application facilitates the connection between families seeking childcare/tutoring services and qualified candidates.

## 🎯 Overview

Oh Nanny Blue is a full-featured web application designed for nanny and tutor placement agencies. It provides tools for managing candidates (nannies and tutors), clients (families), deals, and the entire placement process from initial contact to successful placement.

### Key Features

- **Candidate Management**: Comprehensive profiles for nannies and tutors with skills, experience, and availability
- **Client Management**: Family profiles with requirements, preferences, and contact information
- **Deal Tracking**: Complete deal lifecycle management with status tracking and revenue analytics
- **CRM System**: Contact management with lead tracking and client relationship tools
- **Analytics Dashboard**: Revenue tracking, client metrics, and business intelligence
- **Form System**: Public forms for candidate applications and client requests
- **Permission System**: Role-based access control for different user types
- **File Management**: Document upload and storage for contracts, CVs, and certifications

## 🏗️ Architecture

### Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: Tailwind CSS + shadcn/ui components
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **State Management**: React Query + Context API
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts for analytics visualization
- **Routing**: React Router v6

### Project Structure

```
src/
├── components/          # React components
│   ├── auth/           # Authentication components
│   ├── candidates/     # Candidate management
│   ├── clients/        # Client/CRM management
│   ├── crm/           # CRM tables and views
│   ├── dashboard/     # Analytics and metrics
│   ├── deals/         # Deal management
│   ├── forms/         # Public application forms
│   ├── nannies/       # Nanny-specific components
│   ├── tutors/        # Tutor-specific components
│   └── ui/            # Reusable UI components
├── contexts/           # React contexts
├── hooks/             # Custom React hooks
├── lib/               # Utility functions
├── schemas/           # Zod validation schemas
├── services/          # API service functions
└── types/             # TypeScript type definitions
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account and project

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Oh-Nanny-Blue-UI
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file with your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_PROJECT_ID=your_project_id
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Generate Supabase types** (optional)
   ```bash
   npm run types:supabase
   ```

## 📋 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build-no-errors` - Build ignoring TypeScript errors
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build
- `npm run types:supabase` - Generate TypeScript types from Supabase

## 🔐 User Roles & Permissions

The application supports multiple user types with different access levels:

- **Admin**: Full access to all features
- **Manager**: Access to CRM, deals, and candidate management
- **Nanny/Tutor**: Limited access to their own profile
- **Anonymous**: Access to public application forms only

## 🎨 UI Components

Built with a modern design system using:

- **shadcn/ui**: High-quality, accessible components
- **Radix UI**: Unstyled, accessible primitives
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Beautiful, customizable icons

## 📊 Features Overview

### Dashboard & Analytics
- Revenue tracking and profit analysis
- Client acquisition metrics
- Deal pipeline visualization
- Activity feed and notifications

### CRM System
- Contact and client management
- Lead tracking and conversion
- Client profile pages with deal history
- Searchable and filterable tables

### Candidate Management
- Nanny and tutor profiles
- Skills and certification tracking
- Availability calendars
- CV and document management

### Deal Management
- Deal creation and tracking
- Status workflow management
- Revenue and commission tracking
- Contract document storage

### Public Forms
- Candidate application forms
- Client request forms
- Multi-step form wizards
- File upload capabilities

## 🔧 Configuration

### Tailwind Configuration
Custom color scheme and component variants are defined in `tailwind.config.js`.

### Supabase Integration
Database schema and migrations are managed in the `supabase/` directory.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions, please contact the development team or refer to the internal documentation.
