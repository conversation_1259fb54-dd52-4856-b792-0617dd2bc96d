import { Loader } from "lucide-react";
import { Suspense, useEffect } from "react";
import {
  Outlet,
  Route,
  Routes,
  useLocation,
  useNavigate,
  useRoutes,
} from "react-router-dom";
import routes from "tempo-routes";
import PermissionChangeListener from "./components/auth/PermissionChangeListener";
import RouteGuard from "./components/auth/RouteGuard";
import SignIn from "./components/auth/SignIn";
import CandidatesView from "./components/candidates/CandidatesView";
import ConnectView from "./components/connect/ConnectView";
import ClientProfileView from "./components/crm/clients/ClientProfileView";
import CRMView from "./components/crm/CRMView";
import DealsPage from "./components/deals/DealsPage";
import DealsView from "./components/deals/DealsView";
import NotFound from "./components/error/NotFound";
import CandidateFormPage from "./components/forms/CadidateFormPage";
import NannyRequestFormPage from "./components/forms/NannyRequestFormPage";
import Home from "./components/home";
import NannyProfile from "./components/nannies/NanniesProfileView";
import NanniesView from "./components/nannies/NanniesView";
import { CitiesProvider } from "./components/providers/CitiesProvider";
import { QueryProvider } from "./components/providers/QueryProvider";
import SettingsView from "./components/settings/SettingsView";
import TutorsView from "./components/tutors/TutorsView";
import Header from "./components/ui/layout/Header";
import NannyLayout from "./components/ui/layout/NannyLayout";
import { Toaster } from "./components/ui/toaster";
import { AuthProvider } from "./contexts/AuthContext";
import { PermissionsProvider } from "./contexts/PermissionsContext";
import { handleAuthRedirect } from "./lib/AuthHandler";

// Layout component that includes the header
const DashboardLayout = () => {
  return (
    <>
      <Header />
      <Outlet />
      <Toaster />
      <PermissionChangeListener />
    </>
  );
};

function App() {
  const navigate = useNavigate();
  const location = useLocation();

  // Check for OAuth redirects on initial load
  useEffect(() => {
    // Handle redirects from Supabase OAuth
    const isHandlingAuthRedirect = handleAuthRedirect();

    // If handling an auth redirect, clear the last route
    if (isHandlingAuthRedirect) {
      sessionStorage.removeItem("lastRoute");
    }
  }, [navigate, location]);

  return (
    <AuthProvider>
      <PermissionsProvider>
        <QueryProvider>
          <CitiesProvider>
            <Suspense
              fallback={
                <div className="min-h-screen bg-background">
                  <Loader className="animate-spin" />
                </div>
              }
            >
              <div className="min-h-screen">
                <Toaster />
                <Routes>
                  {/* Public Routes */}
                  <Route
                    path="/"
                    element={
                      <RouteGuard>
                        <SignIn />
                      </RouteGuard>
                    }
                  />

                  {/* Always accessible form routes */}
                  <Route
                    path="/candidate-form"
                    element={
                      <RouteGuard>
                        <Suspense
                          fallback={<Loader className="animate-spin" />}
                        >
                          <CandidateFormPage />
                        </Suspense>
                      </RouteGuard>
                    }
                  />
                  <Route
                    path="/nanny-request-form"
                    element={
                      <RouteGuard>
                        <Suspense
                          fallback={<Loader className="animate-spin" />}
                        >
                          <NannyRequestFormPage />
                        </Suspense>
                      </RouteGuard>
                    }
                  />

                  <Route
                    path="/my-profile/:id"
                    element={
                      <RouteGuard>
                        <NannyLayout
                          children={<NannyProfile isNannyUser={true} />}
                        />
                      </RouteGuard>
                    }
                  />

                  {/* Protected Routes */}
                  <Route
                    element={
                      <RouteGuard>
                        <DashboardLayout />
                      </RouteGuard>
                    }
                  >
                    <Route path="/dashboard" element={<Home />} />
                    <Route path="/crm" element={<CRMView />} />
                    <Route path="/deals" element={<DealsView />} />
                    <Route path="/deals/:id" element={<DealsPage />} />
                    <Route
                      path="/crm/client/:id"
                      element={<ClientProfileView />}
                    />
                    <Route
                      path="/crm/contact/:id"
                      element={<ClientProfileView />}
                    />
                    <Route path="/tutors" element={<TutorsView />} />
                    <Route path="/candidates" element={<CandidatesView />} />
                    <Route path="/nannies" element={<NanniesView />} />
                    {/* Nanny profile for regular users is still in the dashboard layout */}
                    <Route
                      path="/candidates/:id"
                      element={<NannyProfile isNannyUser={false} />}
                    />
                    <Route
                      path="/nannies/:id"
                      element={<NannyProfile isNannyUser={false} />}
                    />
                    <Route
                      path="/tutors/:id"
                      element={<NannyProfile isNannyUser={false} />}
                    />
                    <Route path="/connect" element={<ConnectView />} />
                    <Route path="/settings" element={<SettingsView />} />
                  </Route>

                  {/* Fallback Route */}
                  <Route path="*" element={<NotFound />} />
                </Routes>

                {/* Optional Routes from `tempo-routes` */}
                {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
              </div>
            </Suspense>
          </CitiesProvider>
        </QueryProvider>
      </PermissionsProvider>
    </AuthProvider>
  );
}

export default App;
