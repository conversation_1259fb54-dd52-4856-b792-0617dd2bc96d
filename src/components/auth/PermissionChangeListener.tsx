import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissionChangeListener } from "@/hooks/usePermissionChangeListener";
import { useEffect } from "react";

/**
 * Component that listens for permission changes and shows a toast notification
 * This component doesn't render anything, it just listens for permission changes
 */
const PermissionChangeListener = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Show a toast notification when permissions change
  const handlePermissionChange = () => {
    toast({
      title: "Ενημέρωση Δικαιωμάτων",
      description: "Τα δικαιώματά σας έχουν ενημερωθεί.",
      duration: 5000,
    });
  };

  // Use the permission change listener hook
  usePermissionChangeListener(handlePermissionChange);

  // Log when the component mounts and unmounts
  useEffect(() => {
    console.debug("PermissionChangeListener mounted");
    return () => {
      console.debug("PermissionChangeListener unmounted");
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default PermissionChangeListener;
