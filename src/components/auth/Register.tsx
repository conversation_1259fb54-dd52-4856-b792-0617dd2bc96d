import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/lib/supabase";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const Register = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    name: "",
  });

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            name: formData.name,
          },
        },
      });

      if (error) throw error;

      if (data) {
        navigate("/dashboard");
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-light/20 via-white to-light/20">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto bg-gradient-to-br from-white to-light/20 rounded-2xl shadow-lg overflow-hidden border border-light/30 hover:shadow-xl transition-all duration-300">
          <div className="bg-gradient-to-r from-primary to-secondary p-6 flex items-center justify-center">
            <div className="flex items-center gap-3 text-white">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-white"
              >
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
              </svg>
              <span className="text-xl font-medium">Nanny Blue</span>
            </div>
          </div>

          {/* Register form */}
          <div className="p-8">
            <div className="w-full space-y-6">
              <div className="space-y-2">
                <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                  Δημιουργία λογαριασμού
                </h1>
                <p className="text-sm text-primary hidden md:block">
                  Συμπληρώστε τα στοιχεία σας για να δημιουργήσετε λογαριασμό
                </p>
              </div>

              <form onSubmit={handleRegister} className="space-y-4">
                <Input
                  type="text"
                  placeholder="Όνομα"
                  className="border-light/30 focus-visible:ring-primary"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  required
                />
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  className="border-light/30 focus-visible:ring-primary"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  required
                />
                <Input
                  type="password"
                  placeholder="Κωδικός"
                  className="border-light/30 focus-visible:ring-primary"
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  required
                />

                {error && (
                  <p className="text-sm text-destructive mt-2">{error}</p>
                )}

                <Button
                  type="submit"
                  className="w-full h-10 bg-gradient-to-r from-primary to-secondary hover:from-primary/80 hover:to-secondary/80 text-white font-medium"
                  disabled={loading}
                >
                  {loading ? "Παρακαλώ περιμένετε..." : "Εγγραφή"}
                </Button>
              </form>

              <p className="text-xs text-center text-primary">
                Κάνοντας εγγραφή συμφωνείτε με τους{" "}
                <a
                  href="#"
                  className="text-secondary hover:text-secondary/80 hover:underline"
                >
                  Όρους Χρήσης
                </a>{" "}
                και την{" "}
                <a
                  href="#"
                  className="text-secondary hover:text-secondary/80 hover:underline"
                >
                  Πολιτική Απορρήτου
                </a>{" "}
                μας
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
