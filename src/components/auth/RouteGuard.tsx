import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/contexts/PermissionsContext";
import {
  RouteType,
  getRouteConfig,
  isRouteAccessible,
} from "@/lib/routeConfig";
import {
  RoutePermissionType,
  getRoutePermission,
} from "@/lib/routePermissions";
import { Loader } from "lucide-react";
import { ReactNode, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

interface RouteGuardProps {
  children: ReactNode;
}

const RouteGuard = ({ children }: RouteGuardProps) => {
  const { isAuthenticated, isAnonymous, isLoading, user } = useAuth();
  const { hasPagePermission, isLoading: isLoadingPermissions } =
    usePermissions();
  const location = useLocation();
  const navigate = useNavigate();
  const [isCheckingAccess, setIsCheckingAccess] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const [nannyId, setNannyId] = useState<string | null>(null);
  const [isNannyViewingOwnProfile, setIsNannyViewingOwnProfile] =
    useState(false);
  useEffect(() => {
    const checkAccess = async () => {
      if (isLoading || isLoadingPermissions) {
        return;
      }

      // Update the current path ref

      setIsCheckingAccess(true);

      try {
        // Get the route configuration for the current path
        const routeConfig = getRouteConfig(location.pathname);

        // If no route configuration is found, default to protected
        const routeType = routeConfig?.type || RouteType.PROTECTED;
        let redirectTo = routeConfig?.redirectTo || "/";

        console.log(`Checking access for route: ${location.pathname}`);

        // Determine if user is a nanny
        const isNanny =
          isAuthenticated &&
          !isAnonymous &&
          (user?.app_metadata?.role === "nanny" ||
            user?.app_metadata?.role === "both") &&
          !!user?.app_metadata?.candidate_id;

        if (isNanny) {
          const nannyId = user?.app_metadata?.candidate_id;

          try {
            if (!user || !nannyId) {
              console.error("Error fetching nanny data:", "Nanny ID not found");
            } else {
              setNannyId(nannyId);

              // For nannies, redirect to their profile from ANY route except their own profile
              // and anonymous routes (like forms)
              // This ensures nannies can ONLY see their own profile and anonymous routes, regardless of permissions
              const isAnonymousRoute = routeType === RouteType.ANONYMOUS;
              if (
                !location.pathname.startsWith(`/my-profile/${nannyId}`) &&
                !isAnonymousRoute &&
                isAuthenticated &&
                !isAnonymous
              ) {
                console.debug(
                  `Nanny detected on ${location.pathname}, redirecting to profile`
                );
                navigate(`/my-profile/${nannyId}`, { replace: true });
                setHasAccess(false);
                setIsCheckingAccess(false);
                return;
              }

              // Check if nanny is viewing their own profile
              if (location.pathname.startsWith("/my-profile/")) {
                const pathNannyId = location.pathname.split("/").pop();
                const isOwnProfile = pathNannyId === nannyId;

                // Only update state if it's different to avoid re-renders
                if (isNannyViewingOwnProfile !== isOwnProfile) {
                  setIsNannyViewingOwnProfile(isOwnProfile);
                }

                // If nanny is trying to view another nanny's profile, redirect to their own
                if (
                  !isOwnProfile &&
                  routeType === RouteType.NANNY_PROFILE &&
                  location.pathname !== `/my-profile/${nannyId}`
                ) {
                  console.log("Nanny redirected to their own profile");
                  navigate(`/my-profile/${nannyId}`, { replace: true });
                  setHasAccess(true);
                  setIsCheckingAccess(false);
                  return;
                }
              }
            }
          } catch (error) {
            console.error("Error in nanny check:", error);
          }
        }

        // This section is now handled earlier in the code for all routes, not just protected ones
        // We keep a simplified version just as a fallback
        if (isNanny && !nannyId) {
          console.error("Nanny ID not found, redirecting to home");
          navigate("/", { replace: true });
          setHasAccess(false);
          setIsCheckingAccess(false);
          return;
        }

        // Check if the route is accessible based on auth state
        // For nannies, we've already handled access control above, so this is just for non-nannies
        let canAccess = isNanny
          ? location.pathname.startsWith(`/my-profile/${nannyId}`) // Nannies can only access their profile
          : isRouteAccessible(
              routeType,
              isAuthenticated,
              isAnonymous,
              location.pathname,
              isNanny
            );

        // If the user has access based on auth state, check permissions for protected routes
        if (canAccess && routeType === RouteType.PROTECTED && !isNanny) {
          // Get the route permission configuration
          const routePermission = getRoutePermission(location.pathname);

          console.debug(
            `Route permission for ${location.pathname}:`,
            routePermission
          );

          if (routePermission) {
            // Check if this route requires specific permissions
            if (
              routePermission.type ===
                RoutePermissionType.REQUIRES_PERMISSION &&
              routePermission.permissionPage
            ) {
              // Check if user has view permission for this page
              const hasPermission = hasPagePermission(
                routePermission.permissionPage,
                "view"
              );

              if (!hasPermission) {
                console.error(
                  `Permission denied for page: ${routePermission.permissionPage}`
                );
                canAccess = false;
                redirectTo = "/dashboard"; // Redirect to dashboard if permission denied
              }
            }
            // If route is ALL_AUTHENTICATED, no additional permission check needed
            else if (
              routePermission.type === RoutePermissionType.ALL_AUTHENTICATED
            ) {
              console.debug(
                `Route ${location.pathname} is accessible to all authenticated users`
              );
              // No additional permission check needed, canAccess remains true
            }
          } else {
            // If no route permission configuration found, fall back to the old behavior
            // Extract the page name from the path
            const pathSegments = location.pathname.split("/");
            let pageName = pathSegments[1]
              ? pathSegments[1].toLowerCase()
              : "connect";

            console.error(
              `Falling back to path-based permission check for: ${location.pathname}, pageName: ${pageName}`
            );

            // Check if user has view permission for this page
            const hasPermission = hasPagePermission(pageName, "view");

            if (!hasPermission) {
              console.error(`Permission denied for page: ${pageName}`);
              canAccess = false;
              redirectTo = "/dashboard"; // Redirect to dashboard if permission denied
            }
          }
        }

        if (!canAccess) {
          console.error(`Access denied, redirecting to: ${redirectTo}`);

          // Save the current path for redirection after login (if applicable)
          if (routeType === RouteType.PROTECTED) {
            sessionStorage.setItem("redirectAfterLogin", location.pathname);
          }

          navigate(redirectTo, { replace: true });
          setHasAccess(false);
        } else if (
          routeType === RouteType.PUBLIC &&
          isAuthenticated &&
          !isAnonymous
        ) {
          // Redirect authenticated users from public routes to dashboard
          console.debug(
            `Public route, authenticated user, redirecting to: ${redirectTo}`
          );
          navigate(redirectTo, { replace: true });
          setHasAccess(false);
        } else {
          // User has access to the route
          console.debug(`Access granted to: ${location.pathname}`);
          setHasAccess(true);

          // Store the current path for restoration after refresh
          if (
            routeType === RouteType.PROTECTED &&
            isAuthenticated &&
            !isAnonymous
          ) {
            sessionStorage.setItem("lastRoute", location.pathname);
          }
        }
      } catch (error) {
        console.error("Error checking route access:", error);
        setHasAccess(false);
        navigate("/", { replace: true });
      } finally {
        setIsCheckingAccess(false);
      }
    };

    checkAccess();
  }, [
    isLoading,
    isLoadingPermissions,
    isAuthenticated,
    isAnonymous,
    location.pathname,
    navigate,
    user,
    hasPagePermission,
    // Don't include state variables that are set within the effect
    // Don't include refs as they don't trigger re-renders
  ]);

  // Special case for nannies - check if we need to redirect after loading
  useEffect(() => {
    // Only run this effect when auth and permissions are loaded
    if (!isLoading && !isLoadingPermissions && !isCheckingAccess) {
      const isNanny =
        isAuthenticated &&
        !isAnonymous &&
        (user?.app_metadata?.role === "nanny" ||
          user?.app_metadata?.role === "both") &&
        !!user?.app_metadata?.candidate_id;

      if (isNanny) {
        const nannyId = user?.app_metadata?.candidate_id;

        // Check if this is an anonymous route (like a form)
        const routeConfig = getRouteConfig(location.pathname);
        const routeType = routeConfig?.type || RouteType.PROTECTED;
        const isAnonymousRoute = routeType === RouteType.ANONYMOUS;

        // If nanny is not on their profile page and not on an anonymous route, redirect them
        if (
          nannyId &&
          !location.pathname.startsWith(`/my-profile/${nannyId}`) &&
          !isAnonymousRoute
        ) {
          navigate(`/my-profile/${nannyId}`, { replace: true });
        }
      }
    }
  }, [
    isLoading,
    isLoadingPermissions,
    isCheckingAccess,
    isAuthenticated,
    isAnonymous,
    user,
    location.pathname,
    navigate,
  ]);

  // Show loading indicator while checking access or while auth/permissions are loading
  if (isLoading || isLoadingPermissions || isCheckingAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader className="animate-spin h-8 w-8 text-primary" />
      </div>
    );
  }

  // For all other cases, render children normally if they have access
  return hasAccess ? <>{children}</> : null;
};

export default RouteGuard;
