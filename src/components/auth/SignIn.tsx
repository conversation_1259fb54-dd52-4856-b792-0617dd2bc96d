import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { supabase } from "@/lib/supabase";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const SignIn = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const translateError = (errorMessage: string): string => {
    // Map of English error messages to Greek translations
    const errorTranslations: Record<string, string> = {
      "Invalid login credentials": "Μη έγκυρα στοιχεία σύνδεσης",
      "Email not confirmed": "Το email δεν έχει επιβεβαιωθεί",
      "Invalid email or password": "Μη έγκυρο email ή κωδικός πρόσβασης",
      "Email rate limit exceeded": "Υπέρβαση ορίου προσπαθειών email",
      "Password should be at least 6 characters":
        "Ο κωδικός πρέπει να έχει τουλάχιστον 6 χαρακτήρες",
      "User not found": "Ο χρήστης δεν βρέθηκε",
      "Too many requests": "Πάρα πολλές αιτήσεις",
    };

    // Return the Greek translation if available, otherwise return the original message
    return (
      errorTranslations[errorMessage] ||
      "Σφάλμα σύνδεσης. Παρακαλώ δοκιμάστε ξανά."
    );
  };

  const [anonymousId, setAnonymousId] = useState<string | null>(null);

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // if (isAnonymous) {
      //   const success = await upgradeAnonymousUser(formData.email, formData.password);

      //   if (success) {
      //     navigate("/dashboard");
      //     return;
      //   }
      // }

      // Regular sign in flow
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      });

      if (error) throw error;

      navigate("/dashboard");
    } catch (error: any) {
      setError(translateError(error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      // Store current anonymous ID for linking after OAuth redirect
      if (anonymousId) {
        // Store anonymous ID in localStorage to retrieve after OAuth redirect
        localStorage.setItem("anonymousUserIdForLink", anonymousId);
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${window.location.origin}/dashboard`,
        },
      });
      if (error) throw error;
    } catch (error: any) {
      setError(translateError(error.message));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-light/30 via-accent/20 to-light/30">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-md mx-auto bg-gradient-to-br from-white to-light/20 rounded-2xl shadow-lg overflow-hidden border border-light/30 hover:shadow-xl transition-all duration-300">
          <div className=" p-6 flex items-center justify-center">
            <div className="flex items-center gap-3 text-white">
              <div className=" p-2 rounded-md flex items-center justify-center">
                <div className="bg-transparent p-1 rounded-sm flex items-center justify-center">
                  <img
                    src="/logo.png"
                    alt="Nanny Blue Logo"
                    className="h-auto w-auto max-w-full max-h-full "
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sign in form */}
          <div className="p-8">
            <div className="w-full space-y-6">
              <div className="space-y-2">
                <h1 className="text-2xl font-bold text-primary">
                  Είσοδος στον λογαριασμό
                </h1>
                <p className="text-sm text-secondary hidden md:block">
                  Συμπληρώστε το email σας και συνδεθείτε στον λογαριασμό
                </p>
              </div>

              <form onSubmit={handleEmailSignIn} className="space-y-4">
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  className="border-light/30 focus-visible:ring-primary"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  required
                />
                <Input
                  type="password"
                  placeholder="Κωδικός"
                  className="border-light/30 focus-visible:ring-primary"
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  required
                />

                {error && (
                  <p className="text-sm text-destructive mt-2">{error}</p>
                )}

                <Button
                  type="submit"
                  className="w-full h-10 bg-gradient-to-r from-primary to-secondary hover:from-primary-hover hover:to-secondary-hover text-white font-medium"
                  disabled={loading}
                >
                  {loading ? "Παρακαλώ περιμένετε..." : "Είσοδος με Email"}
                </Button>
              </form>

              <div className="relative hidden md:block">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-light/30" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-light/20 px-2 text-secondary">
                    Ή ΣΥΝΕΧΕΙΑ ΜΕ
                  </span>
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full h-10 font-normal flex items-center justify-center gap-2 hover:bg-light/20 border border-light/30 text-primary transition-all duration-300"
                onClick={handleGoogleSignIn}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="24"
                  viewBox="0 0 24 24"
                  width="24"
                >
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="#4285F4"
                  />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335"
                  />
                </svg>
                Google
              </Button>

              <p className="text-xs text-center text-secondary">
                Κάνοντας είσοδο συμφωνείτε με τους{" "}
                <a
                  href="#"
                  className="text-primary hover:text-primary-hover hover:underline"
                >
                  Όρους Χρήσης
                </a>{" "}
                και την{" "}
                <a
                  href="#"
                  className="text-primary hover:text-primary-hover hover:underline"
                >
                  Πολιτική Απορρήτου
                </a>{" "}
                μας
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
