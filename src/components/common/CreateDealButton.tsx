import { But<PERSON> } from "@/components/ui/button";
import { Handshake } from "lucide-react";
import { useState } from "react";
import CreateDealSheet from "../crm/forms/CreateDealSheet";

interface CreateDealButtonProps {
  candidateId?: string;
  clientId?: string;
  variant?: "default" | "outline" | "secondary" | "destructive" | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  onSuccess?: () => void;
}

/**
 * A reusable button component for creating deals
 * Can be used in two modes:
 * 1. With candidateId - opens CreateDealSheetForCandidate to select a client
 * 2. With clientId - opens CreateDealSheet to select a candidate
 */
const CreateDealButton = ({
  candidateId,
  clientId,
  variant = "default",
  size = "default",
  className = "",
  onSuccess,
}: CreateDealButtonProps) => {
  const [isDealDialogOpen, setIsDealDialogOpen] = useState(false);

  const handleDealDialogClose = (open: boolean) => {
    setIsDealDialogOpen(open);
  };

  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={`flex items-center gap-2 ${className}`}
        onClick={() => setIsDealDialogOpen(true)}
      >
        <Handshake className="h-4 w-4" />
        <span>Δημιουργία Deal</span>
      </Button>

      {
        <CreateDealSheet
          open={isDealDialogOpen}
          onOpenChange={handleDealDialogClose}
          clientId={clientId}
          candidateId={candidateId}
          onSuccess={handleSuccess}
        />
      }
    </>
  );
};

export default CreateDealButton;
