import { TableSkeleton } from "@/components/ui/TableSkeleton";
import { updatePaginationWithFilters } from "@/lib/filterUtils";
import { PaginatedResponse, PaginationParams } from "@/lib/paginationUtils";
import { useEffect, useRef, useState } from "react";
import BasicTable, { ColumnConfig, ContextMenuItem } from "../crm/BasicTable";
import { useToast } from "../ui/use-toast";

interface NannyBlueTableProps<T extends Record<string, any>> {
  fetchData: (
    forceRefresh: boolean,
    paginationParams?: PaginationParams
  ) => Promise<T[] | PaginatedResponse<T>>;
  columns: ColumnConfig<T>[];
  contextMenuItems?: ContextMenuItem[];
  onRowClick?: (item: T) => void;
  shouldReload?: boolean;
}

const NannyBlueTable = <T extends Record<string, any>>({
  fetchData,
  columns,
  contextMenuItems,
  onRowClick,
  shouldReload,
}: NannyBlueTableProps<T>) => {
  const [data, setData] = useState<T[] | PaginatedResponse<T>>([]);
  const [loading, setLoading] = useState(true);
  const [filterLoading, setFilterLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    itemsPerPage: 10,
    filters: {},
  });
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const isMounted = useRef(false);
  const { toast } = useToast();

  const isLoadingRef = useRef(false);
  const lastFetchTimeRef = useRef(0);

  const loadData = async (
    forceRefresh = false,
    paginationParams?: PaginationParams
  ) => {
    const now = Date.now();

    const currentFiltersStr = JSON.stringify(pagination.filters || {});
    const newFiltersStr = paginationParams
      ? JSON.stringify(paginationParams.filters || {})
      : currentFiltersStr;
    const filtersChanged = currentFiltersStr !== newFiltersStr;

    if (
      hasInitiallyLoaded &&
      totalCount === 0 &&
      !forceRefresh &&
      !filtersChanged
    ) {
      return;
    }

    if (isLoadingRef.current && !forceRefresh) {
      console.info("Request already in progress, skipping");
      return;
    }

    lastFetchTimeRef.current = now;

    isLoadingRef.current = true;

    try {
      const params = paginationParams || pagination;

      const isFilterChange =
        paginationParams &&
        Object.keys(paginationParams.filters || {}).length > 0 &&
        paginationParams.page === 1;

      if (isFilterChange) {
        setFilterLoading(true);
      } else if (forceRefresh) {
        await new Promise((resolve) => setTimeout(resolve, 0));
        setLoading(true);
      }

      console.debug("Loading data with params:", params);

      const data = await fetchData(forceRefresh, params);

      // Use a timeout to prevent UI freezing when setting state with large data
      await new Promise((resolve) => setTimeout(resolve, 0));
      setData(data);

      // Update total count and set initial load flag
      if ("totalCount" in data) {
        setTotalCount(data.totalCount);
      } else if (Array.isArray(data)) {
        setTotalCount(data.length);
      } else {
        setTotalCount(0);
      }

      if (!hasInitiallyLoaded) {
        setHasInitiallyLoaded(true);
      }
    } catch (error) {
      console.error("Error loading nannies:", error);
      toast({
        title: "Error",
        description: "Failed to load nannies.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setFilterLoading(false);
      isLoadingRef.current = false;
    }
  };

  // Initial load effect
  useEffect(() => {
    if (!isMounted.current) {
      loadData();
      isMounted.current = true;
    }
  }, []);

  // Separate effect for pagination changes to avoid potential infinite loops
  const prevPageRef = useRef(pagination.page);
  const prevItemsPerPageRef = useRef(pagination.itemsPerPage);
  const prevFiltersRef = useRef(JSON.stringify(pagination.filters || {}));

  // Dedicated effect for shouldReload changes
  useEffect(() => {
    // Skip if we haven't loaded initially yet or if this is the first render
    if (!hasInitiallyLoaded || !isMounted.current) return;

    if (shouldReload) {
      loadData(true, pagination);
    }
  }, [shouldReload, hasInitiallyLoaded]);

  useEffect(() => {
    // Skip if we haven't loaded initially yet or if this is the first render
    if (!hasInitiallyLoaded || !isMounted.current) return;

    const currentFiltersStr = JSON.stringify(pagination.filters || {});

    // Only reload if pagination or filters have changed
    if (
      prevPageRef.current !== pagination.page ||
      prevItemsPerPageRef.current !== pagination.itemsPerPage ||
      prevFiltersRef.current !== currentFiltersStr
    ) {
      // loadData(false, pagination);

      // Update refs with current values
      prevPageRef.current = pagination.page;
      prevItemsPerPageRef.current = pagination.itemsPerPage;
      prevFiltersRef.current = currentFiltersStr;
    }
  }, [
    pagination.page,
    pagination.itemsPerPage,
    pagination.filters,
    hasInitiallyLoaded,
  ]);

  const handlePaginationChange = (newPagination: PaginationParams) => {
    // Don't make a new request if there are no results
    if (totalCount === 0) {
      setPagination(newPagination);
      return;
    }

    // Don't make a new request if the pagination hasn't changed and filters haven't changed
    if (
      newPagination.page === pagination.page &&
      newPagination.itemsPerPage === pagination.itemsPerPage
    ) {
      return;
    }
    setPagination(newPagination);
    loadData(false, newPagination);
  };

  // Create a debounced version of the filter change handler
  // We'll use a closure to capture the latest state values
  const debouncedFilterRef = useRef<any>(null);

  // Function to handle debounced filter changes
  const debouncedLoadData = (
    isAnyFilterReset = false,
    filters: Record<string, any>
  ) => {
    // Clear any existing timeout
    if (debouncedFilterRef.current) {
      clearTimeout(debouncedFilterRef.current);
    }

    // Set a new timeout
    debouncedFilterRef.current = setTimeout(() => {
      console.debug("Debounced filter change triggered with filters:", filters);

      // Use the current pagination value when the debounced function is called
      const newPagination = updatePaginationWithFilters(
        pagination,
        filters,
        false
      );

      setPagination(newPagination);
      setFilterLoading(true);
      loadData(isAnyFilterReset, newPagination);

      // Clear the timeout reference
      debouncedFilterRef.current = null;
    }, 500);
  };

  return (
    <div className="space-y-4">
      {loading ? (
        <div className="space-y-4">
          <TableSkeleton columns={columns.length} rows={10} />
        </div>
      ) : (
        // Show the table with filters when there's data or filters are applied
        <>
          <BasicTable
            data={data}
            columns={columns}
            itemsPerPageOptions={[5, 10, 20, 50, 100]}
            onPaginationChange={handlePaginationChange}
            serverSidePagination={true}
            contextMenuItems={contextMenuItems}
            onRowClick={onRowClick}
            onResetFilters={() => {
              console.debug("Resetting all filters");
              const resetPagination = { ...pagination, filters: {}, page: 1 };
              setPagination(resetPagination);
              loadData(true, resetPagination);
            }}
            rowsLoading={filterLoading}
            onFilterChange={(filters) => {
              const isAnyFilterReset = Object.entries(filters).some(
                ([key, value]) =>
                  (value === "Όλα" || value === "") &&
                  pagination.filters &&
                  key in pagination.filters
              );
              const isTextFilter = Object.entries(filters).some(
                ([key, value]) =>
                  (key === "form_data.name" ||
                    key === "form_data.surname" ||
                    key === "form_data.father_name" ||
                    key === "form_data.mother_name" ||
                    key === "form_data.address" ||
                    key === "form_data.candidate_search" ||
                    key === "form_data.client_search" ||
                    key === "id") &&
                  typeof value === "string"
              );
              console.info("isTextFilter", isTextFilter);

              // For text filters, use debounced function to prevent excessive API calls
              if (isTextFilter) {
                debouncedLoadData(isAnyFilterReset, filters);
                return;
              }

              // For non-text filters (select, date, etc.), apply immediately
              const newPagination = updatePaginationWithFilters(
                pagination,
                filters,
                false
              );

              setPagination(newPagination);
              setFilterLoading(true);
              loadData(isAnyFilterReset, newPagination);
            }}
          />
        </>
      )}
    </div>
  );
};

export default NannyBlueTable;
