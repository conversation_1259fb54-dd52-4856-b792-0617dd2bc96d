import { Button } from "@/components/ui/button";
import GridBadges from "@/components/ui/GridBadges";
import { Input } from "@/components/ui/input";
import { SearchableCitySelect } from "@/components/ui/SearchableCitySelect";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PaginatedResponse, PaginationParams } from "@/lib/paginationUtils";
import { ArrowUpDown, ChevronLeft, ChevronRight, X } from "lucide-react";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Badge } from "../ui/badge";

export interface ColumnConfig<T> {
  key: keyof T;
  header: string;
  filterable?: boolean;
  filterType?: "text" | "number" | "date" | "select" | "city-select";
  options?: string[]; // for select filters
  render?: (item: T) => React.ReactNode; // custom render function
  sortable?: boolean;
  filterFn?: (row: T, columnId: string, filterValue: string) => boolean; // custom filter function
}

export interface ContextMenuItem {
  label: string;
  icon?: React.ReactNode;
  onClick: (item: any) => void;
  className?: string;
}

interface BasicTableProps<T> {
  data: T[] | PaginatedResponse<T>;
  columns: ColumnConfig<T>[];
  itemsPerPageOptions?: number[];
  onRowClick?: (item: T) => void;
  contextMenuItems?: ContextMenuItem[];
  onActionButtonClick?: (item: T) => void;
  onPaginationChange?: (params: PaginationParams) => void;
  onFilterChange?: (filters: Record<string, any>) => void;
  onResetFilters?: (filters: Record<string, any>) => void;
  serverSidePagination?: boolean;
  clientSideFiltering?: boolean; // Whether to apply filters client-side, defaults to false
  rowsLoading?: boolean; // Loading state for just the rows, not the whole table
  selectedItems?: string[]; // Add selected items array to track selection state
}

// TableRows component to be memoized
interface TableRowsProps<T> {
  data: T[];
  columns: ColumnConfig<T>[];
  onRowClick?: (item: T) => void;
  onActionButtonClick?: (item: T) => void;
  handleContextMenu: (e: React.MouseEvent, item: T) => void;
  handleClickOutside: () => void;
  contextMenuPosition: {
    x: number;
    y: number;
    show: boolean;
    item: T | null;
  };
  selectedItems?: string[]; // Add selected items array to track selection state
}

// TableRows component that renders only the rows
const TableRows = <T extends Record<string, any>>({
  data,
  columns,
  onRowClick,
  onActionButtonClick,
  handleContextMenu,
  handleClickOutside,
  contextMenuPosition,
  selectedItems = [],
}: TableRowsProps<T>) => {
  return (
    <>
      {data.map((item: T, index: number) => (
        <TableRow
          key={`row-${index}-${item.id || index}`}
          className={`
            animate-in fade-in duration-300
            ${
              onRowClick
                ? "group cursor-pointer hover:bg-primary/5 transition-colors duration-200"
                : "group hover:bg-accent/10 transition-colors duration-200"
            }
          `}
          style={{ animationDelay: `${index * 50}ms` }}
          onClick={(e) => {
            if (contextMenuPosition.show) {
              handleClickOutside();
              e.preventDefault();
              return;
            }
            // Call the onRowClick handler if provided
            if (onRowClick) {
              onRowClick(item);
            }
          }}
          onContextMenu={(e) => handleContextMenu(e, item)}
        >
          {columns.map((column) => (
            <TableCell
              key={`cell-${column.key as string}-${item.id || index}`}
              className="transition-colors duration-200 group-hover:text-primary/90 max-w-[200px] overflow-hidden"
            >
              {column.render ? (
                column.render(item)
              ) : Array.isArray(item[column.key]) ? (
                <GridBadges items={item[column.key]} maxItemsPerRow={2} />
              ) : (
                <div className="truncate">{item[column.key]}</div>
              )}
            </TableCell>
          ))}
          {onActionButtonClick && (
            <TableCell className="whitespace-nowrap">
              <Button
                className="h-8 w-24 mt-2 mb-2"
                variant="outline"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onActionButtonClick(item);
                }}
              >
                Reject
              </Button>
            </TableCell>
          )}
        </TableRow>
      ))}
    </>
  );
};

// Memoize the TableRows component to prevent unnecessary re-renders
const MemoizedTableRows = React.memo(TableRows, (prevProps, nextProps) => {
  // Check if selectedItems has changed
  if (prevProps.selectedItems?.length !== nextProps.selectedItems?.length) {
    return false; // Different selection state, so re-render
  }

  // Check if any specific selection has changed
  if (prevProps.selectedItems && nextProps.selectedItems) {
    const prevSelectedSet = new Set(prevProps.selectedItems);
    const nextSelectedSet = new Set(nextProps.selectedItems);

    // Check if any item in prevSelected is not in nextSelected or vice versa
    if (
      prevProps.selectedItems.some((id) => !nextSelectedSet.has(id)) ||
      nextProps.selectedItems.some((id) => !prevSelectedSet.has(id))
    ) {
      return false; // Selection has changed, so re-render
    }
  }

  // Only re-render if the data has actually changed
  // This prevents re-renders when filters are applied but the resulting data is the same
  if (prevProps.data.length !== nextProps.data.length) {
    return false; // Different length means different data, so re-render
  }

  // Deep comparison of data items to detect changes in field values
  // This ensures we re-render when any field in any row changes
  const areEqual = prevProps.data.every((prevItem, index) => {
    const nextItem = nextProps.data[index];
    if (!nextItem) return false;

    // First check IDs
    const prevId = prevItem.id || index;
    const nextId = nextItem.id || index;
    if (prevId !== nextId) return false;

    // Then check for changes in any field
    // Compare JSON stringified versions to detect changes in nested objects
    const prevJson = JSON.stringify(prevItem);
    const nextJson = JSON.stringify(nextItem);
    const fieldsEqual = prevJson === nextJson;

    if (!fieldsEqual) {
      console.debug(`Data changed for item ${prevId}:`, {
        prev: prevItem,
        next: nextItem,
      });
    }

    return fieldsEqual;
  });

  if (!areEqual) {
    console.debug("Table data changed, triggering re-render");
  }

  return areEqual;
}) as typeof TableRows;

const BasicTable = <T extends Record<string, any>>({
  data,
  columns,
  itemsPerPageOptions = [10, 20, 50],
  onRowClick,
  onActionButtonClick,
  contextMenuItems = [],
  onPaginationChange,
  onFilterChange,
  onResetFilters,
  serverSidePagination = false,
  clientSideFiltering = false,
  rowsLoading = false,
  selectedItems = [],
}: BasicTableProps<T>) => {
  // Determine if data is a paginated response
  const isPaginatedResponse = (data: any): data is PaginatedResponse<T> => {
    return "totalCount" in data && "data" in data;
  };

  // Extract the actual data array and pagination info
  const dataArray = isPaginatedResponse(data) ? data.data : data;
  const totalCount = isPaginatedResponse(data)
    ? data.totalCount
    : dataArray.length;
  const serverTotalPages = isPaginatedResponse(data) ? data.totalPages : 1;

  const [currentPage, setCurrentPage] = useState(
    isPaginatedResponse(data) ? data.page : 1
  );
  const [filters, setFilters] = useState<Partial<T>>({});
  const [itemsPerPage, setItemsPerPage] = useState(
    isPaginatedResponse(data) ? data.itemsPerPage : itemsPerPageOptions[0]
  );
  const [sort, setSort] = useState<{
    key: keyof T;
    direction: "asc" | "desc";
  } | null>(null);
  const [contextMenuPosition, setContextMenuPosition] = useState<{
    x: number;
    y: number;
    show: boolean;
    item: T | null;
  }>({
    x: 0,
    y: 0,
    show: false,
    item: null,
  });

  const contextMenuRef = useRef<HTMLDivElement>(null);

  // Update pagination when page or items per page changes
  useEffect(() => {
    if (serverSidePagination && onPaginationChange) {
      // Create a pagination object that preserves existing filters
      onPaginationChange({
        page: currentPage,
        itemsPerPage,
        filters: filters as Record<string, any>,
      });
    }
  }, [
    currentPage,
    itemsPerPage,
    serverSidePagination,
    onPaginationChange,
    filters,
  ]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        contextMenuRef.current &&
        !contextMenuRef.current.contains(event.target as Node) &&
        contextMenuPosition.show
      ) {
        setContextMenuPosition((prev) => ({
          ...prev,
          show: false,
          item: null,
        }));
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [contextMenuPosition.show]);

  // We don't need a debounced filter change function anymore
  // The parent component (NannyBlueTable) will handle debouncing

  // Function to handle filter changes
  const handleFilterChange = (
    key: keyof T,
    value: string,
    filterType?: string
  ) => {
    let newFilters: Partial<T>;

    if (value === "Όλα" || value === "") {
      // When "All" is selected, explicitly remove this filter
      const { [key]: _, ...rest } = filters;
      newFilters = rest as T;
      console.debug(`Removing filter for ${String(key)}, value was "${value}"`);
    } else {
      newFilters = { ...filters, [key]: value };
      console.debug(`Setting filter for ${String(key)} to "${value}"`);
    }

    // Always update local filters state for UI consistency
    setFilters(newFilters);

    // When server-side pagination is enabled or client-side filtering is disabled,
    // and onFilterChange is provided, pass the filter change to the parent component
    if ((serverSidePagination || !clientSideFiltering) && onFilterChange) {
      // Pass the filter type along with the filters so the parent component can decide
      // whether to debounce or not
      onFilterChange(newFilters as Record<string, any>);
    }
  };

  // Memoize the resetFilters function to prevent unnecessary re-renders
  const resetFilters = useCallback(() => {
    // Always clear filters for UI consistency
    setFilters({});

    // When client-side filtering is disabled, we need to ensure server-side filtering works
    // If server-side pagination is enabled or client-side filtering is disabled, and onResetFilters is provided
    if ((serverSidePagination || !clientSideFiltering) && onResetFilters) {
      // Always call the original onResetFilters directly for reset (no debounce)
      onResetFilters({});
    }
  }, [serverSidePagination, clientSideFiltering, onResetFilters]);

  // Memoize filtered data to avoid unnecessary recalculations
  const filteredData = useMemo(() => {
    // When client-side filtering is disabled, always return the original data array
    // regardless of filters - this ensures local filter state doesn't affect displayed rows
    if (!clientSideFiltering) return dataArray;

    // If using server-side pagination, return the data as is (server handles filtering)
    if (serverSidePagination) return dataArray;

    // If there are no filters, return the original data array
    if (Object.keys(filters).length === 0) return dataArray;

    // Create a stable filter function that doesn't change on every render
    const filterItem = (item: T) => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        const itemValue = item[key as keyof T];

        // Find the column config for this key to check if it has a filterFn
        const column = columns.find((col) => col.key === key);

        // If the column has a custom filter function, use it
        if (column && column.filterFn) {
          return column.filterFn(item, key as string, value as string);
        }

        // Handle array values (like position_interests, languages, etc.)
        if (Array.isArray(itemValue)) {
          // For arrays of objects (like languages with language and level properties)
          if (itemValue.length > 0 && typeof itemValue[0] === "object") {
            // Check if any object in the array has a property that includes the filter value
            return itemValue.some((obj: any) => {
              // Check all properties of the object
              return Object.values(obj).some((propValue) =>
                String(propValue)
                  .toLowerCase()
                  .includes(String(value).toLowerCase())
              );
            });
          }
          // For simple arrays of strings
          return itemValue.some((arrValue: any) =>
            String(arrValue).toLowerCase().includes(String(value).toLowerCase())
          );
        }

        // Handle number values
        if (typeof itemValue === "number") {
          return itemValue >= Number(value);
        }

        // Handle date values
        if (key === "startDate") {
          return new Date(itemValue) >= new Date(value as string);
        }

        // Handle string values
        return String(itemValue)
          .toLowerCase()
          .includes(String(value).toLowerCase());
      });
    };

    // Apply the filter function to the data array
    return dataArray.filter(filterItem);
  }, [clientSideFiltering, serverSidePagination, dataArray, filters, columns]);

  // Memoize sorted data to avoid unnecessary sorts
  const sortedData = useMemo(() => {
    // When client-side filtering is disabled, don't apply client-side sorting
    if (!clientSideFiltering) return filteredData;

    // If using server-side pagination or no sort key is provided, return filtered data as is
    if (serverSidePagination || !sort?.key || filteredData.length === 0) {
      return filteredData;
    }

    // Create a stable sort function that doesn't change on every render
    const sortKey = sort.key as keyof T;
    const direction = sort.direction;

    // Create a new array to avoid mutating the original
    const sortedArray = [...filteredData];

    // Sort the array based on the sort key and direction
    sortedArray.sort((a, b) => {
      const sortValueA = a[sortKey];
      const sortValueB = b[sortKey];

      if (typeof sortValueA === "string" && typeof sortValueB === "string") {
        return direction === "asc"
          ? sortValueA.localeCompare(sortValueB)
          : sortValueB.localeCompare(sortValueA);
      } else if (
        typeof sortValueA === "number" &&
        typeof sortValueB === "number"
      ) {
        return direction === "asc"
          ? sortValueA - sortValueB
          : sortValueB - sortValueA;
      } else {
        // If types don't match or are not sortable, maintain original order
        return 0;
      }
    });

    return sortedArray;
  }, [
    filteredData,
    clientSideFiltering,
    serverSidePagination,
    sort?.key,
    sort?.direction,
  ]);

  // Memoize paginated data to avoid unnecessary slicing
  const paginatedData = useMemo(() => {
    // When client-side filtering is disabled, don't apply client-side pagination
    if (!clientSideFiltering) return sortedData;

    // If using server-side pagination, return sorted data as is (server handles pagination)
    if (serverSidePagination) return sortedData;

    // Calculate start and end indices for pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = currentPage * itemsPerPage;

    // Return the sliced data for the current page
    return sortedData.slice(startIndex, endIndex);
  }, [
    sortedData,
    clientSideFiltering,
    serverSidePagination,
    currentPage,
    itemsPerPage,
  ]);

  // Memoize total pages calculation
  const totalPages = useMemo(() => {
    // When client-side filtering is disabled, use server-provided total pages
    if (!clientSideFiltering) return serverTotalPages;

    // If using server-side pagination, use server-provided total pages
    if (serverSidePagination) return serverTotalPages;

    // Otherwise calculate based on filtered/sorted data length
    return Math.ceil(sortedData.length / itemsPerPage);
  }, [
    clientSideFiltering,
    serverSidePagination,
    serverTotalPages,
    sortedData.length,
    itemsPerPage,
  ]);

  // Memoize event handlers to prevent unnecessary re-renders
  const handleSortClick = useCallback(
    (key: keyof T) => {
      if (sort?.key === key) {
        setSort({ key, direction: sort.direction === "asc" ? "desc" : "asc" });
      } else {
        setSort({ key, direction: "asc" });
      }
    },
    [sort]
  );

  const handleContextMenu = useCallback(
    (e: React.MouseEvent, item: T) => {
      if (contextMenuItems.length === 0) return;

      e.preventDefault();
      e.stopPropagation();
      setContextMenuPosition({
        x: e.clientX,
        y: e.clientY,
        show: true,
        item,
      });
    },
    [contextMenuItems.length]
  );

  const handleClickOutside = useCallback(() => {
    setContextMenuPosition((prev) => ({ ...prev, show: false, item: null }));
  }, []);

  // Fixed handleMenuItemClick function
  const handleMenuItemClick = useCallback(
    (e: React.MouseEvent, menuItem: ContextMenuItem) => {
      e.stopPropagation();
      e.preventDefault();

      if (contextMenuPosition.item) {
        // Call the menu item's onClick handler with the current item
        menuItem.onClick(contextMenuPosition.item);
      }

      // Close the context menu
      handleClickOutside();
    },
    [contextMenuPosition.item, handleClickOutside]
  );

  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage(newPage);
  }, []);

  const handleItemsPerPageChange = useCallback((value: string) => {
    const newItemsPerPage = Number(value);
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  }, []);

  // Memoize the reset filters button
  const resetFiltersButton = useMemo(() => {
    if (Object.keys(filters).length === 0) return null;

    return (
      <Button
        variant="outline"
        size="sm"
        onClick={resetFilters}
        className="h-8 hover:scale-105 transition-transform duration-200"
      >
        <X className="h-4 w-4 mr-2" />
        Επαναφορά Φίλτρων
      </Button>
    );
  }, [filters, resetFilters]);

  // Memoize the table header to prevent unnecessary re-renders
  const tableHeader = useMemo(
    () => (
      <TableHeader className="animate-in fade-in-50 duration-500 delay-100">
        <TableRow>
          {columns.map((column) => (
            <TableHead
              key={column.key as string}
              className="whitespace-nowrap py-4 min-w-[120px] max-w-[200px] animate-in fade-in-50 duration-300"
            >
              {column.filterable && (
                <div className="flex items-center">
                  <Badge variant="outline" className="text-xs">
                    {column.header}
                  </Badge>
                  {column.sortable && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleSortClick(column.key)}
                      className="ml-1 h-4 w-4 p-0"
                    >
                      <ArrowUpDown className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              )}
              {column.filterable ? (
                <div className="min-w-[80px] pr-2 mt-2 animate-in fade-in-50 duration-300">
                  {column.filterType === "number" ? (
                    <Input
                      type="number"
                      placeholder={`${column.header}`}
                      value={filters[column.key] || ""}
                      onChange={(e) =>
                        handleFilterChange(column.key, e.target.value, "number")
                      }
                      className="h-8 w-24"
                    />
                  ) : column.filterType === "city-select" ? (
                    <SearchableCitySelect
                      value={filters[column.key] || ""}
                      onValueChange={(value: string) =>
                        handleFilterChange(column.key, value, "select")
                      }
                      placeholder={`${column.header}`}
                      language="el"
                      className="w-full"
                    />
                  ) : column.filterType === "select" && column.options ? (
                    <Select
                      value={filters[column.key] || ""}
                      onValueChange={(value) =>
                        handleFilterChange(column.key, value, "select")
                      }
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue placeholder={`${column.header}`} />
                      </SelectTrigger>
                      <SelectContent>
                        {["Όλα", ...column.options].map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : column.filterType === "date" ? (
                    <Input
                      type="date"
                      value={filters[column.key] || ""}
                      onChange={(e) =>
                        handleFilterChange(column.key, e.target.value, "date")
                      }
                      className="h-8 w-48"
                    />
                  ) : (
                    <Input
                      type="text"
                      placeholder={`${column.header}`}
                      value={filters[column.key] || ""}
                      onChange={(e) =>
                        handleFilterChange(column.key, e.target.value, "text")
                      }
                      className="h-8"
                    />
                  )}
                </div>
              ) : (
                <div
                  className="min-w-[80px] pr-2 mt-2 animate-in fade-in-50 duration-300"
                  style={{ animationDelay: "300ms" }}
                >
                  {column.header}
                </div>
              )}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
    ),
    [columns, filters, handleFilterChange, handleSortClick]
  );

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4 animate-in fade-in duration-500">
        {resetFiltersButton}
      </div>

      <div className="rounded-md border overflow-x-auto overflow-y-hidden animate-in fade-in duration-500">
        <Table className="w-full min-w-max">
          {tableHeader}
          <TableBody>
            {rowsLoading ? (
              // Show a loading indicator for the rows only
              <TableRow>
                <TableCell
                  colSpan={columns.length + (onActionButtonClick ? 1 : 0)}
                  className="h-24 text-center"
                >
                  <div className="flex justify-center items-center h-full">
                    <div className="animate-pulse flex space-x-4">
                      <div className="flex-1 space-y-4 py-1">
                        <div className="h-4 bg-primary/10 rounded w-3/4 mx-auto animate-shimmer"></div>
                        <div className="space-y-2">
                          <div
                            className="h-4 bg-primary/10 rounded animate-shimmer"
                            style={{ animationDelay: "0.2s" }}
                          ></div>
                          <div
                            className="h-4 bg-primary/10 rounded w-5/6 mx-auto animate-shimmer"
                            style={{ animationDelay: "0.4s" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              // Use MemoizedTableRows to prevent unnecessary re-renders
              <MemoizedTableRows
                data={paginatedData}
                columns={columns}
                onRowClick={onRowClick}
                onActionButtonClick={onActionButtonClick}
                handleContextMenu={handleContextMenu}
                handleClickOutside={handleClickOutside}
                contextMenuPosition={contextMenuPosition}
                selectedItems={selectedItems}
              />
            )}
          </TableBody>
        </Table>
      </div>

      {/* Memoize the pagination controls to prevent unnecessary re-renders */}
      {useMemo(
        () => (
          <div className="flex items-center justify-between py-4 animate-in fade-in-50 duration-700 delay-300">
            <div className="text-sm text-muted-foreground flex items-center gap-2 px-2">
              <Select
                value={String(itemsPerPage)}
                onValueChange={handleItemsPerPageChange}
              >
                <SelectTrigger className="w-16 hover:bg-primary/5 transition-colors duration-200">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {itemsPerPageOptions.map((option) => (
                    <SelectItem key={option} value={String(option)}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              από {totalCount} εγγραφές
            </div>
            <div className="flex items-center space-x-2 px-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="hover:scale-105 transition-transform duration-200 hover:bg-primary/5"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                Σελίδα {currentPage} από {totalPages || 1}
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || totalPages === 0}
                className="hover:scale-105 transition-transform duration-200 hover:bg-primary/5"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ),
        [
          itemsPerPage,
          handleItemsPerPageChange,
          itemsPerPageOptions,
          totalCount,
          currentPage,
          handlePageChange,
          totalPages,
        ]
      )}

      {/* Simple inline context menu */}
      {contextMenuItems.length > 0 &&
        contextMenuPosition.show &&
        contextMenuPosition.item && (
          <div
            className="fixed z-[9999]"
            style={{
              left: contextMenuPosition.x,
              top: contextMenuPosition.y,
            }}
            ref={contextMenuRef}
          >
            <div className="rounded-lg border border-border bg-card backdrop-blur-sm shadow-lg p-1.5 w[var(--radix-context-menu-content-width)] animate-in fade-in-50 zoom-in-95 duration-200">
              {contextMenuItems.map((menuItem, index) => (
                <button
                  key={index}
                  className={`flex w-full items-center gap-2 rounded-md px-3 py-2 text-sm transition-colors hover:bg-primary/15 hover:text-primary outline-none ${
                    menuItem.className || ""
                  }`}
                  onClick={(e) => handleMenuItemClick(e, menuItem)}
                >
                  {menuItem.icon && (
                    <span className="mr-2">{menuItem.icon}</span>
                  )}
                  {menuItem.label}
                </button>
              ))}
            </div>
          </div>
        )}
    </div>
  );
};

export default BasicTable;
