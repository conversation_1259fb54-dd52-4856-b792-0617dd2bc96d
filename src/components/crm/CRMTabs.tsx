import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ClientsTable from "./clients/ClientsTable";
import ContactsTable from "./ContactsTable";
import DealsTable from "./DealsTable";

interface CRMTabsProps {
  onTabChange?: (value: string) => void;
}

const CRMTabs = ({ onTabChange }: CRMTabsProps) => {
  return (
    <Tabs
      defaultValue="contacts"
      className="w-full"
      onValueChange={onTabChange}
    >
      <TabsList className="w-full justify-start border-b rounded-none h-12 bg-transparent backdrop-blur-sm">
        <TabsTrigger
          value="contacts"
          className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
        >
          Contacts
        </TabsTrigger>
        <TabsTrigger
          value="clients"
          className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
        >
          Clients
        </TabsTrigger>
      </TabsList>
      <TabsContent value="contacts" className="p-0 border-none">
        <ContactsTable />
      </TabsContent>
      <TabsContent value="deals" className="p-0 border-none">
        <DealsTable />
      </TabsContent>
      <TabsContent value="clients" className="p-0 border-none">
        <ClientsTable />
      </TabsContent>
    </Tabs>
  );
};

export default CRMTabs;
