import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SearchableCitySelectInline } from "@/components/ui/SearchableCitySelectInline";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { CLIENT_STATUS, isClientCandidate } from "@/lib/clientUtils";
import { positionOptions, startDateOptions } from "@/schemas/FormSchema";
import { updateClient } from "@/services/clientService";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { Checkbox } from "../ui/checkbox";
import { Client, clientStatuses } from "./clients/ClientsTable";

interface ContactUpdateDialogProps {
  contact: Client | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  updateField: string;
}

const ContactUpdateDialog = ({
  contact,
  isOpen,
  onClose,
  onSuccess,
  updateField,
}: ContactUpdateDialogProps) => {
  const { toast } = useToast();
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<any>({});

  // Reset form data when contact changes
  useState(() => {
    if (contact) {
      setFormData({});
    }
  });

  const handleSave = async () => {
    if (!contact) return;

    setSaving(true);
    let updateSuccessful = false;

    try {
      // Create an update object with only the changed fields
      const updates: Partial<Client> = {};

      // Handle different field types
      if (updateField === "status") {
        updates.status = parseInt(formData.status);
      } else if (updateField === "form_data") {
        // Update the entire form_data object
        updates.form_data = {
          ...contact.form_data,
          ...formData,
        };
      } else if (updateField.startsWith("form_data.")) {
        // Update a specific field in form_data
        const fieldName = updateField.replace("form_data.", "");
        updates.form_data = {
          ...contact.form_data,
          [fieldName]: formData[fieldName],
        };
      }

      const result = await updateClient(contact.id.toString(), updates);

      if (result) {
        updateSuccessful = true;
        toast({
          title: "Επιτυχία",
          description: "Η επαφή ενημερώθηκε επιτυχώς.",
        });

        // Blur any focused elements before closing
        if (
          document.activeElement instanceof HTMLElement &&
          document.activeElement.closest('[role="dialog"]')
        ) {
          document.activeElement.blur();
        }

        // Log the updated data for debugging
        console.debug("Contact updated successfully:", result);

        // Close the dialog first
        onClose();

        // Then trigger the reload with a small delay to ensure the dialog is fully closed
        setTimeout(() => {
          console.debug("Triggering reload after update");
          onSuccess();
        }, 50);
      } else {
        throw new Error("Failed to update contact");
      }
    } catch (error) {
      console.error("Error updating contact:", error);
      toast({
        title: "Σφάλμα",
        description: "Παρουσιάστηκε σφάλμα κατά την ενημέρωση της επαφής.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
      // Only close in case of error, success case already closes
      if (!updateSuccessful) {
        onClose();
      }
    }
  };

  const renderFormField = () => {
    if (!contact) return null;

    switch (updateField) {
      case "status":
        // Determine if this is a contact (Lead/Rejected) or a client (Past Client/Active Client)
        const isContact = isClientCandidate(contact?.status);

        // Filter status options based on whether it's a contact or client
        const filteredStatuses = clientStatuses.filter((status) => {
          if (isContact) {
            // For contacts, only show Lead and Rejected options
            return (
              status.id === CLIENT_STATUS.LEAD ||
              status.id === CLIENT_STATUS.REJECTED
            );
          } else {
            // For clients, only show Past Client and Active Client options
            return (
              status.id === CLIENT_STATUS.PAST_CLIENT ||
              status.id === CLIENT_STATUS.ACTIVE_CLIENT
            );
          }
        });

        return (
          <div className="space-y-4">
            <Label htmlFor="status">Κατάσταση</Label>
            <Select
              value={formData.status?.toString() || contact.status?.toString()}
              onValueChange={(value) => setFormData({ status: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Επιλέξτε κατάσταση" />
              </SelectTrigger>
              <SelectContent>
                {filteredStatuses.map((status) => (
                  <SelectItem key={status.id} value={status.id.toString()}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case "form_data.father_name":
        return (
          <div className="space-y-4">
            <Label htmlFor="father_name">Όνομα Πατέρα</Label>
            <Input
              id="father_name"
              defaultValue={contact.form_data?.father_name || ""}
              onChange={(e) => setFormData({ father_name: e.target.value })}
            />
          </div>
        );

      case "form_data.mother_name":
        return (
          <div className="space-y-4">
            <Label htmlFor="mother_name">Όνομα Μητέρας</Label>
            <Input
              id="mother_name"
              defaultValue={contact.form_data?.mother_name || ""}
              onChange={(e) => setFormData({ mother_name: e.target.value })}
            />
          </div>
        );

      case "form_data.position_type":
        return (
          <div className="space-y-4">
            <Label htmlFor="position_type">Είδος</Label>
            <Select
              value={formData.position_type || contact.form_data?.position_type}
              onValueChange={(value) => setFormData({ position_type: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Επιλέξτε είδος" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="live-in">Live-In</SelectItem>
                <SelectItem value="live-out">Live-Out</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case "form_data.schedule_type":
        return (
          <div className="space-y-4">
            <Label htmlFor="schedule_type">Πρόγραμμα</Label>
            <Select
              value={formData.schedule_type || contact.form_data?.schedule_type}
              onValueChange={(value) => setFormData({ schedule_type: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Επιλέξτε πρόγραμμα" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="full-time">Full-Time</SelectItem>
                <SelectItem value="part-time">Part-Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      case "form_data.position_interests":
        return (
          <div className="space-y-4">
            <Label htmlFor="position_interests">Θέση</Label>
            <div className="grid grid-cols-3 gap-3 mt-2">
              {positionOptions.map((position) => (
                <div
                  key={position.id}
                  className="flex items-center space-x-2 bg-muted/30 p-2 rounded-md"
                >
                  <Checkbox
                    id={position.id}
                    checked={
                      formData.position_interests
                        ? formData.position_interests.includes(position.id)
                        : contact.form_data?.position_interests?.includes(
                            position.id
                          )
                    }
                    onCheckedChange={(checked) => {
                      const currentPositions =
                        formData.position_interests ||
                        contact.form_data?.position_interests ||
                        [];

                      let newPositions: string[];
                      if (checked) {
                        newPositions = [...currentPositions, position.id];
                      } else {
                        newPositions = currentPositions.filter(
                          (p: string) => p !== position.id
                        );
                      }

                      setFormData({ position_interests: newPositions });
                    }}
                  />
                  <Label htmlFor={position.id} className="text-sm">
                    {position.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        );

      case "form_data.start_date":
        return (
          <div className="space-y-2">
            <Label htmlFor="start_date">Έναρξη</Label>
            <Select
              value={formData.start_date || contact.form_data?.start_date}
              onValueChange={(value) => setFormData({ start_date: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Επιλέξτε ημερομηνία έναρξης" />
              </SelectTrigger>
              <SelectContent>
                {startDateOptions.map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    {option.labelEl}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case "form_data.city":
        return (
          <div className="w-full h-full flex flex-col space-y-4">
            <Label htmlFor="city" className="text-lg font-medium">
              Τοποθεσία
            </Label>
            <SearchableCitySelectInline
              value={formData.city || contact.form_data?.city || ""}
              onValueChange={(value: string) => setFormData({ city: value })}
              dropdownHeight="400px" // Adjusted height for better visibility
              className="w-full flex-grow"
            />
          </div>
        );

      default:
        return <div>Δεν υπάρχει φόρμα για αυτό το πεδίο.</div>;
    }
  };

  // Ensure proper cleanup when dialog closes
  const handleDialogChange = (open: boolean) => {
    if (!open) {
      // Ensure we blur any focused elements before closing
      if (
        document.activeElement instanceof HTMLElement &&
        document.activeElement.closest('[role="dialog"]')
      ) {
        document.activeElement.blur();
      }

      // Small delay before closing to ensure blur takes effect
      setTimeout(() => {
        onClose();
      }, 10);
    }
  };

  // Determine if we need a larger dialog for city field
  const isCityField = updateField === "form_data.city";

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogChange}>
      <DialogContent
        className={`${
          isCityField
            ? "w-[90vw] sm:max-w-[700px] md:max-w-[800px] lg:max-w-[900px] min-h-[700px] flex flex-col"
            : "sm:max-w-[600px] md:max-w-[700px]"
        } z-50 max-h-[90vh] p-0 overflow-hidden`}
        // Ensure dialog doesn't trap focus when closed
        onCloseAutoFocus={(e) => {
          e.preventDefault();
          // Return focus to the document body
          document.body.focus();
        }}
        // Prevent interaction outside the dialog when it's open
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
        // Ensure proper focus management
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          // Blur any focused elements inside the dialog
          if (
            document.activeElement instanceof HTMLElement &&
            document.activeElement.closest('[role="dialog"]')
          ) {
            document.activeElement.blur();
          }
          // Small delay before closing to ensure blur takes effect
          setTimeout(() => onClose(), 10);
        }}
      >
        <div className="flex flex-col h-full w-full">
          {/* Fixed Header */}
          <div className="p-6 border-b">
            <DialogHeader className="p-0">
              <DialogTitle>Ενημέρωση Επαφής</DialogTitle>
              <DialogDescription>
                Ενημερώστε τα στοιχεία της επαφής.
              </DialogDescription>
            </DialogHeader>
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-6">
            <div
              className={`${
                isCityField ? "min-h-[400px]" : ""
              } flex flex-col justify-start items-start w-full`}
            >
              {renderFormField()}
            </div>
          </div>

          {/* Fixed Footer */}
          <div className="p-6 border-t mt-auto">
            <div className="flex flex-row justify-start items-center gap-2 w-full">
              <Button
                variant="outline"
                onClick={() => {
                  // Blur any focused elements before closing
                  if (
                    document.activeElement instanceof HTMLElement &&
                    document.activeElement.closest('[role="dialog"]')
                  ) {
                    document.activeElement.blur();
                  }
                  // Small delay before closing to ensure blur takes effect
                  setTimeout(() => onClose(), 10);
                }}
              >
                Ακύρωση
              </Button>
              <Button
                onClick={(e) => {
                  // Prevent default to avoid focus issues
                  e.preventDefault();
                  // Blur the button before saving
                  if (document.activeElement instanceof HTMLElement) {
                    document.activeElement.blur();
                  }
                  // Small delay before saving to ensure blur takes effect
                  setTimeout(() => handleSave(), 10);
                }}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Αποθήκευση...
                  </>
                ) : (
                  "Αποθήκευση"
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContactUpdateDialog;
