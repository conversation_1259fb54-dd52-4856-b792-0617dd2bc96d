import { Badge } from "@/components/ui/badge";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import {
  getClientStatusLabel,
  getClientStatusVariant,
} from "@/lib/clientUtils";
import {
  getCityLabel,
  getLanguageLabel,
  getLanguageOptions,
  getPositionLabel,
  getPositionOptions,
} from "@/lib/tableUtils";
import { startDateOptions } from "@/schemas/FormSchema";
import { deleteClient, fetchContacts } from "@/services/clientService";
import { Edit2, Handshake, Trash2, User } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import NannyBlueTable from "../common/NannyBlueTable";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertD<PERSON>ogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import { toast } from "../ui/use-toast";
import { ColumnConfig, ContextMenuItem } from "./BasicTable";
import { Client, clientStatuses } from "./clients/ClientsTable";
import ContactUpdateDialog from "./ContactUpdateDialog";
import CreateDealSheet from "./forms/CreateDealSheet";

const ContactsTable = () => {
  const navigate = useNavigate();
  const { canEdit, canCreate, canDelete, hasSpecificPermission } =
    usePermissionCheck();

  // Check specific permissions for CRM
  const canEditCRM = canEdit("crm");
  const canDeleteCRM = canDelete("crm");
  const canCreateDeals = canCreate("deals");

  const [selectedContact, setSelectedContact] = useState<Client | null>(null);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [updateField, setUpdateField] = useState<string>("");
  const [shouldReload, setShouldReload] = useState(false);
  const [isDealDialogOpen, setIsDealDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState<Client | null>(null);
  const handleDeleteContact = (contact: Client) => {
    // Check if user has permission to delete contacts
    if (!canDeleteCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα διαγραφής επαφών.",
        variant: "destructive",
      });
      return;
    }

    setContactToDelete(contact);
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion
  const confirmDeleteContact = async () => {
    if (!contactToDelete) return;

    // Double-check permissions before proceeding with deletion
    if (!canDeleteCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα διαγραφής επαφών.",
        variant: "destructive",
      });
      setIsDeleteDialogOpen(false);
      setContactToDelete(null);
      return;
    }

    try {
      const success = await deleteClient(contactToDelete.id);

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Η επαφή "${contactToDelete.form_data?.father_name} & ${contactToDelete.form_data?.mother_name}" διαγράφηκε επιτυχώς.`,
          variant: "default",
        });

        // Trigger a reload of the table
        setShouldReload((prev) => !prev);
      } else {
        throw new Error("Failed to delete contact");
      }
    } catch (error) {
      console.error("Error deleting contact:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία διαγραφής της επαφής.",
        variant: "destructive",
      });
    } finally {
      // Close the dialog and reset the contact to delete
      setIsDeleteDialogOpen(false);
      setContactToDelete(null);
    }
  };
  // Define base context menu items (view items always available)
  let contextMenuItems: ContextMenuItem[] = [
    {
      label: "Προβολή Επαφής",
      icon: <User className="h-4 w-4" />,
      onClick: (item: Client) => {
        navigate(`/crm/contact/${item.id}`);
      },
    },
  ];

  // Add deal creation option if user has permission
  if (canCreateDeals) {
    contextMenuItems.push({
      label: "Δημιουργία Deal",
      icon: <Handshake className="h-4 w-4" />,
      onClick: (item: Client) => {
        setSelectedContact(item);
        setIsDealDialogOpen(true);
      },
    });
  }

  // Add edit options if user has permission
  if (canEditCRM) {
    contextMenuItems = [
      ...contextMenuItems,
      {
        label: "Ενημέρωση Κατάστασης",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "status");
        },
      },
      {
        label: "Ενημέρωση Ονόματος Πατέρα",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.father_name");
        },
      },
      {
        label: "Ενημέρωση Ονόματος Μητέρας",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.mother_name");
        },
      },
      {
        label: "Ενημέρωση Είδους",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.position_type");
        },
      },
      {
        label: "Ενημέρωση Προγράμματος",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.schedule_type");
        },
      },
      {
        label: "Ενημέρωση Θέσης",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.position_interests");
        },
      },
      {
        label: "Ενημέρωση Έναρξης",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.start_date");
        },
      },
      {
        label: "Ενημέρωση Τοποθεσίας",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.city");
        },
      },
    ];
  }

  // Add delete option if user has permission
  if (canDeleteCRM) {
    contextMenuItems.push({
      label: "Διαγραφή Επαφής",
      icon: <Trash2 className="h-4 w-4" />,
      onClick: handleDeleteContact,
      className: "text-destructive hover:bg-destructive/10",
    });
  }

  // Define columns for the table
  const columns: ColumnConfig<Client | any>[] = [
    {
      key: "id",
      header: "ID",
      filterable: true,
      filterType: "text",
      sortable: true,
      render: (item: Client) => `CON-${item.id}`,
    },
    {
      key: "status",
      header: "Κατάσταση",
      filterable: true,
      filterType: "select",
      options: clientStatuses.map((status) => status.label),
      render: (item: Client) => (
        <Badge variant={getClientStatusVariant(item.status)}>
          {getClientStatusLabel(item.status)}
        </Badge>
      ),
    },
    {
      key: "form_data.father_name",
      header: "Όνομα Πατέρα",
      filterable: true,
      filterType: "text",
      render: (item) => item.form_data?.father_name || "-",
    },
    {
      key: "form_data.mother_name",
      header: "Όνομα Μητέρας",
      filterable: true,
      filterType: "text",
      render: (item) => item.form_data?.mother_name || "-",
    },

    {
      key: "form_data.position_type",
      header: "Είδος",
      filterable: true,
      filterType: "select",
      options: ["Live-In", "Live-Out"],
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          <Badge variant="outline">
            {item.form_data?.position_type
              ?.split("-")
              .map((word) => word[0].toUpperCase() + word.slice(1))
              .join("-")}
          </Badge>
        </div>
      ),
    },
    {
      key: "form_data.schedule_type",
      header: "Πρόγραμμα",
      filterable: true,
      filterType: "select",
      options: ["Full-Time", "Part-Time"],
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          <Badge variant="outline">
            {item.form_data?.schedule_type
              ?.split("-")
              .map((word) => word[0].toUpperCase() + word.slice(1))
              .join("-")}
          </Badge>
        </div>
      ),
    },
    {
      key: "form_data.position_interests",
      header: "Θέση",
      filterable: true,
      filterType: "select",
      options: getPositionOptions(),
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          {item.form_data?.position_interests.map((position) => (
            <Badge key={position} variant="outline">
              {getPositionLabel(position)}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: "form_data.languages",
      header: "Γλώσσα",
      filterable: true,
      filterType: "select",
      options: getLanguageOptions(),
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          {item.form_data?.languages.map((lang) => (
            <Badge key={lang.language} variant="outline">
              {getLanguageLabel(lang.language)}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: "form_data.start_date",
      header: "Έναρξη",
      filterable: true,
      filterType: "select",
      options: startDateOptions.map((option) => option.labelEl),
      render: (item: Client) => {
        const startDateOption = startDateOptions.find(
          (option) => option.id === item.form_data?.start_date
        );
        return (
          <div className="flex flex-wrap gap-1">
            {startDateOption ? startDateOption.labelEl : "Δεν δηλώθηκε"}
          </div>
        );
      },
    },

    {
      key: "form_data.city",
      header: "Τοποθεσία",
      filterable: true,
      filterType: "city-select",
      render: (item: Client) => getCityLabel(item.form_data?.city),
    },
  ];

  const handleUpdateSuccess = () => {
    // Force reload by toggling the flag
    setShouldReload(true);

    // Reset the flag after a short delay to prevent multiple reloads
    // but long enough to ensure the reload happens
    setTimeout(() => {
      setShouldReload(false);
    }, 500);
  };

  // Handle dialog close with proper focus management
  const handleDialogClose = () => {
    // Ensure we blur any focused elements before closing, but only if not in a popover
    if (
      document.activeElement instanceof HTMLElement &&
      !document.activeElement.closest("[data-radix-popper-content-wrapper]")
    ) {
      document.activeElement.blur();
    }

    // Reset state
    setIsUpdateDialogOpen(false);
    setUpdateField("");

    // Return focus to the document body after a short delay
    setTimeout(() => {
      document.body.focus();
    }, 10);
  };

  // Handle deal dialog close
  const handleDealDialogClose = (open: boolean) => {
    setIsDealDialogOpen(open);
    if (!open) {
      // Reset selected contact when dialog closes
      setTimeout(() => {
        setSelectedContact(null);
      }, 100);
    }
  };

  // Helper function to open dialog with proper focus management
  const openUpdateDialog = (contact: Client, field: string) => {
    // Check if user has permission to edit contacts
    if (!canEditCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα επεξεργασίας επαφών.",
        variant: "destructive",
      });
      return;
    }

    // Then set up and open the dialog
    setSelectedContact(contact);
    setUpdateField(field);
    setIsUpdateDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <>
        <NannyBlueTable
          fetchData={fetchContacts}
          columns={columns}
          onRowClick={(row) => navigate(`/crm/contact/${row.id}`)}
          contextMenuItems={contextMenuItems}
          shouldReload={shouldReload}
        />

        <ContactUpdateDialog
          contact={selectedContact}
          isOpen={isUpdateDialogOpen}
          onClose={handleDialogClose}
          onSuccess={handleUpdateSuccess}
          updateField={updateField}
        />

        {/* Create Deal Sheet */}
        {selectedContact && (
          <CreateDealSheet
            open={isDealDialogOpen}
            onOpenChange={handleDealDialogClose}
            clientId={selectedContact.id.toString()}
            onSuccess={handleUpdateSuccess}
          />
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Διαγραφή Επαφής</AlertDialogTitle>
              <AlertDialogDescription>
                Είστε βέβαιοι ότι θέλετε να διαγράψετε την επαφή "
                {selectedContact?.form_data?.father_name} &
                {selectedContact?.form_data?.mother_name}" ? Αυτή η ενέργεια δεν
                μπορεί να αναιρεθεί.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Άκυρο</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeleteContact}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Διαγραφή
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    </div>
  );
};

export default ContactsTable;
