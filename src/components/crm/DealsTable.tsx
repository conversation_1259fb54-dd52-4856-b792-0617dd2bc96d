import { useToast } from "@/components/ui/use-toast";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { getFileDownloadUrl, getFilePreviewUrl } from "@/lib/utils";
import {
  Deal,
  DealStatus,
  deleteDeal,
  fetchDeals,
} from "@/services/dealService";
import { Download, Eye, FileText, Trash2, User, Users, X } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import NannyBlueTable from "../common/NannyBlueTable";
import CompactTimeline from "../deals/CompactTimeline";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import { Badge } from "../ui/badge";
import { But<PERSON> } from "../ui/button";
import { ColumnConfig, ContextMenuItem } from "./BasicTable";
import CreateDealSheet from "./forms/CreateDealSheet";

const formatDate = (dateStr: string) => {
  if (!dateStr) return "";
  // Add T00:00:00 to ensure the date is interpreted correctly in local timezone
  const date = new Date(
    dateStr.includes("T") ? dateStr : dateStr + "T00:00:00"
  );
  return date.toLocaleDateString("el-GR", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

interface DealsTableProps {
  shouldReload?: boolean;
}

const DealsTable = ({}: DealsTableProps = {}) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [dealToDelete, setDealToDelete] = useState<Deal | null>(null);
  const { canDelete } = usePermissionCheck();
  const [isDealDialogOpen, setIsDealDialogOpen] = useState(false);
  const [shouldReload, setShouldReload] = useState(false);

  // Handle file preview
  const handleFilePreview = async (filePath: string) => {
    try {
      if (!filePath) {
        toast({
          title: "Σφάλμα",
          description: "Δεν υπάρχει αρχείο για προβολή.",
          variant: "destructive",
        });
        return;
      }

      // If it's a path and not a full URL, get a signed URL
      if (!filePath.startsWith("http")) {
        // Use a longer expiration time for preview (4 hours)
        const expiresIn = 14400; // 4 hours in seconds

        // Use our utility function to get a properly formatted URL
        const previewUrl = await getFilePreviewUrl(
          "deal-documents",
          filePath,
          expiresIn
        );

        // Open the URL in a new tab
        window.open(previewUrl, "_blank");

        toast({
          title: "Προβολή Αρχείου",
          description: "Το αρχείο άνοιξε σε νέα καρτέλα.",
        });
      } else {
        // If it's already a URL, just open it
        window.open(filePath, "_blank");
      }
    } catch (error) {
      console.error("Error previewing file:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία προβολής αρχείου. Ο σύνδεσμος μπορεί να έχει λήξει - παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  // Handle file download
  const handleFileDownload = async (filePath: string) => {
    try {
      if (!filePath) {
        toast({
          title: "Σφάλμα",
          description: "Δεν υπάρχει αρχείο για λήψη.",
          variant: "destructive",
        });
        return;
      }

      let downloadUrl = filePath;

      // If it's a path and not a full URL, get a signed URL
      if (!filePath.startsWith("http")) {
        // Use a longer expiration time for downloads (24 hours)
        downloadUrl = await getFileDownloadUrl("deal-documents", filePath);
      }

      // Create a temporary link element to trigger the download
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filePath.split("/").pop() || "download";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Λήψη Αρχείου",
        description: "Η λήψη του αρχείου ξεκίνησε.",
      });
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία λήψης αρχείου. Ο σύνδεσμος μπορεί να έχει λήξει - παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  // Handle deal deletion
  const handleDeleteDeal = async (deal: Deal) => {
    setDealToDelete(deal);
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion
  const confirmDeleteDeal = async () => {
    if (!dealToDelete) return;

    try {
      const success = await deleteDeal(dealToDelete.id);

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Το deal "${dealToDelete.name}" διαγράφηκε επιτυχώς.`,
          variant: "default",
        });

        // Trigger a reload of the table
        setShouldReload((prev) => !prev);
      } else {
        throw new Error("Failed to delete deal");
      }
    } catch (error) {
      console.error("Error deleting deal:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία διαγραφής του deal.",
        variant: "destructive",
      });
    } finally {
      // Close the dialog and reset the deal to delete
      setIsDeleteDialogOpen(false);
      setDealToDelete(null);
    }
  };

  // Context menu items
  let contextMenuItems: ContextMenuItem[] = [
    {
      label: "Προφίλ Πελάτη",
      icon: <Users className="h-4 w-4" />,
      onClick: (item: Deal) => {
        navigate(`/crm/client/${item.client_id}`);
      },
    },
    {
      label: "Προφίλ Υποψηφίου",
      icon: <User className="h-4 w-4" />,
      onClick: (item: Deal) => {
        navigate(`/candidates/${item.candidate_id}`);
      },
    },
    {
      label: "Επεξεργασία Deal",
      icon: <FileText className="h-4 w-4" />,
      onClick: (item: Deal) => {
        navigate(`/deals/${item.id}`);
      },
    },
    {
      label: "Προβολή Αρχείου",
      icon: <Eye className="h-4 w-4" />,
      onClick: (item: Deal) => {
        if (item.contract_url) {
          handleFilePreview(item.contract_url);
        } else {
          toast({
            title: "Σφάλμα",
            description: "Δεν υπάρχει αρχείο για προβολή.",
            variant: "destructive",
          });
        }
      },
    },
    {
      label: "Λήψη Αρχείου",
      icon: <Download className="h-4 w-4" />,
      onClick: (item: Deal) => {
        if (item.contract_url) {
          handleFileDownload(item.contract_url);
        } else {
          toast({
            title: "Σφάλμα",
            description: "Δεν υπάρχει αρχείο για λήψη.",
            variant: "destructive",
          });
        }
      },
    },
  ];

  // Add delete option only if user has delete permission
  if (canDelete("deals")) {
    contextMenuItems.push({
      label: "Διαγραφή Deal",
      icon: <Trash2 className="h-4 w-4" />,
      onClick: handleDeleteDeal,
      className: "text-destructive hover:bg-destructive/10",
    });
  }

  const columns: ColumnConfig<Deal>[] = [
    {
      key: "name",
      header: "Deal",
      render: (item) => (
        <div className="flex flex-row space-x-2 items-center">
          <Badge
            variant={
              item.status === DealStatus.Active
                ? "success"
                : item.status === DealStatus.Inactive
                ? "secondary"
                : item.status === DealStatus.Rejected
                ? "destructive"
                : "outline"
            }
            className="mt-1"
          >
            {item.name}
          </Badge>
        </div>
      ),
      filterable: true,
      filterType: "text",
    },
    {
      key: "status",
      header: "Status",
      render: (item) => (
        <Badge
          variant={
            item.status === DealStatus.Active
              ? "success"
              : item.status === DealStatus.Inactive
              ? "secondary"
              : item.status === DealStatus.Rejected
              ? "destructive"
              : "outline"
          }
        >
          {item.status}
        </Badge>
      ),
      filterable: true,
      filterType: "select",
      options: Object.values(DealStatus),
    },

    {
      key: "deal_date",
      header: "Έναρξη",
      render: (item) => <p>{formatDate(item.deal_date)}</p>,
      filterable: true,
      filterType: "date",
    },
    {
      key: "revenue",
      header: "Revenue",
      render: (item) => <p>{item.revenue} €</p>,
      filterable: true,
      filterType: "number",
    },
    {
      key: "candidate_salary",
      header: "Nanny Fee",
      render: (item) => <p>{item.candidate_salary} €</p>,
      filterable: true,
      filterType: "number",
    },
    {
      key: "history",
      header: "Πρόοδος",
      render: (item) => (
        <div className="w-[200px] px-1">
          <CompactTimeline history={item.history} />
        </div>
      ),
    },
    {
      key: "client_father_name",
      header: "Client",
      render: (item) => (
        <p>
          {item.client_father_name && item.client_mother_name
            ? `${item.client_father_name} & ${item.client_mother_name}`
            : item.client_father_name ||
              item.client_mother_name ||
              `Client #${item.client_id}`}
        </p>
      ),
    },
    {
      key: "candidate_name",
      header: "Nanny",
      render: (item) => (
        <p>
          {item.candidate_name && item.candidate_surname
            ? `${item.candidate_name} ${item.candidate_surname}`
            : item.candidate_name ||
              item.candidate_surname ||
              `Nanny #${item.candidate_id}`}
        </p>
      ),
    },
    {
      key: "contract_url",
      header: "Files",
      render: (item) => {
        if (item.contract_url) {
          return (
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleFilePreview(item.contract_url);
              }}
              className="text-primary hover:underline"
            >
              <Eye size={16} className="inline mr-1" />
              Προβολή
            </a>
          );
        } else {
          return (
            <p>
              <X size={16} />
            </p>
          );
        }
      },
    },
  ];
  // Function to handle successful updates
  const handleUpdateSuccess = () => {
    setShouldReload((prev) => !prev);
  };
  // Function to handle dialog close
  const handleDialogClose = () => {
    setIsDealDialogOpen(false);
  };
  return (
    <div className="space-y-4">
      <>
        {/* Button to create a new deal  aligned to the right */}
        <Button
          className="flex mb-4 w-full md:w-auto ml-auto justify-end"
          onClick={() => {
            setIsDealDialogOpen(true);
          }}
        >
          Δημιουργία Νέου Deal
        </Button>
        <NannyBlueTable
          fetchData={fetchDeals}
          columns={columns}
          onRowClick={(row) => {
            navigate(`/deals/${row.id}`);
          }}
          contextMenuItems={contextMenuItems}
          shouldReload={shouldReload}
        />

        {
          <CreateDealSheet
            open={isDealDialogOpen}
            onOpenChange={handleDialogClose}
            onSuccess={handleUpdateSuccess}
          />
        }
        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Διαγραφή Deal</AlertDialogTitle>
              <AlertDialogDescription>
                Είστε βέβαιοι ότι θέλετε να διαγράψετε το deal "
                {dealToDelete?.name}"? Αυτή η ενέργεια δεν μπορεί να αναιρεθεί.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Άκυρο</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeleteDeal}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Διαγραφή
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    </div>
  );
};

export default DealsTable;
