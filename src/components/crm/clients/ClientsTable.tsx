import NannyBlueTable from "@/components/common/NannyBlueTable";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import {
  getClientStatusLabel,
  getClientStatusVariant,
} from "@/lib/clientUtils";
import {
  getChildrenAgeOptions,
  getCityLabel,
  getDurationInterestsOptions,
  getLanguageLabel,
  getLanguageOptions,
  getPositionLabel,
  getPositionOptions,
} from "@/lib/tableUtils";
import {
  childrenAgeOptions,
  durationOptions,
  startDateOptions,
} from "@/schemas/FormSchema";
import { deleteClient, fetchClients } from "@/services/clientService";
import {
  Edit,
  Edit2,
  ExternalLink,
  Handshake,
  Trash2,
  User,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ColumnConfig, ContextMenuItem } from "../BasicTable";
import ContactUpdateDialog from "../ContactUpdateDialog";
import CreateDealSheet from "../forms/CreateDealSheet";

// Raw client data structure from Supabase
export interface Client {
  id: string;
  status?: number;
  form_data?: ClientFormData;
  created_at?: string;
  updated_at?: string;
  storage_id?: string;
  total_revenue?: number;
}
export const clientStatuses = [
  { id: 1, label: "Lead" },
  { id: 2, label: "Rejected" },
  { id: 3, label: "Past Client" },
  { id: 4, label: "Active Client" },
];

// Form data structure inside the form_data JSON field
export interface ClientFormData {
  email?: string;
  smoker?: string;
  vehicle?: string;
  first_aid?: string;
  insurance?: string;
  languages?: { language: string; level: string }[];
  start_date?: string;
  father_name?: string;
  mother_name?: string;
  nationality?: string;
  salary_type?: string;
  candidate_age?: string;
  days_per_week?: string;
  hours_per_day?: string;
  position_type?: string;
  salary_amount?: string;
  schedule_type?: string;
  special_needs?: string;
  working_level?: string;
  children_count?: string;
  contact_number?: string;
  insurance_type?: string;
  driving_license?: string;
  salary_currency?: string;
  work_experience?: any[];
  working_address?: string;
  permanent_address?: string;
  position_duration?: string;
  additional_details?: string;
  position_interests?: string[];
  references_contacts?: any[];
  learning_difficulties?: string;
  specialization_preferences?: string[];
  document_upload?: string;
  working_address_same_as_permanent?: boolean;
  children_age?: string[];
  city?: string;
  country?: string;
}

const ClientsTable = () => {
  const navigate = useNavigate();
  const { canEdit, canCreate, canDelete } = usePermissionCheck();

  // Check specific permissions for CRM
  const canEditCRM = canEdit("crm");
  const canDeleteCRM = canDelete("crm");
  const canCreateDeals = canCreate("deals");

  // State for update dialog
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [updateField, setUpdateField] = useState<string>("");
  const [isDealDialogOpen, setIsDealDialogOpen] = useState(false);
  const [shouldReload, setShouldReload] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<Client | null>(null);
  const handleDeleteClient = (contact: Client) => {
    // Check if user has permission to delete clients
    if (!canDeleteCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα διαγραφής πελατών.",
        variant: "destructive",
      });
      return;
    }

    setClientToDelete(contact);
    setIsDeleteDialogOpen(true);
  };
  // Function to open the update dialog
  const openUpdateDialog = (client: Client, field: string) => {
    // Check if user has permission to edit clients
    if (!canEditCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα επεξεργασίας πελατών.",
        variant: "destructive",
      });
      return;
    }

    setSelectedClient(client);
    setUpdateField(field);
    setIsUpdateDialogOpen(true);
  };

  // Function to handle dialog close
  const handleDialogClose = () => {
    setIsUpdateDialogOpen(false);
    setSelectedClient(null);
  };

  // Function to handle successful updates
  const handleUpdateSuccess = () => {
    setShouldReload((prev) => !prev);
  };

  // Confirm deletion
  const confirmDeleteClient = async () => {
    if (!clientToDelete) return;

    // Double-check permissions before proceeding with deletion
    if (!canDeleteCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα διαγραφής πελατών.",
        variant: "destructive",
      });
      setIsDeleteDialogOpen(false);
      setClientToDelete(null);
      return;
    }

    try {
      const success = await deleteClient(clientToDelete.id);

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Ο πελάτης "${clientToDelete.form_data?.father_name} & ${clientToDelete.form_data?.mother_name}" διαγράφηκε επιτυχώς.`,
          variant: "default",
        });

        // Trigger a reload of the table
        setShouldReload((prev) => !prev);
      } else {
        throw new Error("Failed to delete client");
      }
    } catch (error) {
      console.error("Error deleting client:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία διαγραφής του πελάτη. Διεγράψτε τα deals που ανήκουν στον πελάτη και δοκιμάστε ξανά. ",
        variant: "destructive",
      });
    } finally {
      // Close the dialog and reset the client to delete
      setIsDeleteDialogOpen(false);
      setClientToDelete(null);
    }
  };

  const columns: ColumnConfig<Client | any>[] = [
    {
      key: "id",
      header: "ID",
      filterable: true,
      filterType: "text",
      sortable: true,
      render: (item) => `CL-${item.id}`,
    },
    {
      key: "form_data.father_name",
      header: "Όνομα Πατέρα",
      filterable: true,
      filterType: "text",
      render: (item) => item.form_data?.father_name || "-",
    },
    {
      key: "form_data.mother_name",
      header: "Όνομα Μητέρας",
      filterable: true,
      filterType: "text",
      render: (item) => item.form_data?.mother_name || "-",
    },
    {
      key: "status",
      header: "Status",
      filterable: true,
      filterType: "select",
      options: clientStatuses.map((status) => status.label),
      render: (item: Client) => (
        <Badge variant={getClientStatusVariant(item.status)}>
          {getClientStatusLabel(item.status)}
        </Badge>
      ),
    },
    {
      key: "total_revenue",
      header: "Revenue",
      filterable: true,
      filterType: "number",
      render: (item) => `€${item.total_revenue || 0}
      `,
    },
    {
      key: "working_level",
      header: "Επίπεδο",
      filterable: true,
      filterType: "select",
      options: ["junior", "basic", "vip"],
      render: (item) => (
        <Badge
          variant={
            item.form_data?.working_level === "junior"
              ? "default"
              : item.form_data?.working_level === "basic"
              ? "outline"
              : "destructive"
          }
        >
          {item.form_data?.working_level?.slice(0, 1).toUpperCase() +
            item.form_data?.working_level?.slice(1) || "-"}
        </Badge>
      ),
    },
    {
      key: "form_data.position_type",
      header: "Είδος",
      filterable: true,
      filterType: "select",
      options: ["Live-In", "Live-Out"],
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          <Badge variant="outline">
            {item.form_data?.position_type
              ?.split("-")
              .map((word) => word[0].toUpperCase() + word.slice(1))
              .join("-")}
          </Badge>
        </div>
      ),
    },
    {
      key: "form_data.schedule_type",
      header: "Πρόγραμμα",
      filterable: true,
      filterType: "select",
      options: ["Full-Time", "Part-Time"],
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          <Badge variant="outline">
            {item.form_data?.schedule_type
              ?.split("-")
              .map((word) => word[0].toUpperCase() + word.slice(1))
              .join("-")}
          </Badge>
        </div>
      ),
    },
    {
      key: "form_data.position_interests",
      header: "Θέση",
      filterable: true,
      filterType: "select",
      options: getPositionOptions(),
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          {item.form_data?.position_interests?.map((position) => (
            <Badge key={position} variant="outline">
              {getPositionLabel(position)}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: "form_data.languages",
      header: "Γλώσσα",
      filterable: true,
      filterType: "select",
      options: getLanguageOptions(),
      filterFn: (row, _columnId, filterValue) => {
        const languages = row.form_data?.languages;
        if (!languages || !Array.isArray(languages) || languages.length === 0)
          return false;
        return languages.some((lang) =>
          getLanguageLabel(lang).includes(filterValue)
        );
      },
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          {item.form_data?.languages?.map((lang) => (
            <Badge key={lang.language} variant="outline">
              {getLanguageLabel(lang.language)}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: "form_data.position_duration",
      header: "Διάρκεια",
      filterable: true,
      filterType: "select",
      options: getDurationInterestsOptions(),
      render: (item: Client) => (
        <div className="flex flex-wrap gap-1">
          <Badge variant="secondary">
            {
              durationOptions.find(
                (option) => option.id === item.form_data?.position_duration
              )?.label
            }
          </Badge>
        </div>
      ),
    },
    {
      key: "form_data.start_date",
      header: "Έναρξη",
      filterable: true,
      filterType: "select",
      options: startDateOptions.map((option) => option.labelEl),
      render: (item: Client) => {
        const startDateOption = startDateOptions.find(
          (option) => option.id === item.form_data?.start_date
        );
        return (
          <div className="flex flex-wrap gap-1">
            {startDateOption ? startDateOption.labelEl : "Δεν δηλώθηκε"}
          </div>
        );
      },
    },

    {
      key: "form_data.city",
      header: "Τοποθεσία",
      filterable: true,
      filterType: "city-select",
      render: (item: Client) => getCityLabel(item.form_data?.city),
    },
    {
      key: "form_data.children_age_experience",
      header: "Παιδιά",
      filterable: true,
      filterType: "select",
      options: getChildrenAgeOptions(),
      render: (item: Client) => {
        return (
          Array.isArray(item.form_data?.children_age) &&
          item.form_data?.children_age.length > 0 &&
          item.form_data?.children_age.map((age) => {
            const option = childrenAgeOptions.find((o) => o.id === age);
            return (
              <Badge key={age} variant="outline">
                {option?.labelEl}
              </Badge>
            );
          })
        );
      },
    },
  ];

  // Define base context menu items (view items always available)
  let contextMenuItems: ContextMenuItem[] = [
    {
      label: "Προφίλ Πελάτη",
      icon: <User className="h-4 w-4" />,
      onClick: (item: Client) => {
        navigate(`/crm/client/${item.id}`);
      },
    },
    {
      label: "Νέο παράθυρο",
      icon: <ExternalLink className="h-4 w-4" />,
      onClick: (item: Client) => {
        window.open(`/crm/client/${item.id}`, "_blank");
      },
    },
  ];
  // Add deal creation option if user has permission
  if (canCreateDeals) {
    contextMenuItems.push({
      label: "Δημιουργία Deal",
      icon: <Handshake className="h-4 w-4" />,
      onClick: (item: Client) => {
        setSelectedClient(item);
        setIsDealDialogOpen(true);
      },
    });
  }
  // Add edit options if user has permission
  if (canEditCRM) {
    contextMenuItems = [
      ...contextMenuItems,
      {
        label: "Ενημέρωση Κατάστασης",
        icon: <Edit className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "status");
        },
      },
      {
        label: "Ενημέρωση Ονόματος Πατέρα",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.father_name");
        },
      },
      {
        label: "Ενημέρωση Ονόματος Μητέρας",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.mother_name");
        },
      },
      {
        label: "Ενημέρωση Είδους",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.position_type");
        },
      },
      {
        label: "Ενημέρωση Προγράμματος",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.schedule_type");
        },
      },
      {
        label: "Ενημέρωση Τοποθεσίας",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Client) => {
          openUpdateDialog(item, "form_data.city");
        },
      },
    ];
  }
  // Handle deal dialog close
  const handleDealDialogClose = (open: boolean) => {
    setIsDealDialogOpen(open);
    if (!open) {
      // Reset selected contact when dialog closes
      setTimeout(() => {
        setSelectedClient(null);
      }, 100);
    }
  };
  // Add delete option if user has permission
  if (canDeleteCRM) {
    contextMenuItems.push({
      label: "Διαγραφή Πελάτη",
      icon: <Trash2 className="h-4 w-4" />,
      onClick: handleDeleteClient,
      className: "text-destructive hover:bg-destructive/10",
    });
  }

  return (
    <div className="space-y-4">
      <>
        <NannyBlueTable
          fetchData={fetchClients}
          columns={columns}
          onRowClick={(row) => navigate(`/crm/client/${row.id}`)}
          contextMenuItems={contextMenuItems}
          shouldReload={shouldReload}
        />

        {/* Update Dialog */}
        <ContactUpdateDialog
          contact={selectedClient}
          isOpen={isUpdateDialogOpen}
          onClose={handleDialogClose}
          onSuccess={handleUpdateSuccess}
          updateField={updateField}
        />
        {/* Create Deal Sheet */}
        {selectedClient && (
          <CreateDealSheet
            open={isDealDialogOpen}
            onOpenChange={handleDealDialogClose}
            clientId={selectedClient.id.toString()}
            onSuccess={handleUpdateSuccess}
          />
        )}
        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Διαγραφή Πελάτη</AlertDialogTitle>
              <AlertDialogDescription>
                Είστε βέβαιοι ότι θέλετε να διαγράψετε τον πελάτη "
                {selectedClient?.form_data?.father_name} &
                {selectedClient?.form_data?.mother_name}" ? Αυτή η ενέργεια δεν
                μπορεί να αναιρεθεί.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Άκυρο</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeleteClient}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Διαγραφή
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    </div>
  );
};

export default ClientsTable;
