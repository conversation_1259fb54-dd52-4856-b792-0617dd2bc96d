import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import {
  ClientNote,
  addClientNote,
  deleteClientNote,
  fetchClientNotes,
  updateClientNote,
} from "@/services/clientNotesService";
import { Calendar, Edit2, MessageCircle, Phone, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Client } from "../ClientsTable";

interface HistoryTabProps {
  client: Client;
  editMode: boolean;
}

const HistoryTab = ({ client, editMode }: HistoryTabProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [notes, setNotes] = useState<ClientNote[]>([]);
  const [loading, setLoading] = useState(true);
  const [newNote, setNewNote] = useState("");
  const [noteType, setNoteType] = useState("note");
  const [submitting, setSubmitting] = useState(false);
  const [editingNote, setEditingNote] = useState<ClientNote | null>(null);
  const [editedNoteContent, setEditedNoteContent] = useState("");
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // Load notes when component mounts
  useEffect(() => {
    loadNotes();
  }, [client.id]);

  // Function to load notes
  const loadNotes = async () => {
    setLoading(true);
    try {
      const clientNotes = await fetchClientNotes(client.id.toString());
      setNotes(clientNotes);
    } catch (error) {
      console.error("Error loading notes:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία φόρτωσης σημειώσεων.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to add a new note
  const handleAddNote = async () => {
    if (!newNote.trim() || !user) return;
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id")
      .eq("supabase_uuid", user.id)
      .single();
    if (userError) {
      console.error("Error fetching user id:", userError);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία προσθήκης σημείωσης.",
        variant: "destructive",
      });
      return;
    }
    setSubmitting(true);

    try {
      const addedNote = await addClientNote(
        client.id.toString(),
        userData.id,
        newNote,
        noteType
      );

      if (addedNote) {
        setNotes([addedNote, ...notes]);
        setNewNote("");
        setNoteType("note");
        setIsAddDialogOpen(false);
        toast({
          title: "Επιτυχία",
          description: "Η σημείωση προστέθηκε με επιτυχία.",
          variant: "success",
        });
      } else {
        throw new Error("Failed to add note");
      }
    } catch (error) {
      console.error("Error adding note:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία προσθήκης σημείωσης.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Function to update a note
  const handleUpdateNote = async () => {
    if (!editingNote || !editedNoteContent.trim()) return;

    setSubmitting(true);
    try {
      const updatedNote = await updateClientNote(
        editingNote.id,
        editedNoteContent
      );

      if (updatedNote) {
        // Update the notes array with the updated note
        setNotes(
          notes.map((note) => (note.id === updatedNote.id ? updatedNote : note))
        );
        setEditingNote(null);
        setEditedNoteContent("");
        setIsEditDialogOpen(false);
        toast({
          title: "Επιτυχία",
          description: "Η σημείωση ενημερώθηκε με επιτυχία.",
          variant: "success",
        });
      } else {
        throw new Error("Failed to update note");
      }
    } catch (error) {
      console.error("Error updating note:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία ενημέρωσης σημείωσης.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Function to delete a note
  const handleDeleteNote = async (noteId: string) => {
    if (!confirm("Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτή τη σημείωση;")) {
      return;
    }

    try {
      const success = await deleteClientNote(noteId);

      if (success) {
        // Remove the deleted note from the notes array
        setNotes(notes.filter((note) => note.id !== noteId));
        toast({
          title: "Επιτυχία",
          description: "Η σημείωση διαγράφηκε με επιτυχία.",
          variant: "success",
        });
      } else {
        throw new Error("Failed to delete note");
      }
    } catch (error) {
      console.error("Error deleting note:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία διαγραφής σημείωσης.",
        variant: "destructive",
      });
    }
  };

  // Function to open edit dialog
  const openEditDialog = (note: ClientNote) => {
    setEditingNote(note);
    setEditedNoteContent(note.note);
    setIsEditDialogOpen(true);
  };

  // Format date for display
  const formatDate = (dateStr: string) => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return date.toLocaleDateString("el-GR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get icon based on note type
  const getNoteTypeIcon = (type: string) => {
    switch (type) {
      case "call":
        return <Phone className="h-4 w-4" />;
      case "meeting":
        return <Calendar className="h-4 w-4" />;
      case "email":
        return <MessageCircle className="h-4 w-4" />;
      default:
        return <MessageCircle className="h-4 w-4" />;
    }
  };

  // Get note type label
  const getNoteTypeLabel = (type: string) => {
    switch (type) {
      case "call":
        return "Τηλεφωνική Επικοινωνία";
      case "meeting":
        return "Συνάντηση";
      case "email":
        return "Email";
      default:
        return "Σημείωση";
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6" variant="light">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-primary">
              Ιστορικό Επικοινωνίας
            </h3>
            <p className="text-secondary">
              Ιστορικό επικοινωνίας με τον πελάτη.
            </p>
          </div>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                Προσθήκη Σημείωσης
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Προσθήκη Νέας Σημείωσης</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Τύπος Σημείωσης</label>
                  <Select
                    value={noteType}
                    onValueChange={(value) => setNoteType(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Επιλέξτε τύπο" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="note">Σημείωση</SelectItem>
                      <SelectItem value="call">
                        Τηλεφωνική Επικοινωνία
                      </SelectItem>
                      <SelectItem value="meeting">Συνάντηση</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Περιεχόμενο</label>
                  <Textarea
                    placeholder="Προσθέστε μια νέα σημείωση..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                  >
                    Ακύρωση
                  </Button>
                  <Button
                    onClick={handleAddNote}
                    disabled={!newNote.trim() || submitting}
                  >
                    {submitting ? "Αποθήκευση..." : "Αποθήκευση"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        <div className="space-y-4 mt-4">
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin h-8 w-8 border-2 border-primary rounded-full border-t-transparent"></div>
            </div>
          ) : notes.length > 0 ? (
            notes.map((note) => (
              <div
                key={note.id}
                className="border-l-2 border-primary pl-4 py-2"
              >
                <div className="flex justify-between">
                  <div className="flex items-center gap-2">
                    {getNoteTypeIcon(note.note_type)}
                    <h4 className="font-medium">
                      {getNoteTypeLabel(note.note_type)}
                    </h4>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {formatDate(note.created_at)}
                    </span>
                    {user && user.id === note.user_id && (
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => openEditDialog(note)}
                        >
                          <Edit2 className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-destructive hover:text-destructive/80"
                          onClick={() => handleDeleteNote(note.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-sm mt-1 whitespace-pre-line">{note.note}</p>
                <p className="text-xs text-muted-foreground mt-2">
                  Από: {note.user_email}
                </p>
              </div>
            ))
          ) : (
            <p className="text-center py-8 text-muted-foreground">
              Δεν υπάρχουν σημειώσεις για αυτόν τον πελάτη.
            </p>
          )}
        </div>
      </Card>

      {/* Edit Note Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Επεξεργασία Σημείωσης</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 mt-4">
            <Textarea
              value={editedNoteContent}
              onChange={(e) => setEditedNoteContent(e.target.value)}
              className="min-h-[150px]"
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditDialogOpen(false);
                  setEditingNote(null);
                  setEditedNoteContent("");
                }}
              >
                Ακύρωση
              </Button>
              <Button
                onClick={handleUpdateNote}
                disabled={!editedNoteContent.trim() || submitting}
              >
                {submitting ? "Αποθήκευση..." : "Αποθήκευση"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HistoryTab;
