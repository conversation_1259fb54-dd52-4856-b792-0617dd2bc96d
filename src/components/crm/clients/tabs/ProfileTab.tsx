import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SearchableCitySelect } from "@/components/ui/SearchableCitySelect";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { languageLevels, languages as languageOptions } from "@/lib/staticData";
import { getCityLabel, getLanguageLabel } from "@/lib/tableUtils";
import {
  childrenAgeOptions,
  positionOptions,
  startDateOptions,
} from "@/schemas/FormSchema";
import { Deal, DealStatus } from "@/services/dealService";
import {
  BookOpen,
  Briefcase,
  Calendar as CalendarIcon,
  Home,
  Plus,
  Trash2,
  User,
  <PERSON>,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Client } from "../ClientsTable";

interface ProfileTabProps {
  client: Client;
  editedClient: Client | null;
  editMode: boolean;
  handleInputChange: (field: string, value: any) => void;
  handlePositionInterestToggle?: (position: string) => void;
  isContact?: boolean;
  deals?: any[];
  dealsLoading?: boolean;
}

// Schedule options
const scheduleOptions = [
  { id: "full-time", label: "Full-Time" },
  { id: "part-time", label: "Part-Time" },
];

// Position types
const positionTypes = [
  { id: "live-in", label: "Live-In" },
  { id: "live-out", label: "Live-Out" },
];

const ProfileTab = ({
  client,
  editedClient,
  editMode,
  handleInputChange,
  handlePositionInterestToggle,
  isContact = false,
  deals = [],
  dealsLoading = false,
}: ProfileTabProps) => {
  const navigate = useNavigate();
  // Format date for display
  const formatDate = (dateStr: string) => {
    if (!dateStr) return "-";
    const date = new Date(dateStr);
    return date.toLocaleDateString("el-GR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };
  // Handle language changes
  const handleLanguageChange = (
    index: number,
    field: "language" | "level",
    value: string
  ) => {
    if (!editedClient) return;

    const updatedLanguages = [...(editedClient.form_data?.languages || [])];
    if (!updatedLanguages[index]) {
      updatedLanguages[index] = { language: "", level: "" };
    }

    updatedLanguages[index] = {
      ...updatedLanguages[index],
      [field]: value,
    };

    handleInputChange("form_data.languages", updatedLanguages);
  };

  // Add a new language
  const handleAddLanguage = () => {
    if (!editedClient) return;

    const updatedLanguages = [
      ...(editedClient.form_data?.languages || []),
      { language: "", level: "" },
    ];

    handleInputChange("form_data.languages", updatedLanguages);
  };

  // Remove a language
  const handleRemoveLanguage = (index: number) => {
    if (!editedClient) return;

    const updatedLanguages = [...(editedClient.form_data?.languages || [])];
    updatedLanguages.splice(index, 1);

    handleInputChange("form_data.languages", updatedLanguages);
  };

  return (
    <div className="w-full grid grid-cols-1 md:grid-cols-[320px_1fr] gap-8">
      {/* Left Column - Family Information */}
      <div className="space-y-4">
        {/* Family Information */}
        <Card variant="accent" className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 mb-3">
              <Users className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-medium text-primary">Οικογένεια</h3>
            </div>
          </div>
          <div className="space-y-4">
            {/* Children Information */}
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Αριθμός Παιδιών</p>
              {editMode ? (
                <Input
                  value={editedClient?.form_data?.children_count || ""}
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.children_count",
                      e.target.value
                    )
                  }
                  placeholder="Αριθμός παιδιών"
                />
              ) : (
                <p className="font-medium">
                  {client.form_data?.children_count || "-"}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground font-medium">
                Ηλικίες Παιδιών:
              </p>
              {editMode ? (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {childrenAgeOptions.map((option) => (
                    <div
                      key={option.id}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`checkbox-${option.id}`}
                        checked={
                          editedClient?.form_data?.children_age?.includes(
                            option.id
                          ) || false
                        }
                        onCheckedChange={(checked) => {
                          const updatedValue = checked
                            ? [
                                ...(editedClient?.form_data?.children_age ||
                                  []),
                                option.id,
                              ]
                            : (
                                editedClient?.form_data?.children_age || []
                              ).filter((value) => value !== option.id);
                          handleInputChange(
                            "form_data.children_age",
                            updatedValue
                          );
                        }}
                      />
                      <Label
                        htmlFor={`checkbox-${option.id}`}
                        className="font-normal cursor-pointer"
                      >
                        {option.labelEl}
                      </Label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-wrap gap-2 mt-2">
                  {(() => {
                    // Make sure childrenAgeOptions is properly loaded
                    if (
                      !childrenAgeOptions ||
                      childrenAgeOptions.length === 0
                    ) {
                      console.error(
                        "Children age options not loaded properly!"
                      );
                      return (
                        <p className="text-red-500">
                          Error loading age options
                        </p>
                      );
                    }

                    // Check if children_age exists and is an array
                    if (
                      Array.isArray(client.form_data?.children_age) &&
                      client.form_data?.children_age.length > 0
                    ) {
                      // Create a local copy to avoid any reference issues
                      const childrenAges = [...client.form_data.children_age];

                      return childrenAges.map((ageId) => {
                        // Find the matching option
                        const option = childrenAgeOptions.find(
                          (o) => o.id === ageId
                        );

                        console.debug(
                          `Age ID: ${ageId}, Found Option:`,
                          option
                        );

                        // If option is found, display the badge with the label
                        if (option) {
                          return (
                            <Badge
                              key={ageId}
                              variant="secondary"
                              className="px-3 py-1 text-sm mb-2"
                            >
                              {option.labelEl}
                            </Badge>
                          );
                        } else {
                          // If no matching option is found, display the raw ID for debugging
                          return (
                            <Badge
                              key={ageId}
                              variant="outline"
                              className="px-3 py-1 text-sm mb-2"
                            >
                              ID: {ageId}
                            </Badge>
                          );
                        }
                      });
                    } else {
                      return <p className="text-gray-400">-</p>;
                    }
                  })()}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Ειδικές Ανάγκες</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.special_needs || "no"}
                  onValueChange={(value) =>
                    handleInputChange("form_data.special_needs", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Ναι</SelectItem>
                    <SelectItem value="no">Όχι</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.special_needs === "yes" ? "Ναι" : "Όχι"}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Μαθησιακές Δυσκολίες
              </p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.learning_difficulties || "no"}
                  onValueChange={(value) =>
                    handleInputChange("form_data.learning_difficulties", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Ναι</SelectItem>
                    <SelectItem value="no">Όχι</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.learning_difficulties === "yes"
                    ? "Ναι"
                    : "Όχι"}
                </p>
              )}
            </div>
          </div>
        </Card>

        {/* Location Information */}
        <Card variant="accent" className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Home className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">Τοποθεσία</h3>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Διεύθυνση</p>
              {editMode ? (
                <Input
                  value={editedClient?.form_data?.permanent_address || ""}
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.permanent_address",
                      e.target.value
                    )
                  }
                  placeholder="Διεύθυνση κατοικίας"
                />
              ) : (
                <p className="font-medium">
                  {client.form_data?.permanent_address || "-"}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Πόλη</p>
              {editMode ? (
                <SearchableCitySelect
                  value={editedClient?.form_data?.city || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.city", value)
                  }
                  placeholder="Πόλη"
                />
              ) : (
                <p className="font-medium">
                  {getCityLabel(client.form_data?.city) || "-"}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">Χώρα</p>
              {editMode ? (
                <Input
                  value={editedClient?.form_data?.country || ""}
                  onChange={(e) =>
                    handleInputChange("form_data.country", e.target.value)
                  }
                  placeholder="Χώρα"
                />
              ) : (
                <p className="font-medium">
                  {client.form_data?.country || "Ελλάδα"}
                </p>
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Right Column - Job Requirements */}
      <div className="space-y-6">
        {/* Position Requirements */}
        <Card className="p-4" variant="accent">
          <div className="flex items-center gap-2 mb-3">
            <Briefcase className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">
              Απαιτήσεις Θέσης
            </h3>
          </div>

          {/* Position Interests */}
          <div className="mb-4">
            <p className="text-sm text-muted-foreground mb-2">
              Ενδιαφέροντα Θέσεων
            </p>
            {editMode ? (
              <div className="flex flex-wrap gap-2">
                {positionOptions.map((position) => (
                  <div key={position.id} className="flex items-center gap-2">
                    <Checkbox
                      id={`position-${position.id}`}
                      checked={
                        editedClient?.form_data?.position_interests?.includes(
                          position.id
                        ) || false
                      }
                      onCheckedChange={() =>
                        handlePositionInterestToggle &&
                        handlePositionInterestToggle(position.id)
                      }
                    />
                    <Label
                      htmlFor={`position-${position.id}`}
                      className="cursor-pointer"
                    >
                      {position.label}
                    </Label>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-wrap gap-1">
                {Array.isArray(client.form_data?.position_interests) &&
                client.form_data?.position_interests.length > 0 ? (
                  client.form_data?.position_interests.map(
                    (positionId, index) => {
                      const position = positionOptions.find(
                        (p) => p.id === positionId
                      );
                      return (
                        <Badge key={index} variant="secondary">
                          {position ? position.label : positionId}
                        </Badge>
                      );
                    }
                  )
                ) : (
                  <p className="text-gray-400">-</p>
                )}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Position Type */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Είδος Θέσης</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.position_type || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.position_type", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή είδους θέσης" />
                  </SelectTrigger>
                  <SelectContent>
                    {positionTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {positionTypes.find(
                    (type) => type.id === client.form_data?.position_type
                  )?.label ||
                    client.form_data?.position_type ||
                    "-"}
                </p>
              )}
            </div>

            {/* Schedule Type */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Ωράριο</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.schedule_type || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.schedule_type", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή ωραρίου" />
                  </SelectTrigger>
                  <SelectContent>
                    {scheduleOptions.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {scheduleOptions.find(
                    (option) => option.id === client.form_data?.schedule_type
                  )?.label ||
                    client.form_data?.schedule_type ||
                    "-"}
                </p>
              )}
            </div>

            {/* Days per Week */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Ημέρες/Εβδομάδα</p>
              {editMode ? (
                <Input
                  value={editedClient?.form_data?.days_per_week || ""}
                  onChange={(e) =>
                    handleInputChange("form_data.days_per_week", e.target.value)
                  }
                  placeholder="π.χ. 5"
                />
              ) : (
                <p className="font-medium">
                  {client.form_data?.days_per_week || "-"}
                </p>
              )}
            </div>

            {/* Hours per Day */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Ώρες/Ημέρα</p>
              {editMode ? (
                <Input
                  value={editedClient?.form_data?.hours_per_day || ""}
                  onChange={(e) =>
                    handleInputChange("form_data.hours_per_day", e.target.value)
                  }
                  placeholder="π.χ. 8"
                />
              ) : (
                <p className="font-medium">
                  {client.form_data?.hours_per_day || "-"}
                </p>
              )}
            </div>

            {/* Position Duration */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Διάρκεια</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.position_duration || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.position_duration", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή διάρκειας" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="long-term">Μακροχρόνια</SelectItem>
                    <SelectItem value="short-term">Βραχυχρόνια</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.position_duration
                    ?.split("-")
                    .map((word) => word[0].toUpperCase() + word.slice(1))
                    .join("-") || "-"}
                </p>
              )}
            </div>

            {/* Start Date */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                Ημερομηνία Έναρξης
              </p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.start_date || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.start_date", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή ημερομηνίας" />
                  </SelectTrigger>
                  <SelectContent>
                    {startDateOptions.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.labelEl}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {startDateOptions.find(
                    (option) => option.id === client.form_data?.start_date
                  )?.labelEl ||
                    client.form_data?.start_date ||
                    "-"}
                </p>
              )}
            </div>
          </div>
        </Card>

        {/* Salary Information */}
        <Card className="p-4" variant="accent">
          <div className="flex items-center gap-2 mb-3">
            <CalendarIcon className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">Μισθός</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Salary Type */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Τύπος Μισθού</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.salary_type || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.salary_type", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή τύπου μισθού" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Μηνιαίος</SelectItem>
                    <SelectItem value="hourly">Ωριαίος</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.salary_type === "monthly"
                    ? "Μηνιαίος"
                    : client.form_data?.salary_type === "hourly"
                    ? "Ωριαίος"
                    : "-"}
                </p>
              )}
            </div>

            {/* Salary Amount */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Ποσό</p>
              {editMode ? (
                <Input
                  value={editedClient?.form_data?.salary_amount || ""}
                  onChange={(e) =>
                    handleInputChange("form_data.salary_amount", e.target.value)
                  }
                  placeholder="π.χ. 1000"
                />
              ) : (
                <p className="font-medium">
                  {client.form_data?.salary_amount
                    ? `${client.form_data.salary_amount} ${
                        client.form_data.salary_currency || "€"
                      }`
                    : "-"}
                </p>
              )}
            </div>

            {/* Salary Currency */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Νόμισμα</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.salary_currency || "€"}
                  onValueChange={(value) =>
                    handleInputChange("form_data.salary_currency", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή νομίσματος" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="€">Ευρώ (€)</SelectItem>
                    <SelectItem value="$">Δολάριο ($)</SelectItem>
                    <SelectItem value="£">Λίρα (£)</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.salary_currency === "€"
                    ? "Ευρώ (€)"
                    : client.form_data?.salary_currency === "$"
                    ? "Δολάριο ($)"
                    : client.form_data?.salary_currency === "£"
                    ? "Λίρα (£)"
                    : client.form_data?.salary_currency || "Ευρώ (€)"}
                </p>
              )}
            </div>
          </div>
        </Card>

        {/* Candidate Requirements */}
        <Card className="p-4" variant="accent">
          <div className="flex items-center gap-2 mb-3">
            <User className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">
              Απαιτήσεις Υποψηφίου
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Candidate Age */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Ηλικία Υποψηφίου</p>
              {editMode ? (
                <Input
                  value={editedClient?.form_data?.candidate_age || ""}
                  onChange={(e) =>
                    handleInputChange("form_data.candidate_age", e.target.value)
                  }
                  placeholder="π.χ. 25-40"
                />
              ) : (
                <p className="font-medium">
                  {client.form_data?.candidate_age || "-"}
                </p>
              )}
            </div>

            {/* Languages */}
            <div className="space-y-1 lg:col-span-2">
              <p className="text-sm text-muted-foreground">Γλώσσες</p>
              {editMode ? (
                <div className="space-y-2">
                  {(editedClient?.form_data?.languages || []).map(
                    (lang, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Select
                          value={lang.language}
                          onValueChange={(value) =>
                            handleLanguageChange(index, "language", value)
                          }
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Επιλογή γλώσσας" />
                          </SelectTrigger>
                          <SelectContent>
                            {languageOptions.map((option) => (
                              <SelectItem key={option.id} value={option.id}>
                                {option.labelEl}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Select
                          value={lang.level}
                          onValueChange={(value) =>
                            handleLanguageChange(index, "level", value)
                          }
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Επίπεδο" />
                          </SelectTrigger>
                          <SelectContent>
                            {languageLevels.map((level) => (
                              <SelectItem key={level.id} value={level.id}>
                                {level.labelEl}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveLanguage(index)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    )
                  )}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleAddLanguage}
                    className="mt-2 w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" /> Προσθήκη Γλώσσας
                  </Button>
                </div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {Array.isArray(client.form_data?.languages) &&
                  client.form_data?.languages.length > 0 ? (
                    client.form_data?.languages.map((language, index) => (
                      <Badge key={index} variant="secondary">
                        {getLanguageLabel(language.language, "el")} (
                        {languageLevels.find(
                          (level) => level.id === language.level
                        )?.labelEl || language.level}
                        )
                      </Badge>
                    ))
                  ) : (
                    <p className="text-gray-400">-</p>
                  )}
                </div>
              )}
            </div>

            {/* Working Level */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Επίπεδο Εργασίας</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.working_level || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.working_level", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή επιπέδου" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="junior">Junior</SelectItem>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="vip">VIP</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.working_level.slice(0, 1).toUpperCase() +
                    client.form_data?.working_level.slice(1) || "-"}
                </p>
              )}
            </div>

            {/* Driving License */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Δίπλωμα Οδήγησης</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.driving_license || "no"}
                  onValueChange={(value) =>
                    handleInputChange("form_data.driving_license", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Ναι</SelectItem>
                    <SelectItem value="no">Όχι</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.driving_license === "yes" ? "Ναι" : "Όχι"}
                </p>
              )}
            </div>

            {/* First Aid */}
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Πρώτες Βοήθειες</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.first_aid || "no"}
                  onValueChange={(value) =>
                    handleInputChange("form_data.first_aid", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Ναι</SelectItem>
                    <SelectItem value="no">Όχι</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.first_aid === "yes" ? "Ναι" : "Όχι"}
                </p>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Ασφάλιση</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.insurance || "no"}
                  onValueChange={(value) =>
                    handleInputChange("form_data.insurance", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Ναι</SelectItem>
                    <SelectItem value="no">Όχι</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.insurance === "yes" ? "Ναι" : "Όχι"}
                </p>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Τυπος Ασφάλισης</p>
              {editMode ? (
                <Select
                  value={editedClient?.form_data?.insurance_type || ""}
                  onValueChange={(value) =>
                    handleInputChange("form_data.insurance_type", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλογή τύπου" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ergosimo">Εργοσίμο</SelectItem>
                    <SelectItem value="stamps">Στάμπες</SelectItem>
                    <SelectItem value="none">Κανένα</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <p className="font-medium">
                  {client.form_data?.insurance_type === "ergosimo"
                    ? "Εργοσίμο"
                    : client.form_data?.insurance_type === "stamps"
                    ? "Στάμπες"
                    : "-"}
                </p>
              )}
            </div>
          </div>
        </Card>

        {/* Additional Details */}
        <Card className="p-4" variant="accent">
          <div className="flex items-center gap-2 mb-3">
            <BookOpen className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">
              Επιπλέον Πληροφορίες
            </h3>
          </div>
          {editMode ? (
            <Textarea
              value={editedClient?.form_data?.additional_details || ""}
              onChange={(e) =>
                handleInputChange(
                  "form_data.additional_details",
                  e.target.value
                )
              }
              placeholder="Επιπλέον πληροφορίες ή απαιτήσεις"
              className="min-h-32"
            />
          ) : client.form_data?.additional_details ? (
            <div className="bg-muted/20 p-4 rounded-lg">
              <p className="whitespace-pre-line">
                {client.form_data.additional_details}
              </p>
            </div>
          ) : (
            <p className="text-gray-400">Δεν υπάρχουν επιπλέον πληροφορίες</p>
          )}
        </Card>

        {/* Deals Section - Only for clients, not contacts */}
        {!isContact && (
          <Card variant="accent" className="p-4 mt-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Briefcase className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-medium text-primary">
                  Συμφωνίες (Deals)
                </h3>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-primary/20">
                    <th className="text-left py-2 font-medium text-primary">
                      ID
                    </th>
                    <th className="text-left py-2 font-medium text-primary">
                      Υποψήφιος
                    </th>
                    <th className="text-left py-2 font-medium text-primary">
                      Κατάσταση
                    </th>
                    <th className="text-left py-2 font-medium text-primary">
                      Ημερομηνία
                    </th>
                    <th className="text-left py-2 font-medium text-primary">
                      Ποσό
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {dealsLoading ? (
                    <tr>
                      <td colSpan={5} className="py-4 text-center">
                        <div className="flex justify-center">
                          <div className="animate-spin h-5 w-5 border-2 border-primary rounded-full border-t-transparent"></div>
                        </div>
                      </td>
                    </tr>
                  ) : deals && deals.length > 0 ? (
                    deals.map((deal: Deal) => (
                      <tr
                        onClick={() => navigate(`/deals/${deal.id}`)}
                        key={deal.id}
                        className="border-t border-primary/10 hover:bg-primary/5 cursor-pointer"
                      >
                        <td className="py-3">
                          <span className="font-medium text-primary">
                            {`DL-${deal.id.toString().substring(0, 5)}`}
                          </span>
                        </td>
                        <td className="py-3">
                          {deal.candidate_name || "Μη διαθέσιμο"}
                        </td>
                        <td className="py-3">
                          <Badge
                            variant={
                              deal.status === DealStatus.Active
                                ? "success"
                                : deal.status === DealStatus.InProgress
                                ? "outline"
                                : deal.status === DealStatus.Rejected
                                ? "destructive"
                                : "secondary"
                            }
                          >
                            {deal.status}
                          </Badge>
                        </td>
                        <td className="py-3">
                          {formatDate(deal.deal_date || "")}
                        </td>
                        <td className="py-3">
                          {deal.revenue ? `${deal.revenue} €` : "-"}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr className="border-t">
                      <td
                        colSpan={5}
                        className="py-8 text-center text-sm text-muted-foreground"
                      >
                        Δεν υπάρχουν deals για αυτόν τον πελάτη.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ProfileTab;
