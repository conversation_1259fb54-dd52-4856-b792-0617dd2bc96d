import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";

interface AddDealFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

const AddDealForm = ({ onSubmit, onCancel }: AddDealFormProps) => {
  const [formData, setFormData] = useState({
    name: "",
    status: "Active",
    start: "",
    end: "",
    revenue: "",
    nannyFee: "",
    client: "",
    nanny: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Όνομα Deal</label>
          <Input
            required
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="input-primary"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Status</label>
          <Select
            value={formData.status}
            onValueChange={(value) =>
              setFormData({ ...formData, status: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Επιλέξτε status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Ημερομηνία Έναρξης</label>
          <Input
            type="date"
            required
            value={formData.start}
            onChange={(e) =>
              setFormData({ ...formData, start: e.target.value })
            }
            className="input-primary"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Ημερομηνία Λήξης</label>
          <Input
            type="date"
            required
            value={formData.end}
            onChange={(e) => setFormData({ ...formData, end: e.target.value })}
            className="input-primary"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Revenue (€)</label>
          <Input
            type="number"
            required
            min="0"
            value={formData.revenue}
            onChange={(e) =>
              setFormData({ ...formData, revenue: e.target.value })
            }
            className="input-primary"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Nanny Fee (€)</label>
          <Input
            type="number"
            required
            min="0"
            value={formData.nannyFee}
            onChange={(e) =>
              setFormData({ ...formData, nannyFee: e.target.value })
            }
            className="input-primary"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Client</label>
          <Input
            required
            value={formData.client}
            onChange={(e) =>
              setFormData({ ...formData, client: e.target.value })
            }
            className="input-primary"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Nanny</label>
          <Input
            required
            value={formData.nanny}
            onChange={(e) =>
              setFormData({ ...formData, nanny: e.target.value })
            }
            className="input-primary"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          Ακύρωση
        </Button>
        <Button type="submit" className="btn-primary">
          Προσθήκη
        </Button>
      </div>
    </form>
  );
};

export default AddDealForm;
