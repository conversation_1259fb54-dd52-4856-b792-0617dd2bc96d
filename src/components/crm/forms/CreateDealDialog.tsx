import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";

import apiClient from "@/lib/api";
import { uploadFileToSupabase } from "@/lib/utils";
import { DealStatus, statusMap } from "@/services/dealService";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { createEmptyHistory } from "./CreateDealSheet";

interface CreateDealDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clientId: string;
  candidateId: string; // Make candidateId optional
  onSuccess?: () => void;
  candidateName?: string; // Optional prop for candidate name
  clientName?: string; // Optional prop for client name
}

const CreateDealDialog = ({
  open,
  onOpenChange,
  clientId,
  candidateId,
  onSuccess,
  candidateName,
  clientName,
}: CreateDealDialogProps) => {
  // Format today's date as YYYY-MM-DD to avoid timezone issues
  const today = new Date();
  const formattedToday = `${today.getFullYear()}-${String(
    today.getMonth() + 1
  ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;

  const [formData, setFormData] = useState({
    name: "",
    revenue: "",
    candidateSalary: "",
    status: DealStatus.InProgress,
    dealDate: formattedToday,
    initialComment: "",
  });
  const [selectedCandidateId, setSelectedCandidateId] = useState<string>("");
  const [contractFile, setContractFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { toast } = useToast();

  // Set the candidate ID from props if available
  useEffect(() => {
    if (candidateId) {
      setSelectedCandidateId(candidateId);
    } else {
      setSelectedCandidateId("");
    }
  }, [candidateId, open]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleStatusChange = (value: string) => {
    setFormData({
      ...formData,
      status: value as DealStatus,
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setContractFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate that a candidate is selected
    if (!selectedCandidateId) {
      toast({
        title: "Σφάλμα",
        description: "Παρακαλώ επιλέξτε έναν υποψήφιο για το deal.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      let contractUrl = "";

      // Upload contract file if selected
      if (contractFile) {
        setUploadProgress(0);
        const uid = crypto.randomUUID();
        const filePath = `${uid}/${formData.name}.${contractFile.name
          .split(".")
          .pop()}`;
        contractUrl = await uploadFileToSupabase(
          contractFile,
          "deal-documents",
          filePath,
          (progress) => {
            setUploadProgress(progress);
          }
        );
      }

      // Create history object with initial comment if provided
      const history = createEmptyHistory();
      if (formData.initialComment) {
        // Add the initial comment to the first step (trial)
        history.contact_client_nanny.comment = formData.initialComment;
      }

      // Log the date being sent to the API
      console.debug("Submitting deal with date:", formData.dealDate);

      // Create the request payload
      const payload = {
        name: formData.name,
        revenue: parseFloat(formData.revenue),
        candidate_salary: parseFloat(formData.candidateSalary),
        client: parseInt(clientId),
        candidate: parseInt(selectedCandidateId),
        contract_url: contractUrl || null,
        deal_date: formData.dealDate,
        status: statusMap[formData.status],
        history: history,
      };

      // Create deal with API
      const response = await apiClient.post("/deals/", payload);

      console.debug("Deal created successfully:", response.data);
      if (response.data.error) {
        throw new Error(response.data.error);
      }
      toast({
        title: "Επιτυχία",
        description: "To deal δημιουργήθηκε επιτυχώς.",
        variant: "default",
      });

      // Reset form
      setFormData({
        name: "",
        revenue: "",
        candidateSalary: "",
        status: DealStatus.InProgress,
        dealDate: formattedToday,
        initialComment: "",
      });
      setSelectedCandidateId("");
      setContractFile(null);
      setUploadProgress(0);

      // Blur any focused elements before closing
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur();
      }

      // Close dialog with a small delay to ensure blur takes effect
      setTimeout(() => {
        handleDialogOpenChange(false);

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      }, 10);
    } catch (error) {
      console.error("Error creating deal:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Υπήρξε πρόβλημα κατά τη δημιουργία του deal. Παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle dialog open/close with proper focus management
  const handleDialogOpenChange = (open: boolean) => {
    // If closing, ensure we don't leave focus in a weird state
    if (!open) {
      // Blur any focused elements before closing, but only if they're not inside a popover
      if (
        document.activeElement instanceof HTMLElement &&
        !document.activeElement.closest("[data-radix-popper-content-wrapper]")
      ) {
        document.activeElement.blur();
      }

      // Small delay before calling the parent's onOpenChange
      setTimeout(() => {
        onOpenChange(open);
      }, 10);
    } else {
      onOpenChange(open);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Δημιουργία Νέου Deal </DialogTitle>
            <DialogDescription>
              Παρακαλώ συμπληρώστε τα στοιχεία του νέου deal μεταξύ του{" "}
              {clientName} και {candidateName}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Όνομα Deal
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="DEAL-01"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Κατάσταση
              </Label>
              <div className="col-span-3">
                <Select
                  value={formData.status}
                  onValueChange={handleStatusChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Επιλέξτε κατάσταση" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={DealStatus.InProgress}>
                      Σε Εξέλιξη
                    </SelectItem>
                    <SelectItem value={DealStatus.Active}>Ενεργό</SelectItem>
                    <SelectItem value={DealStatus.Inactive}>
                      Ανενεργό
                    </SelectItem>
                    <SelectItem value={DealStatus.Rejected}>
                      Απορρίφθηκε
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="dealDate" className="text-right">
                Ημερομηνία Έναρξης
              </Label>
              <div className="col-span-3">
                <Input
                  id="dealDate"
                  name="dealDate"
                  type="date"
                  value={formData.dealDate}
                  onChange={(e) => {
                    if (e.target.value) {
                      // Use the date string directly to avoid timezone issues
                      setFormData({
                        ...formData,
                        dealDate: e.target.value,
                      });
                    }
                  }}
                  className="w-full"
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="revenue" className="text-right">
                Revenue (€)
              </Label>
              <Input
                id="revenue"
                name="revenue"
                type="number"
                step="0.01"
                min="0"
                value={formData.revenue}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="350.00"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="candidateSalary" className="text-right">
                Candidate Salary (€)
              </Label>
              <Input
                id="candidateSalary"
                name="candidateSalary"
                type="number"
                step="0.01"
                min="0"
                value={formData.candidateSalary}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="140.00"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contractFile" className="text-right">
                Αρχείο Συμβολαίου
              </Label>
              <div className="col-span-3">
                <Input
                  id="contractFile"
                  type="file"
                  onChange={handleFileChange}
                  className="col-span-3"
                  accept=".pdf,.doc,.docx"
                />
                {uploadProgress > 0 && uploadProgress < 100 && (
                  <div className="w-full bg-accent/30 rounded-full h-2 mt-2">
                    <div
                      className="bg-primary h-2 rounded-full"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                )}
                {contractFile && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Επιλεγμένο Αρχείο: {contractFile.name}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="initialComment" className="text-right pt-2">
                Αρχικό Σχόλιο
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="initialComment"
                  name="initialComment"
                  value={formData.initialComment}
                  onChange={handleInputChange}
                  placeholder="Προσθέστε σχόλια για το ιστορικό του deal..."
                  className="min-h-[80px]"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={(e) => {
                // Prevent default to avoid focus issues
                e.preventDefault();
                // Blur the button before closing
                if (document.activeElement instanceof HTMLElement) {
                  document.activeElement.blur();
                }
                // Small delay before closing to ensure blur takes effect
                setTimeout(() => handleDialogOpenChange(false), 10);
              }}
              disabled={isSubmitting}
            >
              Ακύρωση
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Δημιουργία...
                </>
              ) : (
                "Δημιουργία Deal"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateDealDialog;
