import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import { uploadFileToSupabase } from "@/lib/utils";
import {
  Deal,
  DealStatus,
  deleteDeal,
  toDealSchema,
  updateDeal,
} from "@/services/dealService";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@radix-ui/react-select";
import { Loader2, Trash2, Upload } from "lucide-react";
import { useState } from "react";

interface DealDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  deal: Deal | null;
  onSuccess?: () => void;
}

const DealDetailsDialog = ({
  open,
  onOpenChange,
  deal,
  onSuccess,
}: DealDetailsDialogProps) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [contractFile, setContractFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { toast } = useToast();

  if (!deal) return null;

  const formatDate = (dateStr: string) => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return date.toLocaleDateString("el-GR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const handleStatusChange = async (status: DealStatus) => {
    if (!deal) return;

    setIsUpdating(true);
    try {
      const updatedDeal = await updateDeal(
        deal.id,
        toDealSchema({
          ...deal,
          status,
        })
      );

      if (updatedDeal) {
        toast({
          title: "Επιτυχία",
          description: `Η κατάσταση του deal άλλαξε σε ${status}.`,
          variant: "default",
        });

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error("Failed to update deal status");
      }
    } catch (error) {
      console.error("Error updating deal status:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Υπήρξε πρόβλημα κατά την αλλαγή της κατάστασης του deal. Παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setContractFile(e.target.files[0]);
    }
  };

  const handleFileUpload = async () => {
    if (!contractFile || !deal) return;

    setIsUploading(true);
    setUploadProgress(0);
    try {
      const uid = crypto.randomUUID();
      const filePath = `${uid}/${deal.name}.${contractFile.name
        .split(".")
        .pop()}`;

      const storagePath = await uploadFileToSupabase(
        contractFile,
        "deal-documents",
        filePath,
        (progress) => {
          setUploadProgress(progress);
        }
      );

      // Update the deal with the new contract URL
      const updatedDeal = await updateDeal(
        deal.id,
        toDealSchema({
          ...deal,
          contract_url: storagePath,
        })
      );

      if (updatedDeal) {
        toast({
          title: "Επιτυχία",
          description: "Το αρχείο ανέβηκε επιτυχώς.",
          variant: "default",
        });

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error("Failed to update deal with contract URL");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Υπήρξε πρόβλημα κατά το ανέβασμα του αρχείου. Παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      setContractFile(null);
    }
  };

  const handleDeleteDeal = async () => {
    if (!deal) return;

    setIsDeleting(true);
    try {
      const success = await deleteDeal(deal.id);

      if (success) {
        toast({
          title: "Επιτυχία",
          description: "Το deal διαγράφηκε επιτυχώς.",
          variant: "default",
        });

        // Close the dialog
        onOpenChange(false);

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      } else {
        throw new Error("Failed to delete deal");
      }
    } catch (error) {
      console.error("Error deleting deal:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Υπήρξε πρόβλημα κατά τη διαγραφή του deal. Παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Λεπτομέρειες Deal</span>
              <Badge
                variant={
                  deal.status === DealStatus.Active
                    ? "success"
                    : deal.status === DealStatus.InProgress
                    ? "outline"
                    : deal.status === DealStatus.Rejected
                    ? "destructive"
                    : "secondary"
                }
              >
                {deal.status}
              </Badge>
            </DialogTitle>
            <DialogDescription>
              Προβολή και διαχείριση των λεπτομερειών του deal.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right font-medium">
                Όνομα Deal
              </Label>
              <div className="col-span-3">
                <span className="text-primary">{deal.name}</span>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="date" className="text-right font-medium">
                Ημερομηνία
              </Label>
              <div className="col-span-3">
                <span>{formatDate(deal.deal_date)}</span>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="revenue" className="text-right font-medium">
                Revenue
              </Label>
              <div className="col-span-3">
                <span>{deal.revenue} €</span>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="salary" className="text-right font-medium">
                Nanny Fee
              </Label>
              <div className="col-span-3">
                <span>{deal.candidate_salary} €</span>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="client" className="text-right font-medium">
                Πελάτης
              </Label>
              <div className="col-span-3">
                <span>
                  {deal.client_father_name && deal.client_mother_name
                    ? `${deal.client_father_name} & ${deal.client_mother_name}`
                    : deal.client_father_name ||
                      deal.client_mother_name ||
                      `Client #${deal.client_id}`}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="nanny" className="text-right font-medium">
                Nanny
              </Label>
              <div className="col-span-3">
                <span>
                  {deal.candidate_name && deal.candidate_surname
                    ? `${deal.candidate_name} ${deal.candidate_surname}`
                    : deal.candidate_name ||
                      deal.candidate_surname ||
                      `Nanny #${deal.candidate_id}`}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="contract" className="text-right font-medium">
                Συμβόλαιο
              </Label>
              <div className="col-span-3">
                {deal.contract_url ? (
                  <span className="text-primary">{deal.contract_url}</span>
                ) : (
                  <span className="text-muted-foreground">
                    Δεν υπάρχει αρχείο
                  </span>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="upload" className="text-right font-medium">
                Ανέβασμα Αρχείου
              </Label>
              <div className="col-span-3">
                <div className="flex items-center gap-2">
                  <Input
                    id="upload"
                    type="file"
                    onChange={handleFileChange}
                    disabled={isUploading}
                    className="max-w-xs"
                  />
                  <Button
                    type="button"
                    size="sm"
                    onClick={handleFileUpload}
                    disabled={!contractFile || isUploading}
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {uploadProgress}%
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        Ανέβασμα
                      </>
                    )}
                  </Button>
                </div>
                {isUploading && (
                  <Progress
                    value={uploadProgress}
                    className="h-2 mt-2"
                    indicatorClassName="bg-primary"
                  />
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <div>
              <Button
                type="button"
                variant="destructive"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={isDeleting || isUpdating || isUploading}
                className="mr-2"
              >
                {isDeleting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="mr-2 h-4 w-4" />
                )}
                Διαγραφή
              </Button>
            </div>
            <div>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isUpdating || isDeleting || isUploading}
                className="mr-2"
              >
                Κλείσιμο
              </Button>
              <Select
                value={deal.status}
                onValueChange={(value) => {
                  handleStatusChange(value as DealStatus);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Επιλέξτε κατάσταση" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={DealStatus.InProgress}>
                    Σε Εξέλιξη
                  </SelectItem>
                  <SelectItem value={DealStatus.Active}>Ενεργό</SelectItem>
                  <SelectItem value={DealStatus.Inactive}>Ανενεργό</SelectItem>
                  <SelectItem value={DealStatus.Rejected}>
                    Απορρίφθηκε
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Είστε σίγουροι;</AlertDialogTitle>
            <AlertDialogDescription>
              Αυτή η ενέργεια θα διαγράψει μόνιμα το deal "{deal.name}". Η
              ενέργεια αυτή δεν μπορεί να αναιρεθεί.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Άκυρο</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteDeal();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Διαγραφή
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DealDetailsDialog;
