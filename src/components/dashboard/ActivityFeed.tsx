import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PaginationParams } from "@/lib/paginationUtils";
import { Activity } from "@/services/activityService";
import { Filter, Loader, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

interface ActivityFeedProps {
  activities?: Activity[];
  isLoading?: boolean;
  pagination?: PaginationParams;
  totalCount?: number;
  totalPages?: number;
  onPaginationChange?: (pagination: PaginationParams) => void;
  onRefresh?: () => void;
}

const ActivityFeed = ({
  activities = [],
  isLoading = false,
  pagination = { page: 1, itemsPerPage: 5, filters: {} },
  totalCount = 0, // Used for display purposes
  totalPages = 1,
  onPaginationChange,
  onRefresh,
}: ActivityFeedProps) => {
  const navigate = useNavigate();
  const [showFilters, setShowFilters] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(
    null
  );
  const [showUserDetailsDialog, setShowUserDetailsDialog] = useState(false);

  // Effect to manage focus when dialog is opened or closed
  useEffect(() => {
    if (showUserDetailsDialog) {
      // When dialog opens, remove focus
    } else {
      // When dialog closes, ensure document is interactive
    }
  }, [showUserDetailsDialog]);

  // Table name options for filtering
  const tableOptions = [
    { value: "all", label: "Όλοι οι πίνακες" },
    { value: "clients", label: "Πελάτες" },
    { value: "candidates", label: "Υποψήφιοι" },
    { value: "deals", label: "Συμφωνίες" },
    { value: "users", label: "Χρήστες" },
    { value: "client_notes", label: "Σημειώσεις Πελατών" },
    { value: "candidate_notes", label: "Σημειώσεις Υποψηφίων" },
  ];

  // Operation options for filtering
  const operationOptions = [
    { value: "all", label: "Όλες οι ενέργειες" },
    { value: "INSERT", label: "Προσθήκη" },
    { value: "UPDATE", label: "Επεξεργασία" },
    { value: "DELETE", label: "Διαγραφή" },
  ];

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    if (onPaginationChange) {
      const newFilters = { ...pagination.filters };

      if (value === "all") {
        // Remove filter if empty value
        delete newFilters[key];
      } else {
        // Add/update filter
        newFilters[key] = value;
      }

      onPaginationChange({
        ...pagination,
        page: 1, // Reset to first page when filters change
        filters: newFilters,
      });
    }
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (onPaginationChange && newPage >= 1 && newPage <= totalPages) {
      onPaginationChange({
        ...pagination,
        page: newPage,
      });
    }
  };

  // Handle reset filters
  const handleResetFilters = () => {
    if (onPaginationChange) {
      onPaginationChange({
        ...pagination,
        page: 1,
        filters: {},
      });
    }
  };

  // Navigate to entity profile
  const navigateToEntityProfile = (activity: Activity) => {
    const { table_name } = activity;

    // Extract entity ID from the action string
    // The action string format is like "Προσθήκη Πελάτη CL-123" or "Επεξεργασία Υποψηφίου CAN-456"
    const actionParts = activity.action.split(" ");
    const lastPart = actionParts[actionParts.length - 1];

    // Check if the last part contains an ID with a prefix
    let entityId: string | null = null;

    if (lastPart.startsWith("CL-")) {
      entityId = lastPart.replace("CL-", "");
    } else if (lastPart.startsWith("CAN-")) {
      entityId = lastPart.replace("CAN-", "");
    } else if (lastPart.startsWith("DEAL-")) {
      entityId = lastPart.replace("DEAL-", "");
    }

    if (!entityId) return;

    switch (table_name) {
      case "clients":
        navigate(`/crm/client/${entityId}`);
        break;
      case "candidates":
        navigate(`/candidates/${entityId}`);
        break;
      case "deals":
        navigate(`/deals/${entityId}`);
        break;
      default:
        // For other entity types, no navigation
        break;
    }
  };

  // Show user details dialog with focus management
  const showUserDetails = (activity: Activity) => {
    setSelectedActivity(activity);
    // Use the focus manager to handle dialog opening
    // handleModalStateChange(setShowUserDetailsDialog, true);
    setShowUserDetailsDialog(true);
  };

  // Handle dialog state change with focus management
  const handleDialogStateChange = (open: boolean) => {
    // handleModalStateChange(setShowUserDetailsDialog, open);
    setShowUserDetailsDialog(open);
  };

  return (
    <Card className="h-full flex flex-col" variant="accent" gradient>
      <CardHeader className="border-b border-accent/20 pb-4 flex-shrink-0">
        <div className="flex justify-between items-center">
          <CardTitle className="text-primary text-xl font-bold">
            Δραστηριότητα
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="h-8 px-2"
            >
              <Filter className="h-4 w-4 mr-1" />
              Φίλτρα
            </Button>
            {onRefresh && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onRefresh}
                className="h-8 px-2"
                disabled={isLoading}
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
                />
              </Button>
            )}
          </div>
        </div>
        {showFilters && (
          <div className="mt-4 space-y-3 pt-3 border-t border-accent/20">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs mb-1 block">Πίνακας</label>
                <Select
                  value={pagination.filters?.table_name || "all"}
                  onValueChange={(value) =>
                    handleFilterChange("table_name", value)
                  }
                >
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Όλοι οι πίνακες" />
                  </SelectTrigger>
                  <SelectContent>
                    {tableOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-xs mb-1 block">Ενέργεια</label>
                <Select
                  value={pagination.filters?.operation || "all"}
                  onValueChange={(value) =>
                    handleFilterChange("operation", value)
                  }
                >
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Όλες οι ενέργειες" />
                  </SelectTrigger>
                  <SelectContent>
                    {operationOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col sm:flex-row items-center space-x-2">
                <label className="text-xs mb-1 block">
                  Εμφάνιση ανά σελίδα:
                </label>
                <Select
                  value={pagination.itemsPerPage.toString()}
                  onValueChange={(value) => {
                    if (onPaginationChange) {
                      onPaginationChange({
                        ...pagination,
                        page: 1, // Reset to first page when changing items per page
                        itemsPerPage: parseInt(value),
                      });
                    }
                  }}
                >
                  <SelectTrigger className="h-7 w-16 text-xs">
                    <SelectValue placeholder="5" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="15">15</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetFilters}
                className="h-7 text-xs"
              >
                Επαναφορά
              </Button>
            </div>
          </div>
        )}
      </CardHeader>
      {/* Main content area - grows to fill available space */}
      <CardContent className="pt-4 pb-0 flex-grow overflow-hidden flex flex-col">
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader className="animate-spin text-primary" />
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-10 text-tertiary">
            Δεν υπάρχουν πρόσφατες δραστηριότητες
          </div>
        ) : (
          <div className="overflow-y-auto pr-2 custom-scrollbar space-y-3 flex-grow">
            {activities.map((activity) => (
              <div key={activity.id}>
                <div
                  className="flex items-start space-x-4 p-3 rounded-lg hover:bg-primary/5 transition-all duration-300 cursor-context-menu"
                  onClick={() => navigateToEntityProfile(activity)}
                >
                  <Avatar className="mt-1 border-2 border-primary/20">
                    <AvatarImage
                      src={activity.user.avatar}
                      alt={activity.user.name}
                    />
                    <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white">
                      {activity.user.name[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-bold text-primary">
                      {activity.user.name}
                    </p>
                    <p className="text-sm text-secondary">
                      {activity.user.email}
                    </p>
                    <p className="text-sm text-primary font-semibold">
                      {activity.action}
                    </p>
                    <p className="text-xs text-tertiary italic">
                      {new Date(activity.timestamp).toLocaleString("el-GR", {
                        day: "numeric",
                        month: "short",
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Footer with pagination controls - always at bottom */}
      <div className="p-4 pt-2 mt-auto border-t border-accent/10 flex-shrink-0">
        <div className="flex sm:space-x-4 flex-col sm:flex-row justify-between items-center">
          {totalPages > 1 && (
            <Pagination className="justify-end">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(pagination.page - 1)}
                    className={
                      pagination.page <= 1
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum: number;
                  if (totalPages <= 5) {
                    // If 5 or fewer pages, show all
                    pageNum = i + 1;
                  } else if (pagination.page <= 3) {
                    // If near start, show first 5 pages
                    pageNum = i + 1;
                  } else if (pagination.page >= totalPages - 2) {
                    // If near end, show last 5 pages
                    pageNum = totalPages - 4 + i;
                  } else {
                    // Otherwise show 2 before and 2 after current page
                    pageNum = pagination.page - 2 + i;
                  }

                  return (
                    <PaginationItem key={pageNum}>
                      <PaginationLink
                        isActive={pageNum === pagination.page}
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(pagination.page + 1)}
                    className={
                      pagination.page >= totalPages
                        ? "pointer-events-none opacity-50"
                        : ""
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </div>
      </div>

      {/* User Details Dialog */}
      <Dialog
        open={showUserDetailsDialog}
        onOpenChange={(open) => {
          handleDialogStateChange(open);
          if (!open) {
            // Ensure document is interactive after dialog closes
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Στοιχεία Χρήστη</DialogTitle>
            <DialogDescription>
              Πληροφορίες για τον χρήστη που πραγματοποίησε την ενέργεια.
            </DialogDescription>
          </DialogHeader>
          {selectedActivity && (
            <div className="py-4">
              <div className="flex items-center gap-4 mb-4">
                <Avatar className="h-12 w-12 border-2 border-primary/20">
                  <AvatarImage
                    src={selectedActivity.user.avatar}
                    alt={selectedActivity.user.name}
                  />
                  <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white">
                    {selectedActivity.user.name[0]}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium text-lg">
                    {selectedActivity.user.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedActivity.user.email}
                  </p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium mb-1">Ενέργεια</h4>
                  <p className="text-sm">{selectedActivity.action}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Ημερομηνία & Ώρα</h4>
                  <p className="text-sm">
                    {new Date(selectedActivity.timestamp).toLocaleString(
                      "el-GR",
                      {
                        day: "numeric",
                        month: "long",
                        year: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                        second: "2-digit",
                      }
                    )}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Πίνακας</h4>
                  <p className="text-sm">{selectedActivity.table_name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Τύπος Ενέργειας</h4>
                  <p className="text-sm">{selectedActivity.operation}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ActivityFeed;
