import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { downloadCSV } from "@/lib/csvUtils";
import {
  CityData,
  ClientLeadData,
  MonthlyData,
  TopClientData,
  fetchCitiesData,
  fetchClientLeadData,
  fetchMonthlyData,
  fetchTopClientsData,
} from "@/services/analyticsService";
import { FileDown, RefreshCw } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  <PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

const years = ["2025", "2024", "2023"];
const months = [
  "Ιανουάριος",
  "Φεβρουάριος",
  "<PERSON><PERSON>ρτι<PERSON>",
  "Απρ<PERSON><PERSON>ι<PERSON>",
  "<PERSON>άι<PERSON>",
  "Ιούνι<PERSON>",
  "Ιούλιος",
  "Αύγουστος",
  "Σεπτέμβριος",
  "Οκτώβριος",
  "Νοέμβριος",
  "Δεκέμβριος",
];
const quarters = ["1ο Τρίμηνο", "2ο Τρίμηνο", "3ο Τρίμηνο", "4ο Τρίμηνο"];

const Analytics = ({ canEdit }: { canEdit: boolean }) => {
  const [selectedYearForTopClients, setSelectedYearForTopClients] =
    useState("2025");
  const [selectedYearForCities, setSelectedYearForCities] = useState("2025");
  const [selectedYearForClientLeads, setSelectedYearForClientLeads] =
    useState("2025");
  const [selectedYearForMonthly, setSelectedYearForMonthly] = useState("2025");
  const [selectedMonth, setSelectedMonth] = useState("Ιανουάριος");
  const [selectedQuarter, setSelectedQuarter] = useState("1ο Τρίμηνο");

  // State for real data
  const [topClientsData, setTopClientsData] = useState<TopClientData[]>([]);
  const [citiesData, setCitiesData] = useState<CityData[]>([]);
  const [clientsLeadsData, setClientsLeadsData] = useState<ClientLeadData[]>(
    []
  );
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);

  // Loading states
  const [loadingTopClients, setLoadingTopClients] = useState(true);
  const [loadingCities, setLoadingCities] = useState(true);
  const [loadingClientLeads, setLoadingClientLeads] = useState(true);
  const [loadingMonthly, setLoadingMonthly] = useState(true);

  // Error states
  const [topClientsError, setTopClientsError] = useState<string | null>(null);
  const [citiesError, setCitiesError] = useState<string | null>(null);
  const [clientLeadsError, setClientLeadsError] = useState<string | null>(null);
  const [monthlyError, setMonthlyError] = useState<string | null>(null);

  // Colors from the project's color scheme
  const COLORS = ["#002347", "#8a7a71", "#ccc3b4", "#c5d4e8"];

  // Fetch top clients data
  const fetchTopClients = useCallback(
    async (forceRefresh = false) => {
      setLoadingTopClients(true);
      setTopClientsError(null);
      try {
        const data = await fetchTopClientsData(
          selectedYearForTopClients,
          forceRefresh
        );
        setTopClientsData(data);
      } catch (error) {
        console.error("Error fetching top clients data:", error);
        setTopClientsError("Σφάλμα κατά τη φόρτωση δεδομένων");
      } finally {
        setLoadingTopClients(false);
      }
    },
    [selectedYearForTopClients]
  );

  // Fetch cities data
  const fetchCities = useCallback(
    async (forceRefresh = false) => {
      setLoadingCities(true);
      setCitiesError(null);
      try {
        const data = await fetchCitiesData(
          selectedMonth,
          selectedYearForCities,
          forceRefresh
        );
        setCitiesData(data);
      } catch (error) {
        console.error("Error fetching cities data:", error);
        setCitiesError("Σφάλμα κατά τη φόρτωση δεδομένων");
      } finally {
        setLoadingCities(false);
      }
    },
    [selectedMonth, selectedYearForCities]
  );

  // Fetch client/lead data
  const fetchClientLead = useCallback(
    async (forceRefresh = false) => {
      setLoadingClientLeads(true);
      setClientLeadsError(null);
      try {
        const data = await fetchClientLeadData(
          selectedQuarter,
          selectedYearForClientLeads,
          forceRefresh
        );
        setClientsLeadsData(data);
      } catch (error) {
        console.error("Error fetching client/lead data:", error);
        setClientLeadsError("Σφάλμα κατά τη φόρτωση δεδομένων");
      } finally {
        setLoadingClientLeads(false);
      }
    },
    [selectedQuarter, selectedYearForClientLeads]
  );

  // Fetch monthly data
  const fetchMonthly = useCallback(
    async (forceRefresh = false) => {
      setLoadingMonthly(true);
      setMonthlyError(null);
      try {
        const data = await fetchMonthlyData(
          selectedYearForMonthly,
          forceRefresh
        );
        setMonthlyData(data);
      } catch (error) {
        console.error("Error fetching monthly data:", error);
        setMonthlyError("Σφάλμα κατά τη φόρτωση δεδομένων");
      } finally {
        setLoadingMonthly(false);
      }
    },
    [selectedYearForMonthly]
  );

  // Refresh all data
  const refreshAllData = useCallback(() => {
    fetchTopClients(true);
    fetchCities(true);
    fetchClientLead(true);
    fetchMonthly(true);
  }, [fetchTopClients, fetchCities, fetchClientLead, fetchMonthly]);

  // Fetch top clients data when year changes
  useEffect(() => {
    fetchTopClients();
  }, [fetchTopClients]);

  // Fetch cities data when month or year changes
  useEffect(() => {
    fetchCities();
  }, [fetchCities]);

  // Fetch client/lead data when quarter or year changes
  useEffect(() => {
    fetchClientLead();
  }, [fetchClientLead]);

  // Fetch monthly data when year changes
  useEffect(() => {
    fetchMonthly();
  }, [fetchMonthly]);

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      <div className="flex justify-end mb-4">
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 transition-all hover:shadow-md group"
          onClick={refreshAllData}
        >
          <RefreshCw className="h-4 w-4 group-hover:animate-spin" />
          Ανανέωση Δεδομένων
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Top Clients Chart */}
        <Card className="col-span-1" variant="accent" gradient hoverEffect>
          <CardHeader className="border-b border-light/30 pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="space-y-1">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary text-xl font-bold">
                  Κορυφαίοι Πελάτες
                </span>
                <Select
                  value={selectedYearForTopClients}
                  onValueChange={setSelectedYearForTopClients}
                >
                  <SelectTrigger className="w-[140px] md:w-[180px] border-light/30">
                    <SelectValue placeholder="Επιλέξτε έτος" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {canEdit && (
                <Button
                  variant="gradient-outline"
                  size="icon"
                  onClick={() => {
                    if (topClientsData.length > 0) {
                      downloadCSV(
                        topClientsData.map((item) => ({
                          Month: item.name,
                          Total: item.value,
                          ActiveClients: item.activeClients,
                          PastClients: item.pastClients,
                          Leads: item.leads,
                          Rejected: item.rejected,
                          TotalRevenue: item.totalRevenue,
                          AverageRevenue: item.avgRevenue,
                          TopCity: item.topCity || "",
                          TopCityCount: item.topCityCount || 0,
                        })),
                        `top-clients-${selectedYearForTopClients}`
                      );
                    }
                  }}
                  disabled={loadingTopClients || topClientsData.length === 0}
                  title="Κατέβασμα δεδομένων σε CSV"
                >
                  <FileDown className="h-4 w-4" />
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px]">
              {loadingTopClients ? (
                <div className="flex flex-col items-center justify-center h-full animate-pulse">
                  <Skeleton className="h-[250px] w-full" />
                  <p className="text-sm text-muted-foreground mt-2">
                    Φόρτωση δεδομένων...
                  </p>
                </div>
              ) : topClientsError ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-sm text-destructive">{topClientsError}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => fetchTopClients(true)}
                  >
                    Δοκιμάστε ξανά
                  </Button>
                </div>
              ) : topClientsData.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-sm text-muted-foreground">
                    Δεν υπάρχουν δεδομένα για το επιλεγμένο έτος
                  </p>
                </div>
              ) : (
                <ResponsiveContainer
                  width="100%"
                  height="100%"
                  className="animate-in fade-in duration-500"
                >
                  <BarChart
                    className=" dark:text-primary/20"
                    data={topClientsData}
                    layout="vertical"
                    margin={{ top: 0, right: 0, bottom: 0, left: 40 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                    <XAxis type="number" stroke="#002347" />
                    <YAxis dataKey="name" type="category" stroke="#002347" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#f8eedf",
                        border: "1px solid #002347",
                        borderRadius: "8px",
                        boxShadow:
                          "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                      }}
                      labelStyle={{ color: "#002347", fontWeight: "bold" }}
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          const data = payload[0].payload;
                          return (
                            <div className="bg-[#f8eedf] p-4 border border-[#002347] rounded-lg shadow-md">
                              <p className="font-bold text-[#002347] mb-2">
                                {label}
                              </p>
                              <div className="space-y-1 text-sm">
                                <p>
                                  <span className="font-semibold">
                                    Συνολικά:
                                  </span>{" "}
                                  {data.value}
                                </p>
                                <p>
                                  <span className="font-semibold">
                                    Ενεργοί Πελάτες:
                                  </span>{" "}
                                  {data.activeClients}
                                </p>
                                <p>
                                  <span className="font-semibold">
                                    Παλαιοί Πελάτες:
                                  </span>{" "}
                                  {data.pastClients}
                                </p>
                                <p>
                                  <span className="font-semibold">
                                    Υποψήφιοι:
                                  </span>{" "}
                                  {data.leads}
                                </p>
                                <p>
                                  <span className="font-semibold">
                                    Απορριφθέντες:
                                  </span>{" "}
                                  {data.rejected}
                                </p>
                                {data.totalRevenue > 0 && (
                                  <>
                                    <p>
                                      <span className="font-semibold">
                                        Συνολικά Έσοδα:
                                      </span>{" "}
                                      €{data.totalRevenue}
                                    </p>
                                    <p>
                                      <span className="font-semibold">
                                        Μέσα Έσοδα:
                                      </span>{" "}
                                      €{data.avgRevenue}
                                    </p>
                                  </>
                                )}
                                {data.topCity && (
                                  <p>
                                    <span className="font-semibold">
                                      Κορυφαία Πόλη:
                                    </span>{" "}
                                    {data.topCity} ({data.topCityCount})
                                  </p>
                                )}
                              </div>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <defs>
                      <linearGradient
                        id="barGradient"
                        x1="0"
                        y1="0"
                        x2="1"
                        y2="0"
                      >
                        <stop offset="0%" stopColor="#002347" />
                        <stop offset="100%" stopColor="#8a7a71" />
                      </linearGradient>
                    </defs>
                    <Bar
                      dataKey="value"
                      radius={[0, 4, 4, 0]}
                      fill="url(#barGradient)"
                    />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Cities Chart */}
        <Card className="col-span-1" variant="accent" gradient hoverEffect>
          <CardHeader className="border-b border-light/30 pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="space-y-1">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary text-xl font-bold">
                  Πόλεις με τα περισσότερα Leads
                </span>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select
                    value={selectedMonth}
                    onValueChange={setSelectedMonth}
                  >
                    <SelectTrigger className="w-full sm:w-[180px] w-full sm:w-auto border-light/30">
                      <SelectValue placeholder="Μήνας" />
                    </SelectTrigger>
                    <SelectContent>
                      {months.map((month) => (
                        <SelectItem key={month} value={month}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={selectedYearForCities}
                    onValueChange={setSelectedYearForCities}
                  >
                    <SelectTrigger className="w-full sm:w-[100px] w-full sm:w-auto border-light/30">
                      <SelectValue placeholder="Έτος" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {canEdit && (
                <Button
                  variant="gradient-outline"
                  size="icon"
                  onClick={() => {
                    if (citiesData.length > 0) {
                      downloadCSV(
                        citiesData.map((item) => ({
                          City: item.name,
                          Leads: item.value,
                        })),
                        `cities-leads-${selectedMonth}-${selectedYearForCities}`
                      );
                    }
                  }}
                  disabled={loadingCities || citiesData.length === 0}
                  title="Κατέβασμα δεδομένων σε CSV"
                >
                  <FileDown className="h-4 w-4" />
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px]">
              {loadingCities ? (
                <div className="flex flex-col items-center justify-center h-full animate-pulse">
                  <Skeleton className="h-[250px] w-full" />
                  <p className="text-sm text-muted-foreground mt-2">
                    Φόρτωση δεδομένων...
                  </p>
                </div>
              ) : citiesError ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-sm text-destructive">{citiesError}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => fetchCities(true)}
                  >
                    Δοκιμάστε ξανά
                  </Button>
                </div>
              ) : citiesData.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-sm text-muted-foreground">
                    Δεν υπάρχουν δεδομένα για την επιλεγμένη περίοδο
                  </p>
                </div>
              ) : (
                <ResponsiveContainer
                  width="100%"
                  height="100%"
                  className="animate-in fade-in duration-500"
                >
                  <BarChart
                    data={citiesData}
                    margin={{ top: 0, right: 0, bottom: 20, left: 0 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      angle={-45}
                      textAnchor="end"
                      stroke="#002347"
                    />
                    <YAxis stroke="#002347" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#f8eedf",
                        border: "1px solid #002347",
                        borderRadius: "8px",
                        boxShadow:
                          "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                      }}
                      formatter={(value: number) => [value, "Leads"]}
                      labelStyle={{ color: "#002347", fontWeight: "bold" }}
                    />
                    <defs>
                      <linearGradient
                        id="cityBarGradient"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop offset="0%" stopColor="#8a7a71" />
                        <stop offset="100%" stopColor="#002347" />
                      </linearGradient>
                    </defs>
                    <Bar
                      dataKey="value"
                      fill="url(#cityBarGradient)"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Clients/Leads Pie Chart */}
        <Card className="col-span-1" variant="accent" gradient hoverEffect>
          <CardHeader className="border-b border-light/30 pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="space-y-1">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary text-xl font-bold">
                  Πελάτες / Υποψήφιοι
                </span>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select onValueChange={setSelectedQuarter}>
                    <SelectTrigger className="w-full sm:w-[180px] w-full sm:w-auto border-light/30">
                      <SelectValue placeholder="Επιλέξτε τρίμηνο" />
                    </SelectTrigger>
                    <SelectContent>
                      {quarters.map((quarter) => (
                        <SelectItem key={quarter} value={quarter}>
                          {quarter}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={selectedYearForClientLeads}
                    onValueChange={setSelectedYearForClientLeads}
                  >
                    <SelectTrigger className="w-full sm:w-[100px] w-full sm:w-auto border-light/30">
                      <SelectValue placeholder="Έτος" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {canEdit && (
                <Button
                  variant="gradient-outline"
                  size="icon"
                  onClick={() => {
                    if (clientsLeadsData.length > 0) {
                      downloadCSV(
                        clientsLeadsData.map((item) => ({
                          Category: item.name,
                          Value: item.value,
                        })),
                        `clients-leads-${selectedQuarter}-${selectedYearForClientLeads}`
                      );
                    }
                  }}
                  disabled={loadingClientLeads || clientsLeadsData.length === 0}
                  title="Κατέβασμα δεδομένων σε CSV"
                >
                  <FileDown className="h-4 w-4" />
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px]">
              {loadingClientLeads ? (
                <div className="flex flex-col items-center justify-center h-full animate-pulse">
                  <Skeleton className="h-[250px] w-full" />
                  <p className="text-sm text-muted-foreground mt-2">
                    Φόρτωση δεδομένων...
                  </p>
                </div>
              ) : clientLeadsError ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-sm text-destructive">{clientLeadsError}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => fetchClientLead(true)}
                  >
                    Δοκιμάστε ξανά
                  </Button>
                </div>
              ) : clientsLeadsData.length === 0 ||
                clientsLeadsData.every((item) => item.value === 0) ? (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-sm text-muted-foreground">
                    Δεν υπάρχουν δεδομένα για την επιλεγμένη περίοδο
                  </p>
                </div>
              ) : (
                <ResponsiveContainer
                  width="100%"
                  height="100%"
                  className="animate-in fade-in duration-500"
                >
                  <PieChart>
                    <Pie
                      data={clientsLeadsData}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      outerRadius={80}
                      fill="#002347"
                      dataKey="value"
                      cornerRadius={8}
                      label={false}
                      stroke="#FFF"
                      strokeWidth={3}
                    >
                      {clientsLeadsData.map((_, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#f8eedf",
                        border: "1px solid #002347",
                        borderRadius: "8px",
                        boxShadow:
                          "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                      }}
                      labelStyle={{ color: "#002347", fontWeight: "bold" }}
                    />
                    <Legend
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      wrapperStyle={{
                        paddingTop: "20px",
                        fontSize: "14px",
                      }}
                      formatter={(value) => (
                        <span style={{ color: "#002347", fontWeight: "bold" }}>
                          {value}
                        </span>
                      )}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Chart */}
      <Card variant="accent" gradient hoverEffect>
        <CardHeader className="border-b border-light/30 pb-4">
          <CardTitle className="flex items-center justify-between">
            <div className="space-y-1">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary text-2xl font-bold">
                Πελάτες & Υποψήφιοι
              </span>
              <Select
                value={selectedYearForMonthly}
                onValueChange={setSelectedYearForMonthly}
              >
                <SelectTrigger className="w-[140px] md:w-[180px] border-light/30">
                  <SelectValue placeholder="Επιλέξτε έτος" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {canEdit && (
              <Button
                variant="gradient-outline"
                size="icon"
                onClick={() => {
                  if (monthlyData.length > 0) {
                    downloadCSV(
                      monthlyData,
                      `monthly-data-${selectedYearForMonthly}`
                    );
                  }
                }}
                disabled={loadingMonthly || monthlyData.length === 0}
                title="Κατέβασμα δεδομένων σε CSV"
              >
                <FileDown className="h-4 w-4" />
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="h-[400px]">
            {loadingMonthly ? (
              <div className="flex flex-col items-center justify-center h-full animate-pulse">
                <Skeleton className="h-[350px] w-full" />
                <p className="text-sm text-muted-foreground mt-2">
                  Φόρτωση δεδομένων...
                </p>
              </div>
            ) : monthlyError ? (
              <div className="flex flex-col items-center justify-center h-full">
                <p className="text-sm text-destructive">{monthlyError}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => fetchMonthly(true)}
                >
                  Δοκιμάστε ξανά
                </Button>
              </div>
            ) : monthlyData.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full">
                <p className="text-sm text-muted-foreground">
                  Δεν υπάρχουν δεδομένα για το επιλεγμένο έτος
                </p>
              </div>
            ) : (
              <ResponsiveContainer
                width="100%"
                height="100%"
                className="animate-in fade-in duration-500"
              >
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" stroke="#002347" />
                  <YAxis stroke="#002347" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "#f8eedf",
                      border: "1px solid #002347",
                      borderRadius: "8px",
                      boxShadow:
                        "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                    }}
                    labelStyle={{ color: "#002347", fontWeight: "bold" }}
                  />
                  <Legend
                    layout="horizontal"
                    verticalAlign="top"
                    align="right"
                    wrapperStyle={{
                      paddingBottom: "10px",
                      fontSize: "14px",
                    }}
                    formatter={(value) => (
                      <span style={{ color: "#002347", fontWeight: "bold" }}>
                        {value}
                      </span>
                    )}
                  />
                  <defs>
                    <linearGradient
                      id="clientsGradient"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#002347" />
                      <stop offset="50%" stopColor="#002347" opacity="0.7" />
                      <stop offset="100%" stopColor="#002347" opacity="0.5" />
                    </linearGradient>
                    <linearGradient
                      id="leadsGradient"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor="#8a7a71" />
                      <stop offset="50%" stopColor="#8a7a71" opacity="0.7" />
                      <stop offset="100%" stopColor="#8a7a71" opacity="0.5" />
                    </linearGradient>
                  </defs>
                  <Bar
                    dataKey="clients"
                    name="Πελάτες"
                    fill="url(#clientsGradient)"
                    radius={[4, 4, 0, 0]}
                    stroke="#FFF"
                    strokeWidth={1}
                  />
                  <Bar
                    dataKey="leads"
                    name="Υποψήφιοι"
                    fill="url(#leadsGradient)"
                    radius={[4, 4, 0, 0]}
                    stroke="#FFF"
                    strokeWidth={1}
                  />
                </BarChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
