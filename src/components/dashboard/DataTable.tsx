import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { useToast } from "@/components/ui/use-toast";
import { getFileDownloadUrl, getFilePreviewUrl } from "@/lib/utils";
import { Deal, DealStatus } from "@/services/dealService";
import { Download, Eye, FileText, User, Users } from "lucide-react";
import { useNavigate } from "react-router";
import { Button } from "../ui/button";

interface DataTableProps {
  deals?: Deal[];
}

const defaultDeals: Deal[] = [];

const DataTable = ({ deals = defaultDeals }: DataTableProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Handle file preview
  const handleFilePreview = async (filePath: string) => {
    try {
      // If it's a path and not a full URL, get a signed URL
      if (!filePath.startsWith("http")) {
        // Use a longer expiration time for preview (4 hours)
        const expiresIn = 14400; // 4 hours in seconds

        // Use our utility function to get a properly formatted URL
        const previewUrl = await getFilePreviewUrl(
          "deal-documents",
          filePath,
          expiresIn
        );

        // Open the URL in a new tab
        window.open(previewUrl, "_blank");

        toast({
          title: "Προβολή Αρχείου",
          description: "Το αρχείο άνοιξε σε νέα καρτέλα.",
        });
      } else {
        // If it's already a URL, just open it
        window.open(filePath, "_blank");
      }
    } catch (error) {
      console.error("Error previewing file:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η προβολή του αρχείου.",
        variant: "destructive",
      });
    }
  };

  // Handle file download
  const handleFileDownload = async (filePath: string) => {
    try {
      // If it's a path and not a full URL, get a signed URL
      if (!filePath.startsWith("http")) {
        // Use our utility function to get a properly formatted URL
        const downloadUrl = await getFileDownloadUrl(
          "deal-documents",
          filePath
        );

        // Create a temporary link and trigger download
        const a = document.createElement("a");
        a.href = downloadUrl;
        a.download = filePath.split("/").pop() || "document";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        toast({
          title: "Λήψη Αρχείου",
          description: "Η λήψη του αρχείου ξεκίνησε.",
        });
      } else {
        // If it's already a URL, just open it
        window.open(filePath, "_blank");
      }
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η λήψη του αρχείου.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card variant="accent" gradient>
      <CardHeader className="border pb-4">
        <div className="flex justify-between items-center">
          <div className="space-y-1">
            <CardTitle className="text-primary text-xl font-bold">
              Τελευταία Deals
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Εδώ εμφανίζονται τα τελευταία deals.
            </p>
          </div>

          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => navigate("/deals")}
          >
            Δείτε όλα τα deals
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-6 ">
        <div className="space-y-4">
          {deals.map((deal) => (
            <ContextMenu key={deal.id}>
              <ContextMenuTrigger asChild>
                <div
                  className={`flex flex-col md:flex-row md:items-center justify-between p-4 border border-${
                    deal.status === DealStatus.Active ? "green" : "gray"
                  } rounded-lg space-y-4 md:space-y-0 hover:bg-tertiary/10 transition-all duration-300 shadow-sm hover:shadow cursor-context-menu`}
                  onClick={() => navigate(`/deals/${deal.id}`)}
                >
                  <div className="flex items-center space-x-4">
                    {/* Square container with deal ID */}
                    <div className="h-12 w-12 flex items-center justify-center rounded-lg bg-primary/10 text-primary font-bold">
                      {deal.id.substring(0, 5)}
                    </div>

                    <div>
                      <p className="text-sm font-bold text-primary">
                        {deal.name}{" "}
                      </p>
                      <p className="text-md text-primary">
                        {deal.client_father_name} & {deal.client_mother_name} -{" "}
                        {deal.candidate_name} {deal.candidate_surname}
                      </p>
                      <p className="text-sm text-secondary">{deal.deal_date}</p>
                    </div>
                  </div>

                  <div className="text-left md:text-right">
                    <p className="text-sm font-bold text-primary mt-2">
                      {deal.revenue.toFixed(2)} €
                    </p>
                    <p className="text-sm font-bold text-secondary ">
                      {(deal.revenue - deal.candidate_salary).toFixed(2)} €
                    </p>
                    <span className="text-sm text-secondary">
                      {deal.status}
                    </span>
                  </div>
                </div>
              </ContextMenuTrigger>
              <ContextMenuContent>
                <ContextMenuItem
                  onClick={() => navigate(`/crm/client/${deal.client_id}`)}
                  className="focus:bg-primary/10 focus:text-primary"
                >
                  <Users className="h-4 w-4 mr-2" />
                  <span>Προφίλ Πελάτη</span>
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => navigate(`/candidates/${deal.candidate_id}`)}
                  className="focus:bg-primary/10 focus:text-primary"
                >
                  <User className="h-4 w-4 mr-2" />
                  <span>Προφίλ Υποψηφίου</span>
                </ContextMenuItem>
                <ContextMenuItem
                  onClick={() => navigate(`/deals/${deal.id}`)}
                  className="focus:bg-primary/10 focus:text-primary"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  <span>Επεξεργασία Deal</span>
                </ContextMenuItem>
                {deal.contract_url && (
                  <>
                    <ContextMenuItem
                      onClick={() => handleFilePreview(deal.contract_url || "")}
                      className="focus:bg-primary/10 focus:text-primary"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      <span>Προβολή Αρχείου</span>
                    </ContextMenuItem>
                    <ContextMenuItem
                      onClick={() =>
                        handleFileDownload(deal.contract_url || "")
                      }
                      className="focus:bg-primary/10 focus:text-primary"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      <span>Λήψη Αρχείου</span>
                    </ContextMenuItem>
                  </>
                )}
              </ContextMenuContent>
            </ContextMenu>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default DataTable;
