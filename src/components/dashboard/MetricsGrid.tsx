import { Card, CardContent } from "@/components/ui/card";
import { CreditCard, FileText, TrendingUp, Users } from "lucide-react";
import { useNavigate } from "react-router";

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  isPositive?: boolean;
  onClick?: () => void;
}

const MetricCard = ({
  title,
  value,
  change,
  icon,
  isPositive = true,
  onClick,
}: MetricCardProps) => (
  <Card
    variant="accent"
    gradient
    hoverEffect
    onClick={onClick}
    className={onClick ? "cursor-pointer" : ""}
  >
    <CardContent className="flex items-center justify-between p-6">
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">{title}</p>
        <h2 className="text-2xl font-bold text-primary">{value}</h2>
        <p
          className={`text-sm ${
            isPositive ? "text-secondary" : "text-destructive"
          }`}
        >
          {change}
        </p>
      </div>
      <div className="p-3 bg-primary/10 rounded-full text-primary">{icon}</div>
    </CardContent>
  </Card>
);

interface MetricsGridProps {
  totalRevenue?: string;
  revenueChange?: string;
  isRevenuePositive?: boolean;
  totalProfit?: string;
  profitChange?: string;
  isProfitPositive?: boolean;
  totalCustomers?: string;
  customerChange?: string;
  isCustomerChangePositive?: boolean;
  totalDeals?: string;
  dealsChange?: string;
  isDealsChangePositive?: boolean;
}

const MetricsGrid = ({
  totalRevenue = "€45,231.89",
  revenueChange = "+20.1% από τον προηγούμενο μήνα",
  isRevenuePositive = true,
  totalProfit = "€20,105.45",
  profitChange = "+18.7% από τον προηγούμενο μήνα",
  isProfitPositive = true,
  totalCustomers = "2350",
  customerChange = "+180.1% από τον προηγούμενο μήνα",
  isCustomerChangePositive = true,
  totalDeals = "573",
  dealsChange = "+20.1% τον προηγούμενο μήνα",
  isDealsChangePositive = true,
}: MetricsGridProps) => {
  const navigate = useNavigate();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-transparent">
      <MetricCard
        title="Συνολικό Revenue"
        value={totalRevenue}
        change={revenueChange}
        icon={<TrendingUp className="h-5 w-5" />}
        isPositive={isRevenuePositive}
      />
      <MetricCard
        title="Καθαρό Κέρδος"
        value={totalProfit}
        change={profitChange}
        icon={<CreditCard className="h-5 w-5" />}
        isPositive={isProfitPositive}
      />
      <MetricCard
        onClick={() => navigate("/crm")}
        title="Σύνολο Πελατών"
        value={totalCustomers}
        change={customerChange}
        icon={<Users className="h-5 w-5" />}
        isPositive={isCustomerChangePositive}
      />
      <MetricCard
        onClick={() => navigate("/deals")}
        title="Deals"
        value={totalDeals}
        change={dealsChange}
        icon={<FileText className="h-5 w-5" />}
        isPositive={isDealsChangePositive}
      />
    </div>
  );
};

export default MetricsGrid;
