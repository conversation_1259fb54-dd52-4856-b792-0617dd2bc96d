import { usePermissions } from "@/contexts/PermissionsContext";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { routePermissions, RoutePermissionType } from "@/lib/routePermissions";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";

/**
 * Component to display the user's permissions
 * Useful for debugging and testing permissions
 */
const PermissionsDisplay = () => {
  const { permissions, isLoading } = usePermissions();
  const { canView, canCreate, canEdit, canDelete } = usePermissionCheck();

  if (isLoading) {
    return <div>Loading permissions...</div>;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Your Permissions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4 p-4 bg-gray-100 rounded-md">
          <h3 className="text-md font-semibold mb-2">Debug Information</h3>
          <p className="text-sm mb-1">
            Raw Permission IDs: {permissions.join(", ")}
          </p>
          <p className="text-sm">Total Permissions: {permissions.length}</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {routePermissions
            .filter(
              (page) => page.type === RoutePermissionType.REQUIRES_PERMISSION
            )
            .map((page) => (
              <Card key={page.path} className="overflow-hidden">
                <CardHeader className="bg-primary/10 p-3">
                  <CardTitle className="text-lg">{page.label}</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2">
                      <span
                        className={`w-4 h-4 rounded-full ${
                          canView(page.permissionPage)
                            ? "bg-green-500"
                            : "bg-red-500"
                        }`}
                      ></span>
                      <span>View</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <span
                        className={`w-4 h-4 rounded-full ${
                          canCreate(page.permissionPage)
                            ? "bg-green-500"
                            : "bg-red-500"
                        }`}
                      ></span>
                      <span>Create</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <span
                        className={`w-4 h-4 rounded-full ${
                          canEdit(page.permissionPage)
                            ? "bg-green-500"
                            : "bg-red-500"
                        }`}
                      ></span>
                      <span>Edit</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <span
                        className={`w-4 h-4 rounded-full ${
                          canDelete(page.permissionPage)
                            ? "bg-green-500"
                            : "bg-red-500"
                        }`}
                      ></span>
                      <span>Delete</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default PermissionsDisplay;
