import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>A<PERSON>s,
  <PERSON>Axis,
} from "recharts";

interface RevenueChartProps {
  data?: Array<{
    name: string;
    value: number;
  }>;
}

const defaultData = [
  { name: "Ιαν", value: 4500 },
  { name: "Φεβ", value: 1500 },
  { name: "Μαρ", value: 3200 },
  { name: "Απρ", value: 3300 },
  { name: "Μαϊ", value: 3400 },
  { name: "Ιουν", value: 2000 },
  { name: "Ιουλ", value: 1800 },
  { name: "Αυγ", value: 3800 },
  { name: "Σεπτ", value: 3300 },
  { name: "Οκτ", value: 3900 },
  { name: "Νοε", value: 4200 },
  { name: "Δεκ", value: 4500 },
];

const RevenueChart = ({ data = defaultData }: RevenueChartProps) => {
  return (
    <Card className="w-full" variant="accent" gradient hoverEffect>
      <CardHeader className="border-b border-primary/20 pb-4">
        <CardTitle className="text-primary text-2xl">
          Revenue ανά μήνα
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-50" />
              <XAxis
                dataKey="name"
                fontSize={12}
                tickMargin={10}
                stroke="hsl(var(--primary))"
              />
              <YAxis
                fontSize={12}
                tickMargin={10}
                stroke="hsl(var(--primary))"
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "hsl(var(--light))",
                  border: "1px solid hsl(var(--primary))",
                  borderRadius: "8px",
                  boxShadow:
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                }}
                formatter={(value: number) => [`€${value}`, "Revenue"]}
                labelStyle={{
                  color: "hsl(var(--primary))",
                  fontWeight: "bold",
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="url(#colorGradient)"
                strokeWidth={3}
                dot={false}
                activeDot={{
                  r: 8,
                  fill: "hsl(var(--primary))",
                  strokeWidth: 2,
                  stroke: "#FFFFFF",
                }}
              />
              <defs>
                <linearGradient id="colorGradient" x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor="hsl(var(--primary))" />
                  <stop offset="100%" stopColor="hsl(var(--secondary))" />
                </linearGradient>
              </defs>
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default RevenueChart;
