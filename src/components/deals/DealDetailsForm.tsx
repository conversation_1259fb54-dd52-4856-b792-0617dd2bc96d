import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { cn, getFilePreviewUrl, uploadFileToSupabase } from "@/lib/utils";
import { Deal, DealStatus } from "@/services/dealService";
import { format } from "date-fns";
import { el } from "date-fns/locale";
import {
  CalendarIcon,
  Download,
  Eye,
  FileText,
  Loader2,
  Save,
  Upload,
  X,
} from "lucide-react";
import { useRef, useState } from "react";
import { Badge } from "../ui/badge";

interface DealDetailsFormProps {
  deal: Deal;
  onSave: (updatedDeal: Deal) => Promise<void>;
  saving: boolean;
  setSaving: (saving: boolean) => void;
  onStatusChange: (newStatus: DealStatus) => void;
  disabled?: boolean;
}

const DealDetailsForm = ({
  deal,
  onSave,
  saving,
  setSaving,
  onStatusChange,
  disabled = false,
}: DealDetailsFormProps) => {
  const [formData, setFormData] = useState({
    name: deal.name,
    deal_date: deal.deal_date,
    revenue: deal.revenue.toString(),
    candidate_salary: deal.candidate_salary.toString(),
    status: deal.status,
  });
  const [contractFile, setContractFile] = useState<File | null>(null);

  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleDateChange = (date: Date | undefined) => {
    if (!date) return;

    // Format the date as YYYY-MM-DD to avoid timezone issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const formattedDate = `${year}-${month}-${day}`;

    setFormData((prev) => ({
      ...prev,
      deal_date: formattedDate,
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setContractFile(file);

      // Create a temporary preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Clean up the temporary URL when component unmounts
      return () => URL.revokeObjectURL(objectUrl);
    }
  };

  const handleRemoveFile = () => {
    setContractFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleViewContract = async () => {
    if (previewUrl) {
      window.open(previewUrl, "_blank");
    } else if (deal.contract_url) {
      setIsLoadingPreview(true);
      try {
        const url = await getFilePreviewUrl(
          "deal-documents",
          deal.contract_url
        );
        window.open(url, "_blank");
      } catch (error) {
        console.error("Error viewing contract:", error);
        toast({
          title: "Σφάλμα",
          description: "Δεν ήταν δυνατή η προβολή του συμβολαίου.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingPreview(false);
      }
    }
  };

  const handleDownloadContract = async () => {
    if (deal.contract_url) {
      setIsLoadingPreview(true);
      try {
        const url = await getFilePreviewUrl(
          "deal-documents",
          deal.contract_url
        );

        // Create a temporary link and trigger download
        const a = document.createElement("a");
        a.href = url;
        a.download = deal.contract_url.split("/").pop() || "contract";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        toast({
          title: "Επιτυχία",
          description: "Η λήψη του συμβολαίου ξεκίνησε.",
        });
      } catch (error) {
        console.error("Error downloading contract:", error);
        toast({
          title: "Σφάλμα",
          description: "Δεν ήταν δυνατή η λήψη του συμβολαίου.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingPreview(false);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Upload contract file if selected
      let contractPath = deal.contract_url;

      if (contractFile) {
        try {
          const uid = crypto.randomUUID();
          const filePath = `${uid}/${formData.name}.${contractFile.name
            .split(".")
            .pop()}`;
          contractPath = await uploadFileToSupabase(
            contractFile,
            "deal-documents",
            filePath,
            (progress) => {
              console.log("Upload progress:", progress);
            }
          );

          toast({
            title: "Επιτυχία",
            description: "Το συμβόλαιο ανέβηκε επιτυχώς.",
          });
        } catch (error) {
          console.error("Error uploading contract:", error);
          toast({
            title: "Σφάλμα",
            description: "Δεν ήταν δυνατό το ανέβασμα του συμβολαίου.",
            variant: "destructive",
          });
          throw error;
          // Continue with the save operation even if file upload fails
        }
      }

      const updatedDeal: Deal = {
        ...deal,
        name: formData.name,
        deal_date: formData.deal_date,
        revenue: parseFloat(formData.revenue),
        candidate_salary: parseFloat(formData.candidate_salary),
        status: formData.status as DealStatus,
        contract_url: contractPath,
      };

      await onSave(updatedDeal);

      // Reset file input after successful save
      if (contractFile) {
        setContractFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    } catch (error) {
      console.error("Error saving deal details:", error);
      toast({
        title: "Σφάλμα",
        description: "Σφάλμα κατά την αποθήκευση των αλλαγών.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label htmlFor="status">Κατάσταση</Label>
          <Badge
            variant={
              formData.status === DealStatus.Active
                ? "success"
                : formData.status === DealStatus.InProgress
                ? "outline"
                : formData.status === DealStatus.Rejected
                ? "destructive"
                : "secondary"
            }
          >
            {formData.status === DealStatus.Active
              ? "Ενεργό"
              : formData.status === DealStatus.InProgress
              ? "Σε Εξέλιξη"
              : formData.status === DealStatus.Rejected
              ? "Απορρίφθηκε"
              : "Ανενεργό"}
          </Badge>
        </div>
        <Select
          value={formData.status}
          onValueChange={(value) => {
            handleChange("status", value);
            onStatusChange(value as DealStatus);
          }}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue placeholder="Επιλέξτε κατάσταση" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={DealStatus.InProgress}>Σε Εξέλιξη</SelectItem>
            <SelectItem value={DealStatus.Active}>Ενεργό</SelectItem>
            <SelectItem value={DealStatus.Inactive}>Ανενεργό</SelectItem>
            <SelectItem value={DealStatus.Rejected}>Απορρίφθηκε</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="name">Όνομα Deal</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => handleChange("name", e.target.value)}
          disabled={disabled}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="deal_date">Ημερομηνία Έναρξης</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !formData.deal_date && "text-muted-foreground"
              )}
              disabled={disabled}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {formData.deal_date ? (
                format(new Date(formData.deal_date + "T00:00:00"), "PPP", {
                  locale: el,
                })
              ) : (
                <span>Επιλέξτε ημερομηνία</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={
                formData.deal_date
                  ? new Date(formData.deal_date + "T00:00:00")
                  : undefined
              }
              onSelect={handleDateChange}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="space-y-2">
        <Label htmlFor="revenue">Έσοδα (€)</Label>
        <Input
          id="revenue"
          type="number"
          value={formData.revenue}
          onChange={(e) => handleChange("revenue", e.target.value)}
          disabled={disabled}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="candidate_salary">Μισθός Υποψηφίου (€)</Label>
        <Input
          id="candidate_salary"
          type="number"
          value={formData.candidate_salary}
          onChange={(e) => handleChange("candidate_salary", e.target.value)}
          disabled={disabled}
        />
      </div>

      <div className="space-y-2">
        <Label>Στοιχεία Πελάτη</Label>
        <div className="p-3 bg-muted rounded-md">
          <p className="text-sm">
            <span className="font-medium">Πατέρας:</span>{" "}
            {deal.client_father_name || "Μη διαθέσιμο"}
          </p>
          <p className="text-sm">
            <span className="font-medium">Μητέρα:</span>{" "}
            {deal.client_mother_name || "Μη διαθέσιμο"}
          </p>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Στοιχεία Υποψηφίου</Label>
        <div className="p-3 bg-muted rounded-md">
          <p className="text-sm">
            <span className="font-medium">Όνομα:</span>{" "}
            {deal.candidate_name || "Μη διαθέσιμο"}
          </p>
          <p className="text-sm">
            <span className="font-medium">Επώνυμο:</span>{" "}
            {deal.candidate_surname || "Μη διαθέσιμο"}
          </p>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="contract">Συμβόλαιο</Label>
        <div className="flex flex-col space-y-2">
          {/* Hidden file input */}
          <input
            type="file"
            id="contract"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            accept=".pdf,.doc,.docx"
            disabled={disabled}
          />

          {/* Current contract display */}
          {(deal.contract_url || contractFile) && (
            <div className="p-3 bg-muted rounded-md flex items-center justify-between">
              <div className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-primary" />
                <span className="text-sm font-medium">
                  {contractFile
                    ? contractFile.name
                    : deal.contract_url?.split("/").pop() || "Συμβόλαιο"}
                </span>
              </div>
              <div className="flex space-x-2">
                {!contractFile && deal.contract_url && (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleViewContract}
                      disabled={isLoadingPreview || disabled}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Προβολή
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleDownloadContract}
                      disabled={isLoadingPreview || disabled}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Λήψη
                    </Button>
                  </>
                )}
                {contractFile && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleRemoveFile}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Αφαίρεση
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Upload button */}
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
            className="w-full"
          >
            <Upload className="h-4 w-4 mr-2" />
            {deal.contract_url || contractFile
              ? "Αλλαγή Συμβολαίου"
              : "Ανέβασμα Συμβολαίου"}
          </Button>
        </div>
      </div>

      <Button
        type="submit"
        className="w-full mt-6"
        disabled={saving || disabled}
        variant="default"
        size="lg"
      >
        {saving ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Αποθήκευση...
          </>
        ) : (
          <>
            <Save className="mr-2 h-4 w-4" />
            Αποθήκευση
          </>
        )}
      </Button>
      <p className="text-xs text-center text-muted-foreground mt-2">
        Αποθηκεύει τόσο τα στοιχεία του deal όσο και τις αλλαγές στο
        χρονοδιάγραμμα
      </p>
    </form>
  );
};

export default DealDetailsForm;
