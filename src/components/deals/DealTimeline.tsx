import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { Deal, History } from "@/services/dealService";
import { format } from "date-fns";
import { el } from "date-fns/locale";
import { CalendarIcon, Check, Clock, Edit, Save } from "lucide-react";
import { useState } from "react";

interface DealTimelineProps {
  deal: Deal;
  onSave: (updatedDeal: Deal) => Promise<void>;
  disabled?: boolean;
  onTimelineChange?: (hasChanges: boolean, updatedHistory: History) => void;
}

interface TimelineStep {
  key: keyof History;
  title: string;
  hasDate: boolean;
}

const timelineSteps: TimelineStep[] = [
  {
    key: "cv_and_price_list",
    title: "Βιογραφικό & Τιμοκατάλογος",
    hasDate: false,
  },
  {
    key: "contact_client_nanny",
    title: "Επικοινωνία Πελάτη-Νταντάς",
    hasDate: false,
  },
  {
    key: "appointment",
    title: "Ραντεβού",
    hasDate: true,
  },
  {
    key: "trial",
    title: "Δοκιμαστική Περίοδος",
    hasDate: true,
  },
  {
    key: "feedback_from_family",
    title: "Feedback από Οικογένεια",
    hasDate: false,
  },
  {
    key: "feedback_from_nanny",
    title: "Feedback από Νταντά",
    hasDate: false,
  },
];

const DealTimeline = ({
  deal,
  onSave,
  disabled = false,
  onTimelineChange,
}: DealTimelineProps) => {
  const [editingStep, setEditingStep] = useState<keyof History | null>(null);
  const [editedHistory, setEditedHistory] = useState<History>(deal.history);

  // State for managing the date-time picker popover
  const [editingDateTimeStepKey, setEditingDateTimeStepKey] = useState<
    keyof History | null
  >(null);
  const [tempDateTime, setTempDateTime] = useState<Date | undefined>(undefined);

  // Find the current active step (first incomplete step)
  const getCurrentStep = (): keyof History | null => {
    for (const step of timelineSteps) {
      const historyStep = deal.history[step.key];
      if (!historyStep.completed) {
        return step.key;
      }
    }
    return null; // All steps are completed
  };

  const currentStep = getCurrentStep();

  const handleEditStep = (step: keyof History) => {
    setEditingStep(step);
  };

  const handleSaveStep = () => {
    if (!editingStep) return;
    // Just exit edit mode, don't save to database
    setEditingStep(null);
  };

  const handleCommentChange = (step: keyof History, comment: string) => {
    const newHistory = {
      ...editedHistory,
      [step]: {
        ...editedHistory[step],
        comment,
      },
    };
    setEditedHistory(newHistory);

    // Notify parent component of changes
    if (onTimelineChange) {
      onTimelineChange(true, newHistory);
    }
  };

  const handleDateChange = (step: keyof History, date: Date | undefined) => {
    if (!date) return;

    const newHistory = {
      ...editedHistory,
      [step]: {
        ...editedHistory[step],
        datetime: date.toISOString(),
      },
    };
    setEditedHistory(newHistory);

    // Notify parent component of changes
    if (onTimelineChange) {
      onTimelineChange(true, newHistory);
    }
  };

  const handleToggleComplete = (step: keyof History) => {
    // Only update local state, don't save to database
    const newHistory = {
      ...editedHistory,
      [step]: {
        ...editedHistory[step],
        completed: !editedHistory[step].completed,
      },
    };
    setEditedHistory(newHistory);

    // Notify parent component of changes
    if (onTimelineChange) {
      onTimelineChange(true, newHistory);
    }
  };

  // Function to get the current edited history
  const getEditedHistory = (): History => {
    return editedHistory;
  };

  return (
    <div className="space-y-8">
      {disabled && (
        <div className="bg-muted p-4 rounded-md mb-4">
          <p className="text-muted-foreground">
            Το χρονοδιάγραμμα είναι διαθέσιμο μόνο για deals σε εξέλιξη. Αλλάξτε
            την κατάσταση του deal σε "Σε Εξέλιξη" για να επεξεργαστείτε το
            χρονοδιάγραμμα.
          </p>
        </div>
      )}

      {!disabled && (
        <div className="bg-muted/30 p-4 rounded-md mb-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div>
              <h3 className="font-medium text-primary">
                Επεξεργασία Χρονοδιαγράμματος
              </h3>
              <p className="text-sm text-muted-foreground mt-1">
                Οι αλλαγές θα αποθηκευτούν με το κουμπί "Αποθήκευση" στα
                στοιχεία του deal
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-9 top-0 bottom-0 w-0.5 bg-primary/20"></div>

        {/* Timeline steps */}
        <div className="space-y-8">
          {timelineSteps.map((step, index) => {
            const historyStep = editedHistory[step.key];
            const isCompleted = historyStep.completed;
            const isActive = currentStep === step.key;
            const isEditing = editingStep === step.key;

            return (
              <div key={step.key} className="relative">
                {/* Step indicator */}
                <div className="flex items-start">
                  <div
                    className={cn(
                      "z-10 flex items-center justify-center w-10 h-10 rounded-full border-2",
                      isCompleted
                        ? "bg-primary border-primary text-white"
                        : isActive
                        ? "bg-white border-primary text-primary"
                        : "bg-white border-muted-foreground/50 text-muted-foreground"
                    )}
                  >
                    {isCompleted ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <span>{index + 1}</span>
                    )}
                  </div>

                  <div className="ml-4 flex-1">
                    <div className="flex items-center justify-between">
                      <h3
                        className={cn(
                          "font-medium",
                          isCompleted
                            ? "text-primary"
                            : isActive
                            ? "text-primary"
                            : "text-muted-foreground"
                        )}
                      >
                        {step.title}
                      </h3>

                      <div className="flex items-center space-x-2">
                        {!disabled && (
                          <>
                            {isEditing ? (
                              <Button size="sm" onClick={handleSaveStep}>
                                <Save className="h-4 w-4 mr-1" />
                                Αποθήκευση
                              </Button>
                            ) : (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditStep(step.key)}
                              >
                                <Edit className="h-4 w-4 mr-1" />
                                Επεξεργασία
                              </Button>
                            )}

                            <Button
                              size="sm"
                              variant={isCompleted ? "beige" : "default"}
                              onClick={() => handleToggleComplete(step.key)}
                            >
                              {isCompleted ? "Αναίρεση" : "Ολοκλήρωση"}
                            </Button>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Date picker for steps with dates */}
                    {step.hasDate && (
                      <div className="mt-2">
                        <Popover
                          open={editingDateTimeStepKey === step.key}
                          onOpenChange={(isOpen) => {
                            if (isOpen) {
                              setEditingDateTimeStepKey(step.key);
                              const historyVal =
                                editedHistory[step.key].datetime;
                              let initialDate;
                              if (historyVal) {
                                initialDate = new Date(historyVal);
                              } else {
                                initialDate = new Date(); // Today
                                initialDate.setHours(9, 0, 0, 0); // Default to 09:00:00.000 local time
                              }
                              setTempDateTime(initialDate);
                            } else {
                              setEditingDateTimeStepKey(null);
                            }
                          }}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !historyStep.datetime && "text-muted-foreground"
                              )}
                              disabled={!isEditing || disabled}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {historyStep.datetime ? (
                                format(
                                  new Date(historyStep.datetime),
                                  "PPP HH:mm",
                                  {
                                    locale: el,
                                  }
                                )
                              ) : !isEditing ? (
                                <span className="text-muted-foreground/70">
                                  Δεν έχει οριστεί
                                </span>
                              ) : (
                                <span>Επιλέξτε ημερομηνία</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <div className="p-2">
                              <Calendar
                                locale={el}
                                mode="single"
                                selected={tempDateTime}
                                onSelect={(selectedDay) => {
                                  if (selectedDay) {
                                    setTempDateTime((currentTemp) => {
                                      const newDT = currentTemp
                                        ? new Date(currentTemp)
                                        : new Date();
                                      newDT.setFullYear(
                                        selectedDay.getFullYear(),
                                        selectedDay.getMonth(),
                                        selectedDay.getDate()
                                      );
                                      if (!currentTemp)
                                        newDT.setHours(9, 0, 0, 0); // Default time if was undefined
                                      return newDT;
                                    });
                                  }
                                }}
                                initialFocus
                                disabled={!isEditing || disabled}
                              />
                              {isEditing && tempDateTime && (
                                <div className="mt-3 p-2 border-t">
                                  <div className="flex items-center justify-between mb-2">
                                    <label
                                      htmlFor={`time-input-${step.key}`}
                                      className="text-sm font-medium text-muted-foreground flex items-center"
                                    >
                                      <Clock className="h-4 w-4 mr-2" />
                                      Ώρα
                                    </label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Input
                                      type="number"
                                      id={`hour-input-${step.key}`}
                                      min={0}
                                      max={23}
                                      value={tempDateTime
                                        .getHours()
                                        .toString()
                                        .padStart(2, "0")}
                                      onChange={(e) => {
                                        const hour = parseInt(
                                          e.target.value,
                                          10
                                        );
                                        if (
                                          !isNaN(hour) &&
                                          hour >= 0 &&
                                          hour <= 23
                                        ) {
                                          setTempDateTime((currentTemp) => {
                                            const newDT = new Date(
                                              currentTemp || new Date()
                                            );
                                            newDT.setHours(hour);
                                            return newDT;
                                          });
                                        }
                                      }}
                                      className="w-[60px] text-center"
                                    />
                                    <span>:</span>
                                    <Input
                                      type="number"
                                      id={`minute-input-${step.key}`}
                                      min={0}
                                      max={59}
                                      value={tempDateTime
                                        .getMinutes()
                                        .toString()
                                        .padStart(2, "0")}
                                      onChange={(e) => {
                                        const minute = parseInt(
                                          e.target.value,
                                          10
                                        );
                                        if (
                                          !isNaN(minute) &&
                                          minute >= 0 &&
                                          minute <= 59
                                        ) {
                                          setTempDateTime((currentTemp) => {
                                            const newDT = new Date(
                                              currentTemp || new Date()
                                            );
                                            newDT.setMinutes(minute);
                                            return newDT;
                                          });
                                        }
                                      }}
                                      className="w-[60px] text-center"
                                    />
                                  </div>
                                  <Button
                                    size="sm"
                                    className="w-full mt-3"
                                    onClick={() => {
                                      if (
                                        tempDateTime &&
                                        editingDateTimeStepKey
                                      ) {
                                        handleDateChange(
                                          editingDateTimeStepKey,
                                          tempDateTime
                                        );
                                        setEditingDateTimeStepKey(null); // Close popover
                                      }
                                    }}
                                  >
                                    Ορισμός Ημερομηνίας & Ώρας
                                  </Button>
                                </div>
                              )}
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    )}

                    {/* Comments */}
                    <div className="mt-2">
                      <Textarea
                        placeholder="Προσθέστε σχόλια..."
                        value={historyStep.comment || ""}
                        onChange={(e) =>
                          handleCommentChange(step.key, e.target.value)
                        }
                        disabled={!isEditing || disabled}
                        className="min-h-[80px]"
                      />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DealTimeline;
