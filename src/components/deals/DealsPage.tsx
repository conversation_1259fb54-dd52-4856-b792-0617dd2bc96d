import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useRoutePermissions } from "@/hooks/useRoutePermissions";
import {
  Deal,
  DealStatus,
  deleteDeal,
  fetchDealById,
  statusMap,
  updateDeal,
} from "@/services/dealService";
import {
  AlertCircle,
  ArrowLeft,
  Loader2,
  Trash2,
  User,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Alert, AlertDescription } from "../ui/alert";
import DealDetailsForm from "./DealDetailsForm";
import DealTimeline from "./DealTimeline";

const DealsPage = () => {
  const { id } = useParams<{ id: string }>();
  const [deal, setDeal] = useState<Deal | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<DealStatus | null>(
    deal?.status || null
  );
  const [timelineChanges, setTimelineChanges] = useState<{
    hasChanges: boolean;
    history: any;
  }>({
    hasChanges: false,
    history: null,
  });
  const { toast } = useToast();
  const navigate = useNavigate();
  const { canEditOnCurrentRoute, canDeleteOnCurrentRoute } =
    useRoutePermissions();

  useEffect(() => {
    const loadDeal = async () => {
      if (!id) return;

      setLoading(true);
      try {
        const dealData = await fetchDealById(id);
        if (dealData) {
          setDeal(dealData);
          setCurrentStatus(dealData.status);
          // Reset timeline changes when loading a new deal
          setTimelineChanges({
            hasChanges: false,
            history: null,
          });
          console.log("Deal status:", dealData.status);
        } else {
          toast({
            title: "Σφάλμα",
            description: "Το deal δεν βρέθηκε.",
            variant: "destructive",
          });
          navigate("/deals");
        }
      } catch (error) {
        console.error("Error loading deal:", error);
        toast({
          title: "Σφάλμα",
          description: "Σφάλμα κατά τη φόρτωση του deal.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadDeal();
  }, [id, navigate, toast]);

  const handleSave = async (deal: Deal) => {
    if (!deal) return;

    // Check if user has edit permission
    if (!canEditOnCurrentRoute()) {
      toast({
        title: "Άρνηση Πρόσβασης",
        description: "Δεν έχετε δικαίωμα επεξεργασίας deals.",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      // Create a schema-compatible object with timeline changes if they exist
      const schemaCompatibleDeal = {
        id: deal.id,
        name: deal.name,
        deal_date: deal.deal_date,
        revenue: deal.revenue,
        candidate_salary: deal.candidate_salary,
        client_id: deal.client_id,
        candidate_id: deal.candidate_id,
        contract_url: deal.contract_url,
        // Use timeline changes if they exist, otherwise use the deal history
        history: timelineChanges.hasChanges
          ? timelineChanges.history
          : deal.history,
        status: statusMap[currentStatus || deal.status],
      };

      const result = await updateDeal(deal.id, schemaCompatibleDeal);
      if (result) {
        console.log("Updated deal:", result);
        setDeal(result);

        // Reset timeline changes
        setTimelineChanges({
          hasChanges: false,
          history: null,
        });

        setCurrentStatus(result.status);
        console.log("Current status:", currentStatus);
        toast({
          title: "Επιτυχία",
          description: "Όλες οι αλλαγές αποθηκεύτηκαν επιτυχώς.",
          variant: "default",
        });
      } else {
        throw new Error("Failed to update deal");
      }
    } catch (error) {
      console.error("Error saving deal:", error);
      toast({
        title: "Σφάλμα",
        description: "Σφάλμα κατά την αποθήκευση των αλλαγών.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteDeal = async () => {
    if (!deal) return;

    // Check if user has delete permission
    if (!canDeleteOnCurrentRoute()) {
      toast({
        title: "Άρνηση Πρόσβασης",
        description: "Δεν έχετε δικαίωμα διαγραφής deals.",
        variant: "destructive",
      });
      setIsDeleteDialogOpen(false);
      return;
    }

    try {
      const success = await deleteDeal(deal.id);

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Το deal "${deal.name}" διαγράφηκε επιτυχώς.`,
          variant: "default",
        });

        // Navigate back to deals list
        navigate("/deals");
      } else {
        throw new Error("Failed to delete deal");
      }
    } catch (error) {
      console.error("Error deleting deal:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία διαγραφής του deal.",
        variant: "destructive",
      });
    } finally {
      // Close the dialog
      setIsDeleteDialogOpen(false);
    }
  };

  const navigateToClientProfile = () => {
    if (deal?.client_id) {
      navigate(`/crm/client/${deal.client_id}`);
    }
  };

  const navigateToCandidateProfile = () => {
    if (deal?.candidate_id) {
      navigate(`/candidates/${deal.candidate_id}`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!deal) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <h1 className="text-2xl font-bold mb-4">Το deal δεν βρέθηκε</h1>
        <Button onClick={() => navigate("/deals")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Επιστροφή στα Deals
        </Button>
      </div>
    );
  }

  // Check if user has edit permission
  const hasEditPermission = canEditOnCurrentRoute();
  // Check if user has delete permission
  const hasDeletePermission = canDeleteOnCurrentRoute();

  return (
    <div className="container mx-auto py-6 px-4 space-y-6">
      {!hasEditPermission && (
        <Alert variant="default" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Δεν έχετε δικαίωμα επεξεργασίας deals. Μπορείτε μόνο να δείτε τις
            πληροφορίες.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            onClick={() => navigate("/deals")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Επιστροφή στα Deals
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={navigateToClientProfile}
            className="mb-4"
          >
            <Users className="mr-2 h-4 w-4" />
            Προφίλ Πελάτη
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={navigateToCandidateProfile}
            className="mb-4"
          >
            <User className="mr-2 h-4 w-4" />
            Προφίλ Υποψηφίου
          </Button>
          {hasDeletePermission && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setIsDeleteDialogOpen(true)}
              className="mb-4"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Διαγραφή Deal
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Deal Details */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Στοιχεία Deal</CardTitle>
            </CardHeader>
            <CardContent>
              <DealDetailsForm
                deal={deal}
                onSave={handleSave}
                saving={saving}
                onStatusChange={(newStatus) => {
                  setCurrentStatus(newStatus);
                }}
                setSaving={setSaving}
                disabled={!hasEditPermission}
              />
            </CardContent>
          </Card>
        </div>

        {/* Timeline */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Χρονοδιάγραμμα Εξέλιξης</CardTitle>
            </CardHeader>
            <CardContent>
              <DealTimeline
                deal={deal}
                onSave={handleSave}
                disabled={
                  currentStatus !== DealStatus.InProgress || !hasEditPermission
                }
                onTimelineChange={(hasChanges, history) => {
                  setTimelineChanges({ hasChanges, history });
                }}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Διαγραφή Deal</AlertDialogTitle>
            <AlertDialogDescription>
              Είστε βέβαιοι ότι θέλετε να διαγράψετε το deal "{deal.name}"? Αυτή
              η ενέργεια δεν μπορεί να αναιρεθεί.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Άκυρο</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDeal}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Διαγραφή
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default DealsPage;
