import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const NotFound = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center">
      <div className="space-y-4 text-center">
        <h1 className="text-4xl font-bold">404</h1>
        <h2 className="text-2xl font-semibold">Σελίδα δεν βρέθηκε</h2>
        <p className="text-muted-foreground">
          Η σελίδα που ψάχνετε δεν υπάρχει ή έχει μετακινηθεί.
        </p>
        <Button
          onClick={() => navigate("/dashboard")}
          className="bg-primary hover:bg-primary/90 text-primary-foreground text-white font-bold py-2 px-4 rounded"
          variant="outline"
          size="lg"
        >
          Επιστροφή στην αρχική
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
