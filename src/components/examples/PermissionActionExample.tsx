import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useRoutePermissions } from "@/hooks/useRoutePermissions";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { useToast } from "@/components/ui/use-toast";
import { Edit, Plus, Trash } from "lucide-react";

/**
 * Example component that demonstrates how to use the permission checks
 * for edit, delete, and create actions
 */
const PermissionActionExample = () => {
  const {
    canCreateOnCurrentRoute,
    canEditOnCurrentRoute,
    canDeleteOnCurrentRoute,
    getCurrentPermissionPage,
  } = useRoutePermissions();
  const { canCreate, canEdit, canDelete } = usePermissionCheck();
  const { toast } = useToast();

  // Get the current permission page (e.g., "nannies", "crm", etc.)
  const permissionPage = getCurrentPermissionPage();

  // Handle create action
  const handleCreate = () => {
    if (canCreateOnCurrentRoute()) {
      // Perform create action
      toast({
        title: "Create Action",
        description: `You have permission to create on this page (${permissionPage}).`,
      });
    } else {
      toast({
        title: "Permission Denied",
        description: `You don't have permission to create on this page (${permissionPage}).`,
        variant: "destructive",
      });
    }
  };

  // Handle edit action
  const handleEdit = () => {
    if (canEditOnCurrentRoute()) {
      // Perform edit action
      toast({
        title: "Edit Action",
        description: `You have permission to edit on this page (${permissionPage}).`,
      });
    } else {
      toast({
        title: "Permission Denied",
        description: `You don't have permission to edit on this page (${permissionPage}).`,
        variant: "destructive",
      });
    }
  };

  // Handle delete action
  const handleDelete = () => {
    if (canDeleteOnCurrentRoute()) {
      // Perform delete action
      toast({
        title: "Delete Action",
        description: `You have permission to delete on this page (${permissionPage}).`,
      });
    } else {
      toast({
        title: "Permission Denied",
        description: `You don't have permission to delete on this page (${permissionPage}).`,
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="bg-primary/10">
        <CardTitle>Permission Actions Example</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          <p>
            This example demonstrates how to use the permission checks for edit,
            delete, and create actions on the current route.
          </p>

          <div className="flex flex-col gap-4 sm:flex-row">
            <Button
              onClick={handleCreate}
              disabled={!canCreateOnCurrentRoute()}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              <span>Create</span>
            </Button>

            <Button
              onClick={handleEdit}
              disabled={!canEditOnCurrentRoute()}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              <span>Edit</span>
            </Button>

            <Button
              onClick={handleDelete}
              disabled={!canDeleteOnCurrentRoute()}
              variant="destructive"
              className="flex items-center gap-2"
            >
              <Trash className="h-4 w-4" />
              <span>Delete</span>
            </Button>
          </div>

          {permissionPage && (
            <div className="mt-6 rounded-md bg-muted p-4">
              <h3 className="mb-2 font-medium">Current Page Permissions:</h3>
              <ul className="space-y-2">
                <li className="flex items-center gap-2">
                  <span
                    className={`h-3 w-3 rounded-full ${
                      canCreate(permissionPage) ? "bg-green-500" : "bg-red-500"
                    }`}
                  ></span>
                  <span>Create: {canCreate(permissionPage) ? "Yes" : "No"}</span>
                </li>
                <li className="flex items-center gap-2">
                  <span
                    className={`h-3 w-3 rounded-full ${
                      canEdit(permissionPage) ? "bg-green-500" : "bg-red-500"
                    }`}
                  ></span>
                  <span>Edit: {canEdit(permissionPage) ? "Yes" : "No"}</span>
                </li>
                <li className="flex items-center gap-2">
                  <span
                    className={`h-3 w-3 rounded-full ${
                      canDelete(permissionPage) ? "bg-green-500" : "bg-red-500"
                    }`}
                  ></span>
                  <span>Delete: {canDelete(permissionPage) ? "Yes" : "No"}</span>
                </li>
              </ul>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PermissionActionExample;
