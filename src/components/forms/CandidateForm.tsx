import { supabase } from "@/lib/supabase";
import apiClient from "../../lib/api";
import { uploadFileToSupabase, uploadMultipleFiles } from "../../lib/utils";
import { FormData, formSchema } from "../../schemas/FormSchema";
import { BaseFormWrapper } from "./BaseForm";
import { StepManager } from "./steps/leadRequest/StepManager";

// Default form values
const defaultValues: FormData = {
  name: "",
  surname: "",
  birthDate: "",
  contactNumber: "",
  gender: "Male",
  email: "",
  address: "",
  addressNumber: "",
  area: "",
  postalCode: "",
  city: "",
  country: "",
  nationality: "",
  workDocuments: "no",
  drivingLicense: "no",
  vehicle: "no",
  vehicleType: "",
  smoker: "no",
  firstAidCertification: [],
  experienceWithChildren: "no",
  experienceLearningDifficulties: "no",
  experienceSpecialNeeds: "no",
  references: "no",
  insurance: "no",
  languages: [],
  yearsExperience: "",
  childrenAgeExperience: [],
  positionInterests: [],
  scheduleInterests: [],
  durationInterests: [],
  startAvailability: "",
  placePreferences: "",
  personalProfile: "",
  education: [],
  educationTitles: [],
  candidateType: "nanny",
  musicalInstruments: [],
  musicTheory: [],
  lessonFormat: [],
  emergencyContactName: "",
  emergencyContactRelation: "",
  emergencyContactNumber: "",
  emergencyContactEmail: "",
  workExperience: [],
  referencesContacts: [],
  termsAccepted: false,
};

// File fields that need to be uploaded
const singleFileFields = [
  "profilePhoto",
  "introVideo",
  "idPassport",
  "drivingLicenseDocument",
  "criminalRecordDocument",
  "nannyCv",
];

const multipleFileFields = ["educationDocuments", "referenceLetters"];

// Handle file uploads
const handleFileUpload = async (
  files: Array<{ field: string; file: File | null }>,
  multipleFiles: Array<{ field: string; files: FileList | null }>,
  setUploadProgress: (progress: number) => void,
  email: string
) => {
  // Get the current session directly
  console.debug("Getting auth session...");
  let userId: string;

  try {
    // Get the current session directly
    const existingUser = await supabase.auth.getSession();
    const activeSession = existingUser.data.session;

    userId = activeSession?.user?.id;

    if (!userId) {
      console.error("No user ID found in session:", activeSession);
      throw new Error("Failed to get or create user");
    }
  } catch (error) {
    console.error("Error getting auth session:", error);
  }

  // Calculate progress weights for each file type
  const totalSingleFiles = files.length;
  const totalMultipleFiles = multipleFiles.length;
  const totalFiles = totalSingleFiles + totalMultipleFiles;

  // Weight for progress calculation
  const singleFileWeight = totalSingleFiles / totalFiles || 0;
  const multipleFileWeight = totalMultipleFiles / totalFiles || 0;
  const singleFilesWeight = singleFileWeight * totalSingleFiles;

  const uploadedFiles: Record<string, string | string[]> = {};
  uploadedFiles["storage_id"] = userId;

  // Upload single files
  for (const item of files) {
    try {
      if (!item.file) continue;

      // Create a consistent path format for files
      const fileExt = item.file.name.split(".").pop();
      const filePath = `${userId}/${item.field}.${fileExt}`;

      const storagePath = await uploadFileToSupabase(
        item.file,
        "candidate-documents",
        filePath,
        (progress) => {
          // Calculate overall progress including this file
          const currentFileIndex = files.indexOf(item);
          const previousFilesProgress =
            currentFileIndex * singleFileWeight * 100;
          setUploadProgress(
            Math.round(previousFilesProgress + progress * singleFileWeight)
          );
        }
      );

      // Store the storage path in the uploaded files object
      uploadedFiles[item.field] = storagePath;
    } catch (error) {
      console.error(`Error uploading ${item.field}:`, error);
      throw new Error(`Failed to upload ${item.field}`);
    }
  }

  // Upload multiple files
  for (const item of multipleFiles) {
    try {
      if (!item.files || item.files.length === 0) continue;

      const storagePaths = await uploadMultipleFiles(
        item.files,
        "candidate-documents",
        `${userId}/${item.field}`,
        (progress: number) => {
          // Calculate overall progress including this file
          const currentMultiFileIndex = multipleFiles.indexOf(item);
          const previousMultiFilesProgress =
            currentMultiFileIndex * multipleFileWeight * 100;
          setUploadProgress(
            Math.round(
              singleFilesWeight * 100 +
              previousMultiFilesProgress +
              progress * multipleFileWeight
            )
          );
        }
      );

      console.log(
        `Multiple files uploaded successfully for ${item.field}:`,
        storagePaths
      );

      // Store the array of storage paths
      uploadedFiles[item.field] = storagePaths;
      console.debug(`Added storage paths for ${item.field}:`, storagePaths);
    } catch (error) {
      console.error(`Error uploading ${item.field}:`, error);
      throw new Error(`Failed to upload ${item.field}`);
    }
  }

  return uploadedFiles;
};

// Handle form submission
const handleSubmit = async (
  formData: FormData,
  uploadedFiles?: Record<string, string | string[]>
) => {
  // Merge form data with uploaded files
  const formDataForSubmission = {
    ...formData,
    ...uploadedFiles,
    level: "junior",
  };

  // Send form data to the API
  const response = await apiClient.post("/candidates/create-candidate", {
    form_data: formDataForSubmission,
  });
};

// Export the CandidateForm component
export default function CandidateForm() {
  return (
    <BaseFormWrapper
      formSchema={formSchema}
      defaultValues={defaultValues}
      onSubmit={handleSubmit}
      title="Αίτηση Υποψηφίου / Candidate Application Form"
      isCandidateForm={true}
      formType="candidate"
      localStorageKey="candidateFormData"
      fileFields={singleFileFields}
      multipleFileFields={multipleFileFields}
      uploadHandler={handleFileUpload}
    >
      <StepManager />
    </BaseFormWrapper>
  );
}
