import { supabase } from "@/lib/supabase";
import { z } from "zod";
import apiClient from "../../lib/api";
import { uploadFileToSupabase, uploadMultipleFiles } from "../../lib/utils";
import { nannyRequestFormSchema } from "../../schemas/FormSchema";
import { toast } from "../ui/use-toast";
import { BaseFormWrapper } from "./BaseForm";
import { NannyStepManager } from "./steps/nannyRequest/NannyStepManager";

// Default form values
const defaultValues = {
  fatherName: "",
  motherName: "",
  contactNumber: "",
  email: "",
  permanentAddress: "",
  city: "",
  country: "",
  workingAddressSameAsPermanent: false,
  workingAddress: "",
  childrenCount: "",
  childrenAge: "",
  positionType: "live-in",
  positionDuration: "long-term",
  scheduleType: "full-time",
  daysPerWeek: "",
  hoursPerDay: "",
  salaryType: "monthly",
  salaryAmount: "",
  salaryCurrency: "EUR",
  positionInterests: [],
  specializationPreferences: [],
  insurance: "yes",
  insuranceType: "ergosimo",
  startDate: "flexible",
  candidateAge: "",
  workingLevel: "basic",
  smoker: "no",
  drivingLicense: "dont-mind",
  languages: [],
  additionalRequirements: "",
  documentUpload: null,
};

// File fields that need to be uploaded
const singleFileFields = ["documentUpload"];
const multipleFileFields: string[] = [];

// Handle file uploads
const handleFileUpload = async (
  files: Array<{ field: string; file: File | null }>,
  multipleFiles: Array<{ field: string; files: FileList | null }>,
  setUploadProgress: (progress: number) => void,
  email: string
) => {
  let userId: string;

  try {
    const session = await supabase.auth.getSession();
    userId = session.data.session?.user?.id;

    if (!userId) {
      throw new Error("No user ID found in session: " + session);
    }
  } catch (error) {
    console.error("Error getting auth session:", error);

    toast({
      title: "Σφάλμα",
      description:
        "Ο χρήστης δεν μπορεί να ανεβάσει αρχεία. Παρακαλώ επικοινωνήστε μαζί μας.",
      variant: "destructive",
    });
    throw error;
  }

  const uploadedFiles: Record<string, string | string[]> = {};
  uploadedFiles["storage_id"] = userId;

  // Upload single files
  for (const item of files) {
    try {
      if (!item.file) continue;

      // Create a consistent path format for files
      const fileExt = item.file.name.split(".").pop();
      const filePath = `${userId}/${item.field}.${fileExt}`;

      // Upload the file and get the storage path (not public URL)
      const storagePath = await uploadFileToSupabase(
        item.file,
        "client-documents",
        filePath,
        (progress) => {
          setUploadProgress(progress);
        }
      );

      // Store the storage path in the uploaded files object
      uploadedFiles[item.field] = storagePath;
    } catch (error) {
      console.error(`Error uploading ${item.field}:`, error);
      throw new Error(`Failed to upload ${item.field}`);
    }
  }

  // Handle multiple file uploads if needed
  for (const item of multipleFiles) {
    try {
      if (!item.files || item.files.length === 0) continue;

      // Upload multiple files and get array of storage paths
      const storagePaths = await uploadMultipleFiles(
        item.files,
        "client-documents",
        `${userId}/${item.field}`,
        (progress) => {
          setUploadProgress(progress);
        }
      );

      // Store the array of storage paths
      uploadedFiles[item.field] = storagePaths;
    } catch (error) {
      console.error(`Error uploading multiple files for ${item.field}:`, error);
      throw new Error(`Failed to upload multiple files for ${item.field}`);
    }
  }

  return uploadedFiles;
};

// Handle form submission
const handleSubmit = async (
  formData: z.infer<typeof nannyRequestFormSchema>,
  uploadedFiles?: Record<string, string | string[]>
) => {
  // Merge form data with uploaded files
  // Note: BaseForm now ensures storage_id is included in formData
  const formDataForSubmission = { ...formData, ...uploadedFiles };

  // Send form data to the API
  const response = await apiClient.post("/clients/create-client", {
    form_data: formDataForSubmission,
  });

  console.debug("Form submitted successfully:", response.data);
};

// Export the NannyRequestForm component
export default function NannyRequestForm() {
  return (
    <BaseFormWrapper
      formSchema={nannyRequestFormSchema}
      defaultValues={defaultValues as any}
      onSubmit={handleSubmit}
      title="Φόρμα Αίτησης Nanny / Nanny Request Form"
      isCandidateForm={false}
      formType="nannyRequest"
      localStorageKey="nannyRequestFormData"
      fileFields={singleFileFields}
      multipleFileFields={multipleFileFields}
      uploadHandler={handleFileUpload}
    >
      <NannyStepManager />
    </BaseFormWrapper>
  );
}
