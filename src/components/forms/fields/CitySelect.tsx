import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cities, getLocalizedLabel } from "@/lib/staticData";
import { getText } from "@/utils/form-utils";
import { UseFormReturn } from "react-hook-form";

interface CitySelectProps {
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  language: "el" | "en";
  required?: boolean;
}

export function CitySelect({ form, name, label, language, required = false }: CitySelectProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            {label || getText("Πόλη / City", language)}
            {required && <span className="text-destructive ml-1">*</span>}
          </FormLabel>
          <FormControl>
            <Select
              value={field.value}
              onValueChange={field.onChange}
              defaultValue={field.value}
            >
              <SelectTrigger>
                <SelectValue placeholder={getText("Επιλέξτε πόλη / Select city", language)} />
              </SelectTrigger>
              <SelectContent>
                {cities.map((city) => (
                  <SelectItem key={city.id} value={city.id}>
                    {getLocalizedLabel(city, language)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage lang={language} />
        </FormItem>
      )}
    />
  );
}
