import { Button } from "@/components/ui/button";
import { FormDescription } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getLocalizedLabel, languageLevels, languages } from "@/lib/staticData";
import { getText } from "@/utils/form-utils";

interface LanguageSelectProps {
  language: "el" | "en";
  languages: { language: string; level: string }[];
  setLanguages: React.Dispatch<
    React.SetStateAction<{ language: string; level: string }[]>
  >;
}

export function LanguageSelect({
  language,
  languages: selectedLanguages,
  setLanguages,
}: LanguageSelectProps) {
  // Add a new language entry
  const addLanguage = () => {
    setLanguages([...selectedLanguages, { language: "", level: "" }]);
  };

  // Remove a language entry
  const removeLanguage = (index: number) => {
    const newLanguages = [...selectedLanguages];
    newLanguages.splice(index, 1);
    setLanguages(newLanguages);
  };

  // Update a language entry
  const updateLanguage = (
    index: number,
    field: "language" | "level",
    value: string
  ) => {
    const newLanguages = [...selectedLanguages];
    newLanguages[index][field] = value;
    setLanguages(newLanguages);
  };

  return (
    <div>
      <FormDescription>
        {getText(
          "Προσθέστε τις γλώσσες που γνωρίζετε και το επίπεδό σας / Add languages you know and your proficiency level",
          language
        )}
      </FormDescription>
      {selectedLanguages.map((lang, index) => (
        <div key={index} className="flex gap-2 mb-2">
          <Select
            value={lang.language}
            onValueChange={(value) => updateLanguage(index, "language", value)}
          >
            <SelectTrigger className="flex-1">
              <SelectValue
                placeholder={getText("Γλώσσα / Language", language)}
              />
            </SelectTrigger>
            <SelectContent>
              {languages.map((languageOption) => (
                <SelectItem key={languageOption.id} value={languageOption.id}>
                  {getLocalizedLabel(languageOption, language)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={lang.level}
            onValueChange={(value) => updateLanguage(index, "level", value)}
          >
            <SelectTrigger className="flex-1">
              <SelectValue placeholder={getText("Επίπεδο / Level", language)} />
            </SelectTrigger>
            <SelectContent>
              {languageLevels.map((level) => (
                <SelectItem key={level.id} value={level.id}>
                  {getLocalizedLabel(level, language)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {index > 0 && (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              onClick={() => removeLanguage(index)}
            >
              ✕
            </Button>
          )}
        </div>
      ))}
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={addLanguage}
        className="mt-2"
      >
        {getText("Προσθήκη Γλώσσας / Add Language", language)}
      </Button>
    </div>
  );
}
