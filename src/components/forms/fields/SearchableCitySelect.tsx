import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { getLocalized<PERSON>abel } from "@/lib/staticData";
import { cn } from "@/lib/utils";
import { City, fetchCities } from "@/services/cityService";
import { getText } from "@/utils/form-utils";
import { Check, ChevronsUpDown, MapPin, Search } from "lucide-react";
import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { Button } from "../../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "../../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";

interface SearchableCitySelectProps {
  form: UseFormReturn<any>;
  name: string;
  label?: string;
  language: "el" | "en";
  required?: boolean;
}

// Helper function to normalize text for Greek character search
const normalizeText = (text: string): string => {
  if (!text) return "";

  // Convert to lowercase first
  const lowerText = text.toLowerCase();

  // Map of Greek characters with accents to their non-accented versions
  const greekAccentMap: Record<string, string> = {
    ά: "α",
    έ: "ε",
    ή: "η",
    ί: "ι",
    ό: "ο",
    ύ: "υ",
    ώ: "ω",
    ϊ: "ι",
    ϋ: "υ",
    ΐ: "ι",
    ΰ: "υ",
    Ά: "α",
    Έ: "ε",
    Ή: "η",
    Ί: "ι",
    Ό: "ο",
    Ύ: "υ",
    Ώ: "ω",
    Ϊ: "ι",
    Ϋ: "υ",
  };

  // Replace each accented character with its non-accented version
  return Array.from(lowerText)
    .map((char) => greekAccentMap[char] || char)
    .join("");
};

export function SearchableCitySelect({
  form,
  name,
  label,
  language,
  required = false,
}: SearchableCitySelectProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [cities, setCities] = useState<City[]>([]);
  const [filteredCities, setFilteredCities] = useState<City[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch cities from Supabase
  useEffect(() => {
    const loadCities = async () => {
      try {
        setIsLoading(true);
        const citiesData = await fetchCities();
        setCities(citiesData);
        setFilteredCities(citiesData);
      } catch (error) {
        console.error("Error loading cities:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCities();
  }, []);

  // Filter cities based on search input
  useEffect(() => {
    if (!searchValue) {
      setFilteredCities(cities);
      return;
    }

    // Normalize the search value to handle Greek characters properly
    const normalizedSearch = normalizeText(searchValue);

    const filtered = cities.filter((city: City) => {
      // Normalize city names for better matching with Greek characters
      const cityNameEl = normalizeText(city.labelEl);
      const cityNameEn = normalizeText(city.labelEn);
      const countryName = city.country ? normalizeText(city.country) : "";

      // Direct match with the full search term
      if (
        cityNameEl.includes(normalizedSearch) ||
        cityNameEn.includes(normalizedSearch) ||
        countryName.includes(normalizedSearch)
      ) {
        return true;
      }

      // Also check for partial word matches
      const searchWords = normalizedSearch
        .split(/\s+/)
        .filter((w) => w.length > 0);

      // If no valid search words, return false
      if (searchWords.length === 0) return false;

      // Check if all search words are found in either Greek or English city name or country
      return searchWords.every(
        (word) =>
          cityNameEl.includes(word) ||
          cityNameEn.includes(word) ||
          countryName.includes(word)
      );
    });

    console.debug("Filtered cities:", filtered.length, "out of", cities.length);
    setFilteredCities(filtered);
  }, [searchValue, cities]);

  // Group cities by country
  const groupedCities = filteredCities.reduce<Record<string, City[]>>(
    (acc, city) => {
      const country = city.country || "Other";
      if (!acc[country]) {
        acc[country] = [];
      }
      acc[country].push(city);
      return acc;
    },
    {}
  );

  // Sort countries alphabetically
  const sortedCountries = Object.keys(groupedCities).sort();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>
            {label || getText("Πόλη / City", language)}
            {required && <span className="text-destructive ml-1">*</span>}
          </FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn(
                    "w-full justify-between rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  <div className="flex items-center">
                    {field.value ? (
                      <>
                        <MapPin className="mr-2 h-4 w-4 shrink-0 text-primary" />
                        {isLoading ? (
                          <span className="flex items-center">
                            <div className="animate-spin mr-2">
                              <svg
                                className="h-4 w-4 text-primary"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                            </div>
                            {field.value}
                          </span>
                        ) : (
                          getLocalizedLabel(
                            cities.find((city) => city.id === field.value) || {
                              labelEl: field.value,
                              labelEn: field.value,
                            },
                            language
                          )
                        )}
                      </>
                    ) : (
                      <span>
                        {getText("Επιλέξτε πόλη / Select city", language)}
                      </span>
                    )}
                  </div>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-[300px] p-0" align="start">
              <Command>
                <div className="flex items-center border-b px-3">
                  <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                  {/* Use a regular input as a fallback for Greek character input */}
                  <input
                    type="text"
                    placeholder={getText(
                      "Αναζήτηση πόλης / Search city",
                      language
                    )}
                    value={searchValue}
                    onChange={(e) => {
                      setSearchValue(e.target.value);
                    }}
                    className="flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                    lang={language === "el" ? "el" : "en"}
                    autoComplete="off"
                    autoCorrect="off"
                    spellCheck="false"
                  />
                </div>
                <CommandList>
                  {isLoading ? (
                    <div className="flex items-center justify-center py-6">
                      <div className="flex flex-col items-center gap-2">
                        <div className="animate-spin">
                          <svg
                            className="h-6 w-6 text-primary"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {getText("Φόρτωση πόλεων / Loading cities", language)}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <>
                      <CommandEmpty>
                        {getText(
                          "Δεν βρέθηκαν πόλεις / No cities found",
                          language
                        )}
                      </CommandEmpty>
                      {sortedCountries.map((country) => (
                        <CommandGroup key={country} heading={country}>
                          {groupedCities[country].map((city: City) => (
                            <CommandItem
                              key={city.id}
                              value={city.id}
                              onSelect={() => {
                                form.setValue(name, city.id);
                                setOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  field.value === city.id
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                              {getLocalizedLabel(city, language)}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      ))}
                    </>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage lang={language} />
        </FormItem>
      )}
    />
  );
}
