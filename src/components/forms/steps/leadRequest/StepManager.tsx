import React from "react";
import { useCandidateFormContext } from "../../../../contexts/CandidateFormContext";
import { CareerProfileStep } from "./CareerProfileStep";
import { EmergencyDocsStep } from "./EmergencyDocsStep";
import { PersonalInfoStep } from "./PersonalInfoStep";
export const StepManager: React.FC = () => {
  const { currentStep } = useCandidateFormContext();

  // Render the appropriate step based on currentStep
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <PersonalInfoStep />;
      case 1:
        return <CareerProfileStep />;
      case 2:
        return <EmergencyDocsStep />;
      default:
        return <PersonalInfoStep />;
    }
  };

  return <div className="mt-6">{renderStep()}</div>;
};
