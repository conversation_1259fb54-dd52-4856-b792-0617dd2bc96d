import React from "react";
import { useFormContext as useRHFFormContext } from "react-hook-form";
import { z } from "zod";
import { useNannyRequestFormContext } from "../../../../contexts/NannyRequestFormContext";
import { nannyRequestFormSchema } from "../../../../schemas/FormSchema";
import { getText } from "../../../../utils/form-utils";
import { Card, CardContent } from "../../../ui/card";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import { Input } from "../../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../ui/select";
import { Textarea } from "../../../ui/textarea";
import { LanguageSelect } from "../../fields/LanguageSelect";

export const NannyPreferencesStep: React.FC = () => {
  const { language, languages, setLanguages } = useNannyRequestFormContext();
  const form = useRHFFormContext<z.infer<typeof nannyRequestFormSchema>>();

  // Language handlers are now handled by the LanguageSelect component

  return (
    <>
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h3 className="text-xl font-semibold mb-4">
            {getText(
              "Προτιμήσεις για Nanny / Preferences for the perfect candidate",
              language
            )}
          </h3>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="candidateAge"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{getText("Ηλικία / Age", language)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText("π.χ. 25-40 / e.g. 25-40", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="workingLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Επίπεδο / Working level", language)}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="junior">Junior</SelectItem>
                      <SelectItem value="basic">Basic</SelectItem>
                      <SelectItem value="vip">VIP</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="smoker"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Καπνίστρια / Smoker", language)}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="no">
                        {getText("Όχι / No", language)}
                      </SelectItem>
                      <SelectItem value="dont-mind">
                        {getText("Αδιάφορο / We don't mind", language)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="drivingLicense"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Δίπλωμα Οδήγησης / Driving license", language)}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">
                        {getText("Ναι / Yes", language)}
                      </SelectItem>
                      <SelectItem value="dont-mind">
                        {getText("Αδιάφορο / We don't mind", language)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="vehicle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{getText("Όχημα / Vehicle", language)}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">
                        {getText("Ναι / Yes", language)}
                      </SelectItem>
                      <SelectItem value="dont-mind">
                        {getText("Αδιάφορο / We don't mind", language)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="firstAid"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Πρώτες Βοήθειες / First Aid Certification",
                      language
                    )}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">
                        {getText("Ναι / Yes", language)}
                      </SelectItem>
                      <SelectItem value="dont-mind">
                        {getText("Αδιάφορο / We don't mind", language)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="learningDifficulties"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Εμπειρία με παιδιά με μαθησιακές δυσκολίες / Would you like a nanny who is experienced in children with learning difficulties?",
                      language
                    )}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">
                        {getText("Ναι / Yes", language)}
                      </SelectItem>
                      <SelectItem value="dont-mind">
                        {getText("Αδιάφορο / We don't mind", language)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="specialNeeds"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Εμπειρία με παιδιά με ειδικές ανάγκες / Would you like a nanny who is experienced in children with special needs?",
                      language
                    )}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">
                        {getText("Ναι / Yes", language)}
                      </SelectItem>
                      <SelectItem value="dont-mind">
                        {getText("Αδιάφορο / We don't mind", language)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h3 className="text-xl font-semibold mb-4">
            {getText(
              "Γλώσσες & Εθνικότητα / Languages & Nationality",
              language
            )}
          </h3>

          <div className="mb-4">
            <FormField
              control={form.control}
              name="nationality"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Εθνικότητα-Υπηκοότητα / Nationality-Citizenship",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-6">
            <LanguageSelect
              language={language}
              languages={languages}
              setLanguages={setLanguages}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h3 className="text-xl font-semibold mb-4">
            {getText("Επιπλέον Πληροφορίες / Additional Information", language)}
          </h3>

          <div>
            <FormField
              control={form.control}
              name="additionalDetails"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Θα θέλατε να προσθέσετε κάποιες λεπτομέρειες περαιτέρω για τη θέση έτσι ώστε να έχουμε μία πιο ολοκληρωμένη εικόνα; / Would you like to give us a more detailed description about the offered position, your needs and the duties, so we have a fuller view on what you are looking for?",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Textarea rows={4} {...field} />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-6">
            <FormField
              control={form.control}
              name="documentUpload"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Επισύναψη κάποιου αρχείου που να επιβεβαιώνει ότι ο πελάτης είναι υπαρκτός, κατά προτίμηση πιστοποιητικό οικογενειακής κατάστασης (για λόγους ασφαλείας των Nannies) / Please upload any document that could certify that you are an existing client or person or family. A marital status certificate would be preferred.",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "Για την ασφάλεια των Nannies / Just to make sure our nannies will not be in danger",
                      language
                    )}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      onChange={(e) => {
                        // Store the actual File object, not just the name
                        const file = e.target.files?.[0] || null;
                        field.onChange(file);
                      }}
                      // Don't pass the value prop to file inputs
                      onBlur={field.onBlur}
                      name={field.name}
                      ref={field.ref}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </>
  );
};
