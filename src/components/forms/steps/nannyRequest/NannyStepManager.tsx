import React from "react";
import { useNannyRequestFormContext } from "../../../../contexts/NannyRequestFormContext";
import { FamilyInfoStep } from "./FamilyInfoStep";
import { NannyPreferencesStep } from "./NannyPreferencesStep";
import { PositionDetailsStep } from "./PositionDetailsStep";

export const NannyStepManager: React.FC = () => {
  const { currentStep } = useNannyRequestFormContext();

  // Render the appropriate step based on currentStep
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <FamilyInfoStep />;
      case 1:
        return <PositionDetailsStep />;
      case 2:
        return <NannyPreferencesStep />;
      default:
        return <FamilyInfoStep />;
    }
  };

  return <div className="mt-6">{renderStep()}</div>;
};
