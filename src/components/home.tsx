import { Client } from "@/components/crm/clients/ClientsTable";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { processStoredAuthHash } from "@/lib/AuthHandler";
import { getAuthSession } from "@/lib/authUtils";
import { PaginationParams } from "@/lib/paginationUtils";
import { supabase } from "@/lib/supabase";
import { Activity, fetchActivities } from "@/services/activityService";
import { Candidate, fetchCandidates } from "@/services/candidateService";
import { fetchClients } from "@/services/clientService";
import { Deal, fetchDeals } from "@/services/dealService";
import { useCallback, useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import ActivityFeed from "./dashboard/ActivityFeed";
import Analytics from "./dashboard/Analytics";
import DataTable from "./dashboard/DataTable";
import MetricsGrid from "./dashboard/MetricsGrid";
import RevenueChart from "./dashboard/RevenueChart";

function Home() {
  const { canView, canEdit } = usePermissionCheck();
  const [isProcessingAuth, setIsProcessingAuth] = useState(false);
  const [userData, setUserData] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const location = useLocation();

  // Dashboard data states
  const [deals, setDeals] = useState<Deal[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(false);
  const [activitiesPagination, setActivitiesPagination] =
    useState<PaginationParams>({
      page: 1,
      itemsPerPage: 10,
      filters: {},
    });
  const [activitiesTotalCount, setActivitiesTotalCount] = useState(0);
  const [activitiesTotalPages, setActivitiesTotalPages] = useState(1);
  const [dataLoaded, setDataLoaded] = useState(false);

  // Process authentication hash on component mount
  useEffect(() => {
    const handleAuthHash = async () => {
      setIsProcessingAuth(true);
      try {
        const wasProcessed = await processStoredAuthHash(supabase);
        if (wasProcessed) {
          console.debug("Successfully processed OAuth redirect");
        }
      } catch (err) {
        console.error("Error processing auth hash:", err);
      } finally {
        setIsProcessingAuth(false);
      }
    };

    handleAuthHash();
  }, []);

  // Fetch activities from the audit_log table with pagination and filtering
  const loadActivities = useCallback(async () => {
    try {
      setActivitiesLoading(true);
      console.debug(
        "Fetching activities from audit_log with pagination:",
        activitiesPagination
      );

      // Fetch activities with pagination
      const result = await fetchActivities(activitiesPagination);

      console.debug(
        `Loaded ${result.data.length} activities (total: ${result.totalCount})`
      );
      setActivities(result.data);
      setActivitiesTotalCount(result.totalCount);
      setActivitiesTotalPages(result.totalPages);
    } catch (error) {
      console.error("Error loading activities:", error);
    } finally {
      setActivitiesLoading(false);
    }
  }, [activitiesPagination]);

  // Handle pagination changes for activities
  const handleActivitiesPaginationChange = useCallback(
    (newPagination: PaginationParams) => {
      console.debug("Changing activities pagination:", newPagination);
      setActivitiesPagination(newPagination);
    },
    []
  );

  // Load activities when data is loaded or pagination changes
  useEffect(() => {
    if (dataLoaded) {
      loadActivities();
    }
  }, [dataLoaded, loadActivities]);

  // Load all dashboard data
  const loadAllData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      console.debug("Loading dashboard data...");

      // Get current user data using the cached session
      const session = await getAuthSession();
      if (session?.user) {
        setUserData(session.user);
      }

      // Fetch all required data in parallel
      const [dealsResult, clientsResult, candidatesResult] = await Promise.all([
        fetchDeals(true),
        fetchClients(true),
        fetchCandidates(true),
      ]);

      // Process deals data
      if (dealsResult) {
        const dealsData = Array.isArray(dealsResult)
          ? dealsResult
          : dealsResult.data;
        console.debug(`Loaded ${dealsData.length} deals`);
        setDeals(dealsData);
      }

      // Process clients data
      if (clientsResult) {
        const clientsData = Array.isArray(clientsResult)
          ? clientsResult
          : clientsResult.data;
        console.debug(`Loaded ${clientsData.length} clients`);
        setClients(clientsData);
      }

      // Process candidates data
      if (candidatesResult) {
        const candidatesData = Array.isArray(candidatesResult)
          ? candidatesResult
          : candidatesResult.data;
        console.debug(`Loaded ${candidatesData.length} candidates`);
        setCandidates(candidatesData);
      }

      setDataLoaded(true);
    } catch (err) {
      console.error("Error loading dashboard data:", err);
      setError("Failed to load dashboard data. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, []);

  // Load dashboard data when not processing auth
  useEffect(() => {
    if (!isProcessingAuth) {
      loadAllData();
    }
  }, [isProcessingAuth, loadAllData]);

  // Calculate metrics for the dashboard
  const calculateMetrics = useCallback(() => {
    // Get current and previous month
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const previousYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    // Calculate start and end dates for current and previous months
    const currentMonthStart = new Date(currentYear, currentMonth, 1);
    const currentMonthEnd = new Date(
      currentYear,
      currentMonth + 1,
      0,
      23,
      59,
      59,
      999
    );
    const previousMonthStart = new Date(previousYear, previousMonth, 1);
    const previousMonthEnd = new Date(
      previousYear,
      previousMonth + 1,
      0,
      23,
      59,
      59,
      999
    );

    // Filter deals for current and previous months
    const currentMonthDeals = deals.filter((deal) => {
      const dealDate = new Date(deal.deal_date);
      return dealDate >= currentMonthStart && dealDate <= currentMonthEnd;
    });

    const previousMonthDeals = deals.filter((deal) => {
      const dealDate = new Date(deal.deal_date);
      return dealDate >= previousMonthStart && dealDate <= previousMonthEnd;
    });

    // Calculate total revenue and candidate salary for current and previous months
    const currentMonthRevenue = currentMonthDeals.reduce(
      (sum, deal) => sum + (deal.revenue || 0),
      0
    );

    const previousMonthRevenue = previousMonthDeals.reduce(
      (sum, deal) => sum + (deal.revenue || 0),
      0
    );

    const currentMonthCandidateSalary = currentMonthDeals.reduce(
      (sum, deal) => sum + (deal.candidate_salary || 0),
      0
    );

    const previousMonthCandidateSalary = previousMonthDeals.reduce(
      (sum, deal) => sum + (deal.candidate_salary || 0),
      0
    );

    // Calculate profit (revenue - candidate salary)
    const currentMonthProfit =
      currentMonthRevenue - currentMonthCandidateSalary;
    const previousMonthProfit =
      previousMonthRevenue - previousMonthCandidateSalary;

    // Calculate total revenue and profit (all time)
    const totalRevenue = deals.reduce(
      (sum, deal) => sum + (deal.revenue || 0),
      0
    );

    const totalCandidateSalary = deals.reduce(
      (sum, deal) => sum + (deal.candidate_salary || 0),
      0
    );

    const totalProfit = totalRevenue - totalCandidateSalary;

    // Format revenue and profit with Euro symbol
    const formattedRevenue = `€${totalRevenue.toLocaleString("el-GR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;

    const formattedProfit = `€${totalProfit.toLocaleString("el-GR", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;

    // Calculate month-over-month changes
    let revenueChange = "0% από τον προηγούμενο μήνα";
    let profitChange = "0% από τον προηγούμενο μήνα";
    let isRevenuePositive = true;
    let isProfitPositive = true;

    if (previousMonthRevenue > 0) {
      const revenueChangePercent =
        ((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) *
        100;
      isRevenuePositive = revenueChangePercent >= 0;
      revenueChange = `${
        isRevenuePositive ? "+" : ""
      }${revenueChangePercent.toFixed(1)}% από τον προηγούμενο μήνα`;
    }

    if (previousMonthProfit > 0) {
      const profitChangePercent =
        ((currentMonthProfit - previousMonthProfit) / previousMonthProfit) *
        100;
      isProfitPositive = profitChangePercent >= 0;
      profitChange = `${
        isProfitPositive ? "+" : ""
      }${profitChangePercent.toFixed(1)}% από τον προηγούμενο μήνα`;
    }

    // Filter clients for current and previous months
    const currentMonthClients = clients.filter((client) => {
      const createdAt = new Date(client.created_at || "");
      return createdAt >= currentMonthStart && createdAt <= currentMonthEnd;
    });

    const previousMonthClients = clients.filter((client) => {
      const createdAt = new Date(client.created_at || "");
      return createdAt >= previousMonthStart && createdAt <= previousMonthEnd;
    });

    // Calculate client change percentage
    const totalClientsCount = clients.length;
    let clientsChange = "0% από τον προηγούμενο μήνα";
    let isClientChangePositive = true;

    if (previousMonthClients.length > 0) {
      const clientChangePercent =
        ((currentMonthClients.length - previousMonthClients.length) /
          previousMonthClients.length) *
        100;
      isClientChangePositive = clientChangePercent >= 0;
      clientsChange = `${
        isClientChangePositive ? "+" : ""
      }${clientChangePercent.toFixed(1)}% από τον προηγούμενο μήνα`;
    }

    // Calculate deals change percentage
    const totalDealsCount = deals.length;
    let dealsChange = "0% από τον προηγούμενο μήνα";
    let isDealsChangePositive = true;

    if (previousMonthDeals.length > 0) {
      const dealsChangePercent =
        ((currentMonthDeals.length - previousMonthDeals.length) /
          previousMonthDeals.length) *
        100;
      isDealsChangePositive = dealsChangePercent >= 0;
      dealsChange = `${
        isDealsChangePositive ? "+" : ""
      }${dealsChangePercent.toFixed(1)}% από τον προηγούμενο μήνα`;
    }

    return {
      totalRevenue: formattedRevenue,
      revenueChange,
      isRevenuePositive,
      totalProfit: formattedProfit,
      profitChange,
      isProfitPositive,
      totalCustomers: totalClientsCount.toString(),
      customerChange: clientsChange,
      isClientChangePositive,
      totalDeals: totalDealsCount.toString(),
      dealsChange,
      isDealsChangePositive,
    };
  }, [deals, clients]);

  // Prepare data for the revenue chart
  const prepareRevenueChartData = useCallback(() => {
    if (!deals.length) {
      console.debug("No deals data available for revenue chart");
      return [];
    }

    // Create a map to store revenue by month
    const revenueByMonth = new Map();

    // Initialize all months with zero
    const months = [
      "Ιαν",
      "Φεβ",
      "Μαρ",
      "Απρ",
      "Μαϊ",
      "Ιουν",
      "Ιουλ",
      "Αυγ",
      "Σεπτ",
      "Οκτ",
      "Νοε",
      "Δεκ",
    ];
    months.forEach((month) => revenueByMonth.set(month, 0));

    // Aggregate revenue by month
    deals.forEach((deal) => {
      if (deal.deal_date && deal.revenue) {
        try {
          const date = new Date(deal.deal_date);
          const monthIndex = date.getMonth();
          const monthName = months[monthIndex];

          // Add revenue to the corresponding month
          revenueByMonth.set(
            monthName,
            revenueByMonth.get(monthName) + deal.revenue
          );
        } catch (err) {
          console.error("Error processing deal date:", err, deal);
        }
      }
    });

    // Convert map to array format required by the chart
    return months.map((month) => ({
      name: month,
      value: revenueByMonth.get(month),
    }));
  }, [deals]);

  // Prepare data for the deals table
  const prepareDealsTableData = useCallback(() => {
    if (!deals.length) {
      console.debug("No deals data available for deals table");
      return [];
    }

    return deals.slice(0, 5).map((deal) => ({
      ...deal,
    }));
  }, [deals]);

  // If processing auth redirect, show loading
  if (isProcessingAuth) {
    return (
      <div className="flex justify-center items-center min-h-[80vh] bg-background">
        <div className="text-center">
          <svg
            className="animate-spin h-10 w-10 text-primary mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-lg font-medium text-primary">
            Επεξεργασία στοιχείων...
          </p>
          <p className="text-sm text-secondary mt-2">
            Παρακαλώ περιμένετε μέχρι να ολοκληρωθεί η επεξεργασία των στοιχείων
            σας.
          </p>
        </div>
      </div>
    );
  }

  // Regular loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh] bg-background">
        <div className="text-center">
          <svg
            className="animate-spin h-10 w-10 text-primary mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-lg font-medium text-primary">
            Παρακαλώ περιμένετε...
          </p>
        </div>
      </div>
    );
  }

  // Show error message if there was an error loading data
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[80vh] bg-background">
        <div className="text-center max-w-md p-6 bg-destructive/10 rounded-lg border border-destructive/30">
          <svg
            className="h-12 w-12 text-destructive mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <p className="text-lg font-medium text-destructive mb-2">
            Error Loading Data
          </p>
          <p className="text-sm text-destructive/80">{error}</p>
          <button
            onClick={loadAllData}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Show empty state if no data is available
  if (dataLoaded && !deals.length && !clients.length && !candidates.length) {
    return (
      <div className="flex justify-center items-center min-h-[80vh] bg-background">
        <div className="text-center max-w-md p-6 bg-primary/5 rounded-lg border border-primary/20">
          <svg
            className="h-12 w-12 text-primary mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <p className="text-lg font-medium text-primary mb-2">
            Δεν υπάρχουν δεδομένα
          </p>
          <p className="text-sm text-secondary">
            Δεν υπάρχουν δεδομένα για να εμφανιστούν στο dashboard. Παρακαλώ
            προσθέστε δεδομένα για να εμφανιστούν εδώ.
            <br />
            <br />
            Προσθέστε πρώτα υποψηφίους, μετά προσφορές και επιλέξτε τις
            προσφορές που θέλετε να εμφανιστούν στο dashboard.
          </p>
        </div>
      </div>
    );
  }

  // Calculate metrics and prepare data for components
  const metrics = calculateMetrics();
  const chartData = prepareRevenueChartData();
  const recentDeals = prepareDealsTableData();

  // Determine what to render based on the current route
  const renderContent = () => {
    // Default dashboard view
    return (
      <div className="min-h-screen bg-background">
        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <div className="border-b border-light/20 bg-background/50 backdrop-blur-sm">
            <div className="container mx-auto px-4 md:px-6">
              <TabsList className="h-12 bg-transparent">
                <TabsTrigger
                  value="overview"
                  className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                >
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="analytics"
                  className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                  disabled={!canView("analytics")}
                  title={
                    !canView("analytics")
                      ? "Δεν έχετε πρόσβαση για προβολή των αναλυτικών στοιχείων"
                      : ""
                  }
                >
                  Analytics
                </TabsTrigger>
              </TabsList>
            </div>
          </div>

          <TabsContent value="overview">
            <div className="container mx-auto py-6 px-4 md:px-6">
              <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                <div className="lg:col-span-9 space-y-6">
                  {canView("analytics") && (
                    <>
                      <MetricsGrid
                        totalRevenue={metrics.totalRevenue}
                        revenueChange={metrics.revenueChange}
                        isRevenuePositive={metrics.isRevenuePositive}
                        totalProfit={metrics.totalProfit}
                        profitChange={metrics.profitChange}
                        isProfitPositive={metrics.isProfitPositive}
                        totalCustomers={metrics.totalCustomers}
                        customerChange={metrics.customerChange}
                        isCustomerChangePositive={
                          metrics.isClientChangePositive
                        }
                        totalDeals={metrics.totalDeals}
                        dealsChange={metrics.dealsChange}
                        isDealsChangePositive={metrics.isDealsChangePositive}
                      />
                      <RevenueChart data={chartData} />
                    </>
                  )}
                  <DataTable deals={recentDeals} />
                </div>
                <div className="lg:col-span-3">
                  {/* Only show ActivityFeed if user has activities view permission */}
                  {canView("activities") ? (
                    <ActivityFeed
                      activities={activities}
                      isLoading={activitiesLoading}
                      pagination={activitiesPagination}
                      totalCount={activitiesTotalCount}
                      totalPages={activitiesTotalPages}
                      onPaginationChange={handleActivitiesPaginationChange}
                      onRefresh={loadActivities}
                    />
                  ) : (
                    <div className="h-full flex items-center justify-center p-6 border rounded-lg bg-muted/10">
                      <p className="text-muted-foreground text-center">
                        Δεν έχετε πρόσβαση για προβολή της ροής δραστηριοτήτων.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            {canView("analytics") ? (
              <Analytics canEdit={canEdit("analytics")} />
            ) : (
              <div className="container mx-auto py-6 px-4 flex items-center justify-center min-h-[50vh]">
                <div className="text-center p-6 border rounded-lg bg-muted/10 max-w-md">
                  <p className="text-muted-foreground">
                    Δεν έχετε πρόσβαση για προβολή των αναλυτικών στοιχείων.
                  </p>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  return (
    <>
      {loading ? (
        <div className="flex items-center justify-center h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="text-center text-red-500">{error}</div>
      ) : (
        renderContent()
      )}
    </>
  );
}

export default Home;
