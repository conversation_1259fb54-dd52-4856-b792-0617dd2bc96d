import { Globe } from "lucide-react";
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { useCandidateFormContext } from "../../contexts/CandidateFormContext";
import { useFormContext } from "../../contexts/FormContext";
import { useNannyRequestFormContext } from "../../contexts/NannyRequestFormContext";
import { Button } from "../ui/button";
import { FormControls } from "../ui/FormControls";
import { StepNavigation } from "../ui/StepNavigation";
import { Toaster } from "../ui/toaster";

interface FormLayoutProps {
  form: UseFormReturn<any>;
  formRef: React.RefObject<HTMLFormElement>;
  children: React.ReactNode;
  onSubmit: (values: any) => Promise<void>;
  title?: string;
  isCandidateForm?: boolean;
}

export const FormLayout: React.FC<FormLayoutProps> = ({
  form,
  formRef,
  children,
  onSubmit,
  title,
  isCandidateForm = true,
}) => {
  // Determine which context to use based on form type
  const useAppropriateContext = () => {
    if (isCandidateForm) {
      try {
        return useCandidateFormContext();
      } catch (error) {
        return useFormContext(); // Fallback to the original context
      }
    } else {
      try {
        return useNannyRequestFormContext();
      } catch (error) {
        return useFormContext(); // Fallback to the original context
      }
    }
  };

  const context = useAppropriateContext();
  const { isLastStep, language, setLanguage, languages } = context;

  // These properties might not exist in all contexts
  const workExperience =
    "workExperience" in context ? context.workExperience : undefined;
  const references = "references" in context ? context.references : undefined;

  const handleSubmit = async (values: any) => {
    let formData = { ...values };

    // Include languages if available
    if (languages) {
      formData.languages = languages.filter(
        (lang) => lang.language.trim() !== ""
      );
    }

    // Include work experience if available
    if (workExperience && Array.isArray(workExperience)) {
      formData.workExperience = workExperience.filter(
        (exp: any) => exp.period?.trim() !== ""
      );
    }

    // Include references if available
    if (references && Array.isArray(references)) {
      formData.referencesContacts = references.filter(
        (ref: any) => ref.name?.trim() !== ""
      );
    }
    await onSubmit(formData);
  };

  const toggleLanguage = () => {
    setLanguage(language === "el" ? "en" : "el");
  };

  const submitForm = form.handleSubmit(handleSubmit);

  // Use consistent styling for both forms
  const getBgClass = () => {
    return "min-h-screen w-full bg-gradient-to-b from-accent/20 via-background to-accent/20 fixed inset-0 overflow-auto";
  };

  const getCardClass = () => {
    return "bg-white rounded-xl shadow-lg border border-accent/20 p-6 mb-8 transition-all duration-300 hover:shadow-xl";
  };

  const getTitleClass = () => {
    return "text-3xl font-bold text-center text-primary";
  };

  return (
    <div className={getBgClass()}>
      <div className="container relative z-10 mx-auto py-10 px-10 max-w-4xl">
        <div className="flex flex-col items-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className=" p-2 rounded-md flex items-center justify-center">
              <div className=" p-1 rounded-sm flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="Nanny Blue Logo"
                  className="h-24 w-auto"
                />
              </div>
            </div>
          </div>

          <div className="w-full flex justify-between items-center">
            <div className="relative">
              <h1 className={getTitleClass()}>
                {title ||
                  (language === "el" ? "Αίτηση Υποψηφίου" : "Candidate's Form")}
              </h1>
            </div>
            <Toaster />
            <Button
              variant="outline"
              size="sm"
              onClick={toggleLanguage}
              className={
                "flex items-center gap-2 rounded-md border-light/30 hover:bg-primary/10 hover:text-primary hover-float"
              }
            >
              <Globe className="h-4 w-4" />
              {language === "el" ? "English" : "Ελληνικά"}
            </Button>
          </div>
        </div>

        <div className={getCardClass()}>
          <StepNavigation isCandidateForm={isCandidateForm} />

          <form ref={formRef}>
            <div className="relative">{children}</div>

            <FormControls
              submitForm={submitForm}
              isLastStep={isLastStep}
              isCandidateForm={isCandidateForm}
            />
          </form>
        </div>
      </div>
    </div>
  );
};
