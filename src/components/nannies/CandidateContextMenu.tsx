import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import { Candidate, deleteCandidate } from "@/services/candidateService";
import { PERMISSION_CODES } from "@/services/userService";
import {
  CheckCircle,
  Edit2,
  ExternalLink,
  Handshake,
  Trash2,
  User,
  XCircle,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ContextMenuItem } from "../crm/BasicTable";
import CreateDealSheet from "../crm/forms/CreateDealSheet";
import { toast } from "../ui/use-toast";
import CandidateUpdateDialog from "./CandidateUpdateDialog";

interface CandidateContextMenuProps {
  candidateType: "candidate" | "nanny" | "tutor";
  onReject: (candidate: Candidate) => Promise<void>;
  onApproveNanny?: (candidate: Candidate) => Promise<void>;
  onApproveTutor?: (candidate: Candidate) => Promise<void>;
  onReload: () => void;
}

const CandidateContextMenu = ({
  candidateType,
  onReject,
  onApproveNanny,
  onApproveTutor,
  onReload,
}: CandidateContextMenuProps) => {
  const navigate = useNavigate();
  const { canEdit, canCreate, canDelete, hasSpecificPermission } =
    usePermissionCheck();

  // Check specific permissions based on candidate type
  const canEditPermission =
    candidateType === "nanny"
      ? canEdit("nannies")
      : candidateType === "tutor"
      ? canEdit("tutors")
      : canEdit("candidates");

  const canDeletePermission =
    candidateType === "nanny"
      ? canDelete("nannies")
      : candidateType === "tutor"
      ? canDelete("tutors")
      : canDelete("candidates");

  const canCreateDeal = canCreate("deals");
  const canApproveNannyPermission = hasSpecificPermission(
    PERMISSION_CODES.put_nannies
  );
  const canApproveTutorPermission = hasSpecificPermission(
    PERMISSION_CODES.put_tutors
  );
  const canRejectCandidatePermission = hasSpecificPermission(
    PERMISSION_CODES.put_candidates
  );

  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(
    null
  );
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [updateField, setUpdateField] = useState<string>("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [candidateToDelete, setCandidateToDelete] = useState<Candidate | null>(
    null
  );
  const [isDealDialogOpen, setIsDealDialogOpen] = useState(false);

  // Get the appropriate route based on candidate type
  const getRoute = (id: string | number) => {
    switch (candidateType) {
      case "candidate":
        return `/candidates/${id}`;
      case "nanny":
        return `/nannies/${id}`;
      case "tutor":
        return `/tutors/${id}`;
      default:
        return `/candidates/${id}`;
    }
  };

  const handleDeleteCandidate = (candidate: Candidate) => {
    // Check if user has permission to delete
    if (!canDeletePermission) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα διαγραφής υποψηφίων.",
        variant: "destructive",
      });
      return;
    }

    setCandidateToDelete(candidate);
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion
  const confirmDeleteCandidate = async () => {
    if (!candidateToDelete) return;

    try {
      const success = await deleteCandidate(candidateToDelete.id.toString());

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Ο υποψήφιος "${candidateToDelete.form_data?.name} ${candidateToDelete.form_data?.surname}" διαγράφηκε επιτυχώς.`,
          variant: "default",
        });

        // Trigger a reload
        onReload();
      } else {
        throw new Error("Failed to delete candidate");
      }
    } catch (error) {
      console.error("Error deleting candidate:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία διαγραφής του υποψηφίου. Παρακαλώ διεγράψτε τα deals που ανήκουν στον υποψήφιο και δοκιμάστε ξανά. ",
        variant: "destructive",
      });
    } finally {
      // Close the dialog and reset the candidate to delete
      setIsDeleteDialogOpen(false);
      setCandidateToDelete(null);
    }
  };

  // Handle dialog close with proper focus management
  const handleDialogClose = () => {
    // Ensure we blur any focused elements before closing, but only if not in a popover
    if (
      document.activeElement instanceof HTMLElement &&
      !document.activeElement.closest("[data-radix-popper-content-wrapper]")
    ) {
      document.activeElement.blur();
    }

    // Reset state
    setIsUpdateDialogOpen(false);
    setUpdateField("");

    // Return focus to the document body after a short delay
    setTimeout(() => {
      document.body.focus();
    }, 10);
  };

  const handleUpdateSuccess = () => {
    // Trigger a reload
    onReload();
  };

  // Helper function to open dialog with proper focus management
  const openUpdateDialog = (candidate: Candidate, field: string) => {
    // Check if user has permission to edit
    if (!canEditPermission) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα επεξεργασίας.",
        variant: "destructive",
      });
      return;
    }

    // Then set up and open the dialog
    setSelectedCandidate(candidate);
    setUpdateField(field);
    setIsUpdateDialogOpen(true);
  };

  // Handle deal dialog open/close
  const handleDealDialogOpen = (candidate: Candidate) => {
    // Check if user has permission to create deals
    if (!canCreateDeal) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα δημιουργίας συμφωνιών.",
        variant: "destructive",
      });
      return;
    }

    setSelectedCandidate(candidate);
    setIsDealDialogOpen(true);
  };

  const handleDealDialogClose = (open: boolean) => {
    setIsDealDialogOpen(open);
    if (!open) {
      // Reset selected candidate when dialog closes
      setTimeout(() => {
        setSelectedCandidate(null);
      }, 100);
    }
  };

  // Base context menu items that all candidate types share
  let contextMenuItems: ContextMenuItem[] = [
    // View items - always available
    {
      label: "Προβολή",
      icon: <User className="h-4 w-4" />,
      onClick: (item: Candidate) => {
        navigate(getRoute(item.id));
      },
    },
    {
      label: "Νέο παράθυρο",
      icon: <ExternalLink className="h-4 w-4" />,
      onClick: (item: Candidate) => {
        window.open(getRoute(item.id), "_blank");
      },
    },
  ];

  // Add reject option if user has permission
  if (canRejectCandidatePermission) {
    contextMenuItems.push({
      label: "Απόρριψη",
      icon: <XCircle className="h-4 w-4 text-orange-500" />,
      onClick: onReject,
      className: "text-orange-600 hover:bg-orange-50",
    });
  }

  // Add edit options if user has permission
  if (canEditPermission) {
    contextMenuItems = [
      ...contextMenuItems,
      {
        label: "Ενημέρωση Ονόματος",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Candidate) => {
          openUpdateDialog(item, "form_data.name");
        },
      },
      {
        label: "Ενημέρωση Επωνύμου",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Candidate) => {
          openUpdateDialog(item, "form_data.surname");
        },
      },
      {
        label: "Ενημέρωση Θέσης",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Candidate) => {
          openUpdateDialog(item, "form_data.position_interests");
        },
      },
      {
        label: "Ενημέρωση Έναρξης",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Candidate) => {
          openUpdateDialog(item, "form_data.start_availability");
        },
      },
      {
        label: "Ενημέρωση Τοποθεσίας",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Candidate) => {
          openUpdateDialog(item, "form_data.city");
        },
      },
    ];
  }

  // Add delete option if user has permission
  if (canDeletePermission) {
    contextMenuItems.push({
      label: "Διαγραφή",
      icon: <Trash2 className="h-4 w-4" />,
      onClick: handleDeleteCandidate,
      className: "text-destructive hover:bg-destructive/10",
    });
  }

  // Add candidate-specific menu items
  if (candidateType === "candidate" && onApproveNanny && onApproveTutor) {
    // Create a new array for the updated menu items
    let updatedMenuItems = [...contextMenuItems.slice(0, 2)]; // Keep the first two items (view options)

    // Add approve nanny option if user has permission
    if (canApproveNannyPermission) {
      updatedMenuItems.push({
        label: "Approve Nanny",
        icon: <CheckCircle className="h-4 w-4 text-green-500" />,
        onClick: onApproveNanny,
        className: "text-green-600 hover:bg-green-50",
      });
    }

    // Add approve tutor option if user has permission
    if (canApproveTutorPermission) {
      updatedMenuItems.push({
        label: "Approve Tutor",
        icon: <CheckCircle className="h-4 w-4 text-blue-500" />,
        onClick: onApproveTutor,
        className: "text-blue-600 hover:bg-blue-50",
      });
    }

    // Add the rest of the items
    updatedMenuItems = [...updatedMenuItems, ...contextMenuItems.slice(2)];

    // Update the context menu items
    contextMenuItems = updatedMenuItems;
  }

  // Add deal creation option for nannies and tutors only if user has permission
  if (
    (candidateType === "nanny" || candidateType === "tutor") &&
    canCreateDeal
  ) {
    // Find the index to insert after "Νέο παράθυρο"
    const insertIndex = contextMenuItems.findIndex(
      (item) => item.label === "Νέο παράθυρο"
    );

    if (insertIndex !== -1) {
      contextMenuItems.splice(insertIndex + 1, 0, {
        label: "Δημιουργία Deal",
        icon: <Handshake className="h-4 w-4" />,
        onClick: (item: Candidate) => {
          handleDealDialogOpen(item);
        },
      });
    }
  }

  // Add candidate type update for candidates only if user has edit permission
  if (candidateType === "candidate" && canEditPermission) {
    // Find the index to insert after
    const insertIndex = contextMenuItems.findIndex(
      (item) => item.label === "Ενημέρωση Επωνύμου"
    );

    if (insertIndex !== -1) {
      contextMenuItems.splice(insertIndex + 1, 0, {
        label: "Ενημέρωση Κατηγορίας",
        icon: <Edit2 className="h-4 w-4" />,
        onClick: (item: Candidate) => {
          openUpdateDialog(item, "form_data.candidate_type");
        },
      });
    }
  }

  return {
    contextMenuItems,
    dialogComponents: (
      <>
        {/* Update Dialog */}
        <CandidateUpdateDialog
          candidate={selectedCandidate}
          isOpen={isUpdateDialogOpen}
          onClose={handleDialogClose}
          onSuccess={handleUpdateSuccess}
          updateField={updateField}
        />

        {/* Create Deal Sheet */}
        {selectedCandidate && (
          <CreateDealSheet
            open={isDealDialogOpen}
            onOpenChange={handleDealDialogClose}
            candidateId={selectedCandidate.id.toString()}
            onSuccess={handleUpdateSuccess}
          />
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Διαγραφή Υποψηφίου</AlertDialogTitle>
              <AlertDialogDescription>
                Είστε βέβαιοι ότι θέλετε να διαγράψετε τον υποψήφιο "
                {candidateToDelete?.form_data?.name}{" "}
                {candidateToDelete?.form_data?.surname}" ? Αυτή η ενέργεια δεν
                μπορεί να αναιρεθεί.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Άκυρο</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDeleteCandidate}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Διαγραφή
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    ),
  };
};

export default CandidateContextMenu;
