import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SearchableCitySelectInline } from "@/components/ui/SearchableCitySelectInline";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { positionOptions, startDateOptions } from "@/schemas/FormSchema";
import { Candidate, updateCandidate } from "@/services/candidateService";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { Checkbox } from "../ui/checkbox";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";

interface CandidateUpdateDialogProps {
  candidate: Candidate | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  updateField: string;
}

const CandidateUpdateDialog = ({
  candidate,
  isOpen,
  onClose,
  onSuccess,
  updateField,
}: CandidateUpdateDialogProps) => {
  const { toast } = useToast();
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<any>({});

  // Reset form data when candidate changes
  useState(() => {
    if (candidate) {
      setFormData({});
    }
  });

  const handleSave = async () => {
    if (!candidate) return;

    setSaving(true);
    let updateSuccessful = false;

    try {
      // Create an update object with only the changed fields
      const updatedCandidate: Candidate = { ...candidate };

      // Handle different field types
      if (updateField === "is_rejected") {
        updatedCandidate.is_rejected = formData.is_rejected;
      } else if (updateField === "form_data") {
        // Update the entire form_data object
        updatedCandidate.form_data = {
          ...candidate.form_data,
          ...formData,
        };
      } else if (updateField.startsWith("form_data.")) {
        // Update a specific field in form_data
        const fieldName = updateField.replace("form_data.", "");
        updatedCandidate.form_data = {
          ...candidate.form_data,
          [fieldName]: formData[fieldName],
        };
      }

      const result = await updateCandidate(updatedCandidate);

      if (result) {
        updateSuccessful = true;
        toast({
          title: "Επιτυχία",
          description: "Ο υποψήφιος ενημερώθηκε επιτυχώς.",
        });

        // Blur any focused elements before closing
        if (
          document.activeElement instanceof HTMLElement &&
          document.activeElement.closest('[role="dialog"]')
        ) {
          document.activeElement.blur();
        }

        // Close the dialog first
        onClose();

        // Then trigger the reload with a small delay to ensure the dialog is fully closed
        setTimeout(() => {
          onSuccess();
        }, 50);
      } else {
        throw new Error("Failed to update candidate");
      }
    } catch (error) {
      console.error("Error updating candidate:", error);
      toast({
        title: "Σφάλμα",
        description: "Παρουσιάστηκε σφάλμα κατά την ενημέρωση του υποψηφίου.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
      // Only close in case of error, success case already closes
      if (!updateSuccessful) {
        onClose();
      }
    }
  };

  const renderFormField = () => {
    if (!candidate) return null;

    switch (updateField) {
      case "is_rejected":
        return (
          <div className="space-y-4">
            <Label htmlFor="is_rejected">Κατάσταση</Label>
            <RadioGroup
              value={
                formData.is_rejected !== undefined
                  ? formData.is_rejected
                    ? "true"
                    : "false"
                  : candidate.is_rejected
                  ? "true"
                  : "false"
              }
              onValueChange={(value) =>
                setFormData({ is_rejected: value === "true" })
              }
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="rejected" />
                <Label htmlFor="rejected">Rejected</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="pending" />
                <Label htmlFor="pending">Pending</Label>
              </div>
            </RadioGroup>
          </div>
        );

      case "form_data.name":
        return (
          <div className="space-y-4">
            <Label htmlFor="name">Όνομα</Label>
            <Input
              id="name"
              defaultValue={candidate.form_data?.name || ""}
              onChange={(e) => setFormData({ name: e.target.value })}
            />
          </div>
        );

      case "form_data.surname":
        return (
          <div className="space-y-4">
            <Label htmlFor="surname">Επώνυμο</Label>
            <Input
              id="surname"
              defaultValue={candidate.form_data?.surname || ""}
              onChange={(e) => setFormData({ surname: e.target.value })}
            />
          </div>
        );

      case "form_data.candidate_type":
        return (
          <div className="space-y-4">
            <Label htmlFor="candidate_type">Κατηγορία</Label>
            <Select
              value={
                formData.candidate_type || candidate.form_data?.candidate_type
              }
              onValueChange={(value) => setFormData({ candidate_type: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Επιλέξτε κατηγορία" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="nanny">Nanny</SelectItem>
                <SelectItem value="tutor">Tutor</SelectItem>
                <SelectItem value="nanny_tutor">Nanny & Tutor</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case "form_data.position_interests":
        return (
          <div className="space-y-4">
            <Label htmlFor="position_interests">Θέση</Label>
            <div className="grid grid-cols-3 gap-3 mt-2">
              {positionOptions.map((position) => (
                <div
                  key={position.id}
                  className="flex items-center space-x-2 bg-muted/30 p-2 rounded-md"
                >
                  <Checkbox
                    id={position.id}
                    checked={
                      formData.position_interests
                        ? formData.position_interests.includes(position.id)
                        : candidate.form_data?.position_interests?.includes(
                            position.id
                          )
                    }
                    onCheckedChange={(checked) => {
                      const currentPositions =
                        formData.position_interests ||
                        candidate.form_data?.position_interests ||
                        [];

                      let newPositions: string[];
                      if (checked) {
                        newPositions = [...currentPositions, position.id];
                      } else {
                        newPositions = currentPositions.filter(
                          (p: string) => p !== position.id
                        );
                      }

                      setFormData({ position_interests: newPositions });
                    }}
                  />
                  <Label htmlFor={position.id} className="text-sm">
                    {position.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        );

      case "form_data.start_availability":
        return (
          <div className="space-y-2">
            <Label htmlFor="start_availability">Έναρξη</Label>
            <Select
              value={
                formData.start_availability ||
                candidate.form_data?.start_availability
              }
              onValueChange={(value) =>
                setFormData({ start_availability: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Επιλέξτε ημερομηνία έναρξης" />
              </SelectTrigger>
              <SelectContent>
                {startDateOptions.map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    {option.labelEl}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case "form_data.city":
        return (
          <div className="w-full h-full flex flex-col space-y-4">
            <Label htmlFor="city" className="text-lg font-medium">
              Τοποθεσία
            </Label>
            <SearchableCitySelectInline
              value={formData.city || candidate.form_data?.city || ""}
              onValueChange={(value: string) => setFormData({ city: value })}
              dropdownHeight="400px" // Adjusted height for better visibility
              className="w-full flex-grow"
            />
          </div>
        );

      default:
        return <div>Δεν υπάρχει φόρμα για αυτό το πεδίο.</div>;
    }
  };

  // Ensure proper cleanup when dialog closes
  const handleDialogChange = (open: boolean) => {
    if (!open) {
      // Ensure we blur any focused elements before closing
      if (
        document.activeElement instanceof HTMLElement &&
        document.activeElement.closest('[role="dialog"]')
      ) {
        document.activeElement.blur();
      }

      // Small delay before closing to ensure blur takes effect
      setTimeout(() => {
        onClose();
      }, 10);
    }
  };

  // Determine if we need a larger dialog for city field
  const isCityField = updateField === "form_data.city";

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogChange}>
      <DialogContent
        className={`${
          isCityField
            ? "w-[90vw] sm:max-w-[700px] md:max-w-[800px] lg:max-w-[900px] min-h-[700px] flex flex-col"
            : "sm:max-w-[600px] md:max-w-[700px]"
        } z-50 max-h-[90vh] p-0 overflow-hidden`}
        // Ensure dialog doesn't trap focus when closed
        onCloseAutoFocus={(e) => {
          e.preventDefault();
          // Return focus to the document body
          document.body.focus();
        }}
        // Prevent interaction outside the dialog when it's open
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
        // Ensure proper focus management
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          // Blur any focused elements inside the dialog
          if (
            document.activeElement instanceof HTMLElement &&
            document.activeElement.closest('[role="dialog"]')
          ) {
            document.activeElement.blur();
          }
          // Small delay before closing to ensure blur takes effect
          setTimeout(() => onClose(), 10);
        }}
      >
        <div className="flex flex-col h-full w-full">
          {/* Fixed Header */}
          <div className="p-6 border-b">
            <DialogHeader className="p-0">
              <DialogTitle>Ενημέρωση Υποψηφίου</DialogTitle>
              <DialogDescription>
                Ενημερώστε τα στοιχεία του υποψηφίου.
              </DialogDescription>
            </DialogHeader>
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-6">
            <div
              className={`${
                isCityField ? "min-h-[400px]" : ""
              } flex flex-col justify-start items-start w-full`}
            >
              {renderFormField()}
            </div>
          </div>

          {/* Fixed Footer */}
          <div className="p-6 border-t mt-auto">
            <div className="flex flex-row justify-start items-center gap-2 w-full">
              <Button
                variant="outline"
                onClick={() => {
                  // Blur any focused elements before closing
                  if (
                    document.activeElement instanceof HTMLElement &&
                    document.activeElement.closest('[role="dialog"]')
                  ) {
                    document.activeElement.blur();
                  }
                  // Small delay before closing to ensure blur takes effect
                  setTimeout(() => onClose(), 10);
                }}
              >
                Ακύρωση
              </Button>
              <Button
                onClick={(e) => {
                  // Prevent default to avoid focus issues
                  e.preventDefault();
                  // Blur the button before saving
                  if (document.activeElement instanceof HTMLElement) {
                    document.activeElement.blur();
                  }
                  // Small delay before saving to ensure blur takes effect
                  setTimeout(() => handleSave(), 10);
                }}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Αποθήκευση...
                  </>
                ) : (
                  "Αποθήκευση"
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CandidateUpdateDialog;
