export interface Candidate {
  id: string;
  is_nanny_approved?: boolean;
  is_tutor_approved?: boolean;
  form_data?: CandidateFormData;
  created_at?: string;
  updated_at?: string;
  storage_id?: string;
  cv_generated?: boolean;
  is_rejected?: boolean;
  schedule?: {
    availableRanges?: Array<{ from: string; to: string }>; // YYYY-MM-DD strings
    explicitlyUnavailable?: string[]; // YYYY-MM-DD strings
  };
}

export interface CandidateFormData {
  // Personal Information
  area?: string;
  name?: string;
  surname?: string;
  email?: string;
  gender?: string;
  address?: string;
  address_number?: string;
  city?: string;
  country?: string;
  postal_code?: string;
  nationality?: string;
  birth_date?: string;
  contact_number?: string;
  level?: string;

  // Profile and Experience
  personal_profile?: string;
  years_experience?: string;
  hobbies?: string;

  // Availability and Preferences
  start_availability?: string;
  place_preferences?: string;
  night_shift_availability?: string;
  travel_availability?: string;
  comfortable_with_pets?: string;
  experienced_driver?: string;
  experienced_swimmer?: string;

  // Vehicle and Smoking
  driving_license?: string;
  vehicle?: string;
  vehicle_type?: string;
  smoker?: string;

  // Interests
  position_interests?: string[];
  schedule_interests?: string[];
  duration_interests?: string[];

  // Education and Skills
  education?: string[];
  education_titles?: string[];
  languages?: { language: string; level: string }[];

  // Certifications and Documents

  references?: string;
  first_aid_certification?: string[];
  first_aid_update?: string;

  // Experience with Children
  experience_with_children?: string;
  children_age_experience?: string[];
  experience_learning_difficulties?: string;
  experience_special_needs?: string;

  // For Tutors
  candidate_type?: string;
  musical_instruments?: string[];
  music_theory?: string[];
  lesson_format?: string[];

  // Work Experience
  work_experience?: {
    period?: string;
    location?: string;
    position?: string;
    children?: string;
    schedule?: string;
    endingReason?: string;
  }[];

  // Emergency Contact
  emergency_contact_name?: string;
  emergency_contact_number?: string;
  emergency_contact_email?: string;
  emergency_contact_relation?: string;

  // References
  references_contacts?: any[];

  // Terms
  terms_accepted?: boolean;

  // Price List
  price_list_position_type?: string;
  price_list_location?: string;
  price_list_start_date?: string;
  price_list_duration?: string;
  price_list_schedule?: string;
  price_list_days_off?: string;
  price_list_salary?: string;
  price_list_insurance?: string;
  price_list_weekday_rate?: string;
  price_list_weekend_rate?: string;
  price_list_overnight_rate?: string;
  price_list_out_of_town_rate?: string;
  price_list_additional_children_rate?: string;
  price_list_transportation_covered?: boolean;
  price_list_food_covered?: boolean;
  price_list_accommodation_covered?: boolean;
  price_list_holidays_available?: boolean;
  price_list_travel_available?: boolean;
  price_list_weekends_available?: boolean;
  price_list_overnight_available?: boolean;
  price_list_drivers_license?: string;
  price_list_car?: string;
  price_list_notes?: string[];

  // File paths
  driving_license_document?: string;
  intro_video?: string;
  criminal_record_document?: string;
  insurance?: string;
  work_documents?: string;
  cv?: string;
  nanny_cv?: string;
  id_passport?: string;
  first_aid?: string;
  profile_photo?: string;
  price_list_file?: string;
  education_documents?: string[];
  reference_letters?: string[];
  client_cv_file?: string;
}
