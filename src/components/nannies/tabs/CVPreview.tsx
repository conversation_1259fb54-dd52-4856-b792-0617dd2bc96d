import { PDFViewer } from "@/components/ui/pdf-viewer";
import { useEffect, useRef, useState } from "react";

interface CVPreviewProps {
  cvUrl: string;
  getFilePreviewUrl: (
    bucket: string,
    path: string,
    expiresIn?: number
  ) => Promise<string>;
  expiresIn?: number; // Expiration time in seconds
}

const CVPreview = ({
  cvUrl,
  getFilePreviewUrl,
  expiresIn = 3600,
}: CVPreviewProps) => {
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const refreshTimerRef = useRef<number | null>(null);

  // Function to fetch and set the URL
  const fetchUrl = async () => {
    try {
      setLoading(true);
      setError(null);

      // If it's already a full URL, use it directly
      if (cvUrl.startsWith("https")) {
        setPreviewUrl(cvUrl);
      } else {
        // Otherwise, get a signed URL from Supabase
        const url = await getFilePreviewUrl(
          "candidate-documents",
          cvUrl,
          expiresIn
        );
        setPreviewUrl(url);

        // Set up a timer to refresh the URL before it expires
        // Refresh at 80% of the expiration time to ensure continuous access
        const refreshTime = Math.floor(expiresIn * 0.8) * 1000;

        // Clear any existing timer
        if (refreshTimerRef.current) {
          window.clearTimeout(refreshTimerRef.current);
        }

        // Set a new timer
        refreshTimerRef.current = window.setTimeout(() => {
          console.debug("Refreshing signed URL before expiration");
          fetchUrl();
        }, refreshTime);
      }
    } catch (err) {
      console.error("Error fetching CV preview URL:", err);
      setError("Failed to load CV preview");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUrl();

    // Clean up the timer when component unmounts
    return () => {
      if (refreshTimerRef.current) {
        window.clearTimeout(refreshTimerRef.current);
      }
    };
  }, [cvUrl, getFilePreviewUrl, expiresIn]);

  if (loading) {
    return (
      <div className="w-full h-[500px] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-[500px] flex items-center justify-center">
        <div className="text-center">
          <p className="text-destructive">{error}</p>
          <p className="text-sm text-muted-foreground mt-2">
            Το αρχείο μπορεί να είναι σε ένα private bucket ή να μην υπάρχει
            πλέον.
          </p>
        </div>
      </div>
    );
  }

  // Determine if the file is a PDF
  const isPdf =
    previewUrl.toLowerCase().endsWith(".pdf") ||
    previewUrl.toLowerCase().includes(".pdf?") ||
    previewUrl.includes("application/pdf");

  return (
    <div className="w-full flex flex-col">
      {isPdf ? (
        <PDFViewer
          url={previewUrl}
          fileName={cvUrl.split("/").pop() || "CV.pdf"}
          height="500px"
        />
      ) : (
        <div className="w-full h-[500px] flex items-center justify-center">
          <div className="text-center">
            <p className="text-muted-foreground">
              Η προεπισκόπηση δεν είναι διαθέσιμη για αυτόν τον τύπο αρχείου.
            </p>
            <a
              href={previewUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="mt-2 inline-block text-primary hover:underline"
            >
              Άνοιγμα αρχείου σε νέα καρτέλα
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default CVPreview;
