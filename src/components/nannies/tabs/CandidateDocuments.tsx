import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  getFileDownloadUrl,
  getFilePreviewUrl,
  listFilesInBucket,
} from "@/lib/utils";
import { Download, Eye, FileIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface CandidateDocumentsProps {
  storageId: string;
  bucketName?: string;
}

interface FileItem {
  name: string;
  id: string;
  metadata: {
    size: number;
    mimetype: string;
  };
}

const CandidateDocuments = ({
  storageId,
  bucketName = "candidates-documents",
}: CandidateDocumentsProps) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchFiles = async () => {
      if (!storageId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const filesList = await listFilesInBucket(bucketName, storageId);

        // Filter out folders and only keep files
        const onlyFiles = filesList.filter(
          (item) => !item.id.endsWith("/") && !item.id.endsWith("\\")
        );

        setFiles(onlyFiles);
      } catch (error) {
        console.error("Error fetching candidate documents:", error);
        toast({
          title: "Error",
          description: "Failed to load candidate documents.",
          variant: "destructive",
        });
        // Set empty array to avoid showing loading state indefinitely
        setFiles([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFiles();
  }, [storageId, bucketName, toast]);

  const handleFilePreview = async (filename: string) => {
    try {
      const filePath = `${storageId}/${filename}`;
      const isPdf = filename.toLowerCase().endsWith(".pdf");

      // Use a longer expiration time for PDFs
      const expiresIn = isPdf ? 14400 : 3600; // 4 hours for PDFs, 1 hour for other files

      const previewUrl = await getFilePreviewUrl(
        bucketName,
        filePath,
        expiresIn
      );

      // For PDFs, add parameters to ensure browser displays it instead of downloading
      if (isPdf) {
        const enhancedUrl = `${previewUrl}#toolbar=1&navpanes=1&view=FitH`;
        window.open(enhancedUrl, "_blank");
      } else {
        window.open(previewUrl, "_blank");
      }
    } catch (error) {
      console.error("Error previewing file:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία προβολής αρχείου. Παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  const handleFileDownload = async (filename: string) => {
    try {
      const filePath = `${storageId}/${filename}`;
      const downloadUrl = await getFileDownloadUrl(bucketName, filePath);

      // Create a temporary link element to trigger the download
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία λήψης αρχείου. Παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  const getFileTypeIcon = (filename: string) => {
    const extension = filename.split(".").pop()?.toLowerCase();

    // Return appropriate icon based on file extension
    switch (extension) {
      case "pdf":
        return <FileIcon className="text-red-500" />;
      case "doc":
      case "docx":
        return <FileIcon className="text-blue-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <FileIcon className="text-green-500" />;
      case "txt":
        return <FileIcon className="text-gray-500" />;
      case "xls":
      case "xlsx":
        return <FileIcon className="text-emerald-500" />;
      case "zip":
      case "rar":
        return <FileIcon className="text-amber-500" />;
      default:
        return <FileIcon className="text-gray-500" />;
    }
  };

  // Format file size to human-readable format
  const formatFileSize = (sizeInBytes: number): string => {
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} B`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(2)} KB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="text-center p-4 text-muted-foreground border border-dashed border-gray-200 rounded-md">
        <p>Δεν βρέθηκαν έγγραφα για αυτόν τον υποψήφιο.</p>
        <p className="text-xs mt-1">
          Τα έγγραφα που υποβάλλονται μέσω της φόρμας υποψηφιότητας εμφανίζονται
          εδώ.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
      {files.map((file) => (
        <div
          key={file.id}
          className="border border-light/30 rounded-lg p-4 flex items-center gap-3"
        >
          <div className="bg-primary/10 p-2 rounded-md">
            {getFileTypeIcon(file.name)}
          </div>
          <div className="flex-1">
            <p className="font-medium truncate" title={file.name}>
              {file.name}
            </p>
            <p className="text-sm text-muted-foreground">
              {file.metadata?.size
                ? formatFileSize(file.metadata.size)
                : "Unknown size"}
            </p>
            <div className="flex gap-2 mt-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2"
                onClick={() => handleFilePreview(file.name)}
              >
                <Eye size={14} className="mr-1" />
                <span className="text-xs">Προβολή</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2"
                onClick={() => handleFileDownload(file.name)}
              >
                <Download size={14} className="mr-1" />
                <span className="text-xs">Κατέβασμα</span>
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CandidateDocuments;
