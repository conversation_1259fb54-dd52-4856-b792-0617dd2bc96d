import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { MessageCircle, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Candidate } from "../models/Candidate";

interface Comment {
  id: string;
  candidate_id: string;
  user_id: string;
  comment: string;
  created_at: string;
  updated_at: string;
  user_email?: string;
}

interface CommentsTabProps {
  candidate: Candidate | null;
  editMode: boolean;
}

const CommentsTab = ({ candidate, editMode }: CommentsTabProps) => {
  const { toast } = useToast();
  const { user } = useAuth();

  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [currentUser, setCurrentUser] = useState<{
    id: string;
    email: string;
  } | null>(null);

  // Fetch the current user
  useEffect(() => {
    const fetchCurrentUser = async () => {
      if (user) {
        setCurrentUser({
          id: user.id,
          email: user.email || "",
        });
      }
    };

    fetchCurrentUser();
  }, []);

  // Fetch comments for the candidate
  useEffect(() => {
    if (candidate) {
      fetchComments();
    }
  }, [candidate]);

  const fetchComments = async () => {
    if (!candidate) return;

    setLoading(true);
    try {
      // Fetch comments with user information using a join
      const { data, error } = await supabase
        .from("candidate_comments")
        .select(
          `
          *,
          users:user_id (email,id)
        `
        )
        .eq("candidate_id", candidate.id)
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      // Format the comments with user email
      const formattedComments = data.map((content) => ({
        ...content,
        user_email: content.users?.email || "Unknown User",
        user_id: content.users?.id || "Unknown User",
      }));

      setComments(formattedComments);
    } catch (error) {
      console.error("Error fetching comments:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία λήψης σχολίων.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async () => {
    if (!candidate || !newComment.trim() || !currentUser) return;
    const user = await supabase
      .from("users")
      .select("id")
      .eq("supabase_uuid", currentUser.id)
      .single();

    if (!user.data) {
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία προσθήκης σχολίων.",
        variant: "destructive",
      });
      return;
    }
    setSubmitting(true);
    try {
      // Add the new comment
      const { data, error } = await supabase
        .from("candidate_comments")
        .insert({
          candidate_id: candidate.id,
          user_id: user.data?.id,
          comment: newComment.trim(),
        })
        .select();

      if (error) {
        throw error;
      }

      // Clear the input and refresh comments
      setNewComment("");
      await fetchComments();

      toast({
        title: "Επιτυχία",
        description: "Το σχόλιο προστέθηκε επιτυχώς.",
      });
    } catch (error) {
      console.error("Error adding comment:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία προσθήκης σχολίου.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!candidate) return;

    try {
      // Delete the comment
      const { error } = await supabase
        .from("candidate_comments")
        .delete()
        .eq("id", commentId);

      if (error) {
        throw error;
      }

      // Refresh comments
      await fetchComments();

      toast({
        title: "Επιτυχία",
        description: "Το σχόλιο διαγράφηκε επιτυχώς.",
      });
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία διαγραφής σχολίου.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("el-GR", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Card variant="accent" className="p-4 mt-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium text-primary">Σχόλια</h3>
        </div>
      </div>

      {/* Add new comment */}
      <div className="mb-6">
        <Textarea
          placeholder="Προσθέστε ένα νέο σχόλιο..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          className="min-h-[100px] mb-2"
        />
        <Button
          onClick={handleAddComment}
          disabled={!newComment.trim() || submitting}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          {submitting ? "Αποθήκευση..." : "Αποθήκευση Σχολίου"}
        </Button>
      </div>

      {/* Comments list */}
      <div className="space-y-4">
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin h-8 w-8 border-2 border-primary rounded-full border-t-transparent"></div>
          </div>
        ) : comments.length > 0 ? (
          comments.map((comment) => (
            <div
              key={comment.id}
              className="border border-border rounded-lg p-4 bg-card"
            >
              <div className="flex justify-between items-start mb-2">
                <div>
                  <p className="text-sm font-medium">{comment.user_email}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatDate(comment.created_at)}
                  </p>
                </div>
                {currentUser?.id === comment.user_id && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteComment(comment.id)}
                    className="h-8 w-8 text-destructive hover:text-destructive/80"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <p className="text-sm whitespace-pre-wrap">{comment.comment}</p>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            Δεν υπάρχουν σχόλια ακόμα.
          </div>
        )}
      </div>
    </Card>
  );
};

export default CommentsTab;
