import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import apiClient from "@/lib/api";
import { isNanny } from "@/lib/userUtils";
import { Download, Eye, Upload } from "lucide-react";
import { Candidate } from "../models/Candidate";

interface FilesTabProps {
  candidate: Candidate;
  editedCandidate: Candidate | null;
  editMode: boolean;
  handleFilePreview: (filePath: string) => void;
  handleFileDownload: (filePath: string) => void;
  handleInputChange: (
    field: string,
    value: string | boolean | string[]
  ) => void;
  triggerFileInput: (ref: React.RefObject<HTMLInputElement>) => void;
  fileInputRefs: {
    intro_video?: React.RefObject<HTMLInputElement>;
    profile_photo?: React.RefObject<HTMLInputElement>;
    education_documents?: React.RefObject<HTMLInputElement>;
    reference_letters?: React.RefObject<HTMLInputElement>;
    cv: React.RefObject<HTMLInputElement>;
    id_passport: React.RefObject<HTMLInputElement>;
    criminal_record_document: React.RefObject<HTMLInputElement>;
    driving_license_document: React.RefObject<HTMLInputElement>;
    first_aid: React.RefObject<HTMLInputElement>;
    nanny_cv: React.RefObject<HTMLInputElement>;
    price_list_file?: React.RefObject<HTMLInputElement>;
    client_cv_file?: React.RefObject<HTMLInputElement>;
  };
  docFiles: Record<string, File | null>;
  isNannyUser: boolean;
}

const FilesTab = ({
  candidate,
  editedCandidate,
  editMode,
  handleFilePreview,
  handleFileDownload,
  handleInputChange,
  triggerFileInput,
  fileInputRefs,
  docFiles,
  isNannyUser,
}: FilesTabProps) => {
  return (
    <div className="space-y-6">
      <Card className="p-6" variant="light">
        <h3 className="text-lg font-medium text-primary">Έγγραφα</h3>
        <div className="flex flex-row items-center justify-between w-full gap-4 ">
          <p className="text-secondary">
            Έγγραφα και πιστοποιητικά του υποψηφίου
          </p>
          {!isNannyUser && (
            <Button
              onClick={async () => {
                try {
                  await apiClient.put(
                    `/candidates/${candidate.id}/generate-file`,
                    {
                      file_type: "client_cv",
                    }
                  );
                  toast({
                    title: "Επιτυχία",
                    description: "Το Βιογραφικό δημιουργήθηκε με επιτυχία.",
                  });
                  setTimeout(() => {
                    window.location.reload();
                  }, 500);
                } catch (error) {
                  console.error("Error generating CV:", error);
                  toast({
                    title: "Σφάλμα",
                    description: "Απέτυχε η δημιουργία του Βιογραφικού.",
                    variant: "destructive",
                  });
                }
              }}
              variant="outline"
              className="border-primary text-primary hover:bg-primary/10"
            >
              Δημιουργία Βιογραφικού Πελάτη
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
          {/* ID Document */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Ταυτότητα</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.id_passport
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.id_passport && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(candidate.form_data.id_passport)
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(candidate.form_data.id_passport)
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.id_passport && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.id_passport.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.id_passport.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.id_passport ? "default" : "outline"}
                  size="sm"
                  className={
                    docFiles.id_passport ? "bg-primary text-white" : ""
                  }
                  onClick={() => triggerFileInput(fileInputRefs.id_passport)}
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.id_passport ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>

          {/* Criminal Record */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Ποινικό Μητρώο</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.criminal_record_document
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.criminal_record_document && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(
                        candidate.form_data.criminal_record_document
                      )
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(
                        candidate.form_data.criminal_record_document
                      )
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.criminal_record_document && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.criminal_record_document.name}
                    </span>
                    <span className="ml-1">
                      (
                      {(docFiles.criminal_record_document.size / 1024).toFixed(
                        1
                      )}{" "}
                      KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={
                    docFiles.criminal_record_document ? "default" : "outline"
                  }
                  size="sm"
                  className={
                    docFiles.criminal_record_document
                      ? "bg-primary text-white"
                      : ""
                  }
                  onClick={() =>
                    triggerFileInput(fileInputRefs.criminal_record_document)
                  }
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.criminal_record_document ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>

          {/* Driving License */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Δίπλωμα Οδήγησης</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.driving_license_document
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.driving_license_document && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(
                        candidate.form_data.driving_license_document
                      )
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(
                        candidate.form_data.driving_license_document
                      )
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.driving_license_document && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.driving_license_document.name}
                    </span>
                    <span className="ml-1">
                      (
                      {(docFiles.driving_license_document.size / 1024).toFixed(
                        1
                      )}{" "}
                      KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={
                    docFiles.driving_license_document ? "default" : "outline"
                  }
                  size="sm"
                  className={
                    docFiles.driving_license_document
                      ? "bg-primary text-white"
                      : ""
                  }
                  onClick={() =>
                    triggerFileInput(fileInputRefs.driving_license_document)
                  }
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.driving_license_document ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>

          {/* First Aid */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Πρώτες Βοήθειες</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.first_aid
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.first_aid && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(candidate.form_data.first_aid)
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(candidate.form_data.first_aid)
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.first_aid && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.first_aid.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.first_aid.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.first_aid ? "default" : "outline"}
                  size="sm"
                  className={docFiles.first_aid ? "bg-primary text-white" : ""}
                  onClick={() => triggerFileInput(fileInputRefs.first_aid)}
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.first_aid ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>

          {/* Intro Video */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Βίντεο Παρουσίασης</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.intro_video
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.intro_video && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(candidate.form_data.intro_video)
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(candidate.form_data.intro_video)
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && fileInputRefs.intro_video && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.intro_video && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.intro_video.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.intro_video.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.intro_video ? "default" : "outline"}
                  size="sm"
                  className={
                    docFiles.intro_video ? "bg-primary text-white" : ""
                  }
                  onClick={() => triggerFileInput(fileInputRefs.intro_video)}
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.intro_video ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>

          {/* Profile Photo */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Φωτογραφία Προφίλ</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.profile_photo
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.profile_photo && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(candidate.form_data.profile_photo)
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(candidate.form_data.profile_photo)
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && fileInputRefs.profile_photo && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.profile_photo && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.profile_photo.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.profile_photo.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.profile_photo ? "default" : "outline"}
                  size="sm"
                  className={
                    docFiles.profile_photo ? "bg-primary text-white" : ""
                  }
                  onClick={() => triggerFileInput(fileInputRefs.profile_photo)}
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.profile_photo ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>

          {/* Education Documents */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Έγγραφα Εκπαίδευσης</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.education_documents
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.education_documents && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(
                        Array.isArray(candidate.form_data.education_documents)
                          ? candidate.form_data.education_documents[0]
                          : candidate.form_data.education_documents
                      )
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(
                        Array.isArray(candidate.form_data.education_documents)
                          ? candidate.form_data.education_documents[0]
                          : candidate.form_data.education_documents
                      )
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && fileInputRefs.education_documents && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.education_documents && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.education_documents.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.education_documents.size / 1024).toFixed(1)}{" "}
                      KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.education_documents ? "default" : "outline"}
                  size="sm"
                  className={
                    docFiles.education_documents ? "bg-primary text-white" : ""
                  }
                  onClick={() =>
                    triggerFileInput(fileInputRefs.education_documents)
                  }
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.education_documents ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>

          {/* Reference Letters */}
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">Συστατικές Επιστολές</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.reference_letters
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.reference_letters && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(
                        Array.isArray(candidate.form_data.reference_letters)
                          ? candidate.form_data.reference_letters[0]
                          : candidate.form_data.reference_letters
                      )
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(
                        Array.isArray(candidate.form_data.reference_letters)
                          ? candidate.form_data.reference_letters[0]
                          : candidate.form_data.reference_letters
                      )
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && fileInputRefs.reference_letters && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.reference_letters && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.reference_letters.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.reference_letters.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.reference_letters ? "default" : "outline"}
                  size="sm"
                  className={
                    docFiles.reference_letters ? "bg-primary text-white" : ""
                  }
                  onClick={() =>
                    triggerFileInput(fileInputRefs.reference_letters)
                  }
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.reference_letters ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">CV υποψηφίου</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.nanny_cv
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.nanny_cv && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(candidate.form_data.nanny_cv)
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(candidate.form_data.nanny_cv)
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && fileInputRefs.nanny_cv && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.nanny_cv && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.nanny_cv.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.nanny_cv.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.nanny_cv ? "default" : "outline"}
                  size="sm"
                  className={docFiles.nanny_cv ? "bg-primary text-white" : ""}
                  onClick={() => triggerFileInput(fileInputRefs.nanny_cv)}
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.nanny_cv ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>
          <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
            <div className="bg-primary/10 p-2 rounded-md">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">CV πελάτη</p>
              <p className="text-sm text-muted-foreground">
                {candidate.form_data.client_cv_file
                  ? "Υποβλήθηκε"
                  : "Δεν έχει υποβληθεί"}
              </p>
              {candidate.form_data.client_cv_file && !editMode && (
                <div className="flex gap-2 mt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFilePreview(candidate.form_data.client_cv_file)
                    }
                  >
                    <Eye size={14} className="mr-1" />
                    <span className="text-xs">Προβολή</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 px-2"
                    onClick={() =>
                      handleFileDownload(candidate.form_data.client_cv_file)
                    }
                  >
                    <Download size={14} className="mr-1" />
                    <span className="text-xs">Κατέβασμα</span>
                  </Button>
                </div>
              )}
            </div>
            {editMode && fileInputRefs.client_cv_file && (
              <div className="ml-auto flex flex-col items-end">
                {docFiles.client_cv_file && (
                  <div className="text-xs text-primary mb-1 flex items-center">
                    <span className="truncate max-w-[120px]">
                      {docFiles.client_cv_file.name}
                    </span>
                    <span className="ml-1">
                      ({(docFiles.client_cv_file.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                )}
                <Button
                  variant={docFiles.client_cv_file ? "default" : "outline"}
                  size="sm"
                  className={
                    docFiles.client_cv_file ? "bg-primary text-white" : ""
                  }
                  onClick={() => triggerFileInput(fileInputRefs.client_cv_file)}
                >
                  <Upload size={16} className="mr-2" />
                  {docFiles.client_cv_file ? "Αλλαγή" : "Ανέβασμα"}
                </Button>
              </div>
            )}
          </div>
          {!isNanny && (
            <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
              <div className="bg-primary/10 p-2 rounded-md">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-primary"
                >
                  <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                  <polyline points="14 2 14 8 20 8" />
                </svg>
              </div>
              <div className="flex-1">
                <p className="font-medium">Αρχείο PriceList</p>
                <p className="text-sm text-muted-foreground">
                  {candidate.form_data.price_list_file
                    ? "Υποβλήθηκε"
                    : "Δεν έχει υποβληθεί"}
                </p>
                {candidate.form_data.price_list_file && !editMode && (
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-2"
                      onClick={() =>
                        handleFilePreview(candidate.form_data.price_list_file)
                      }
                    >
                      <Eye size={14} className="mr-1" />
                      <span className="text-xs">Προβολή</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-2"
                      onClick={() =>
                        handleFileDownload(candidate.form_data.price_list_file)
                      }
                    >
                      <Download size={14} className="mr-1" />
                      <span className="text-xs">Κατέβασμα</span>
                    </Button>
                  </div>
                )}
              </div>
              {editMode && fileInputRefs.price_list_file && (
                <div className="ml-auto flex flex-col items-end">
                  {docFiles.price_list_file && (
                    <div className="text-xs text-primary mb-1 flex items-center">
                      <span className="truncate max-w-[120px]">
                        {docFiles.price_list_file.name}
                      </span>
                      <span className="ml-1">
                        ({(docFiles.price_list_file.size / 1024).toFixed(1)} KB)
                      </span>
                    </div>
                  )}
                  <Button
                    variant={docFiles.price_list_file ? "default" : "outline"}
                    size="sm"
                    className={
                      docFiles.price_list_file ? "bg-primary text-white" : ""
                    }
                    onClick={() =>
                      triggerFileInput(fileInputRefs.price_list_file)
                    }
                  >
                    <Upload size={16} className="mr-2" />
                    {docFiles.price_list_file ? "Αλλαγή" : "Ανέβασμα"}
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Emergency Contact Information */}
        <div className="mt-8 space-y-4">
          <h3 className="text-lg font-medium">Επαφή Έκτακτης Ανάγκης</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Όνομα</p>
              {editMode ? (
                <Input
                  value={
                    editedCandidate?.form_data?.emergency_contact_name || ""
                  }
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.emergency_contact_name",
                      e.target.value
                    )
                  }
                />
              ) : (
                <p className="font-medium">
                  {candidate.form_data.emergency_contact_name || "-"}
                </p>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Σχέση</p>
              {editMode ? (
                <Input
                  value={
                    editedCandidate?.form_data?.emergency_contact_relation || ""
                  }
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.emergency_contact_relation",
                      e.target.value
                    )
                  }
                />
              ) : (
                <p className="font-medium">
                  {candidate.form_data.emergency_contact_relation || "-"}
                </p>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Τηλέφωνο</p>
              {editMode ? (
                <Input
                  value={
                    editedCandidate?.form_data?.emergency_contact_number || ""
                  }
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.emergency_contact_number",
                      e.target.value
                    )
                  }
                />
              ) : (
                <p className="font-medium">
                  {candidate.form_data.emergency_contact_number || "-"}
                </p>
              )}
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Email</p>
              {editMode ? (
                <Input
                  value={
                    editedCandidate?.form_data?.emergency_contact_email || ""
                  }
                  onChange={(e) =>
                    handleInputChange(
                      "form_data.emergency_contact_email",
                      e.target.value
                    )
                  }
                />
              ) : (
                <p className="font-medium">
                  {candidate.form_data.emergency_contact_email || "-"}
                </p>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default FilesTab;
