import { updateCities } from "@/lib/staticData";
import { fetchCities } from "@/services/cityService";
import { useEffect, useState } from "react";

interface CitiesProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that fetches cities from the API and updates the cities array
 * This should be placed high in the component tree to ensure cities are loaded
 * before they are needed by other components
 */
export function CitiesProvider({ children }: CitiesProviderProps) {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadCities = async () => {
      try {
        const cities = await fetchCities();
        updateCities(cities);
      } catch (error) {
        console.error("Error loading cities:", error);
        // The cities array will remain initialized with default values
      } finally {
        setIsLoading(false);
      }
    };

    loadCities();
  }, []);

  // We don't need to show a loading state as the default cities are already available
  return <>{children}</>;
}
