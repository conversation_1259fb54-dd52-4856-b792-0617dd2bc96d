import { <PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/alert";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/lib/supabase";
import {
  addCity,
  deleteCity,
  fetchCities,
  updateCity,
} from "@/services/cityService";
import {
  fetchUserPermissions,
  fetchUsers,
  pagePermissionsToPermissions,
  permissionsToPagePermissions,
  User as SupabaseUser,
  updateUserPermissions,
} from "@/services/userService";
import { AlertCircle, Loader, PlusCircle, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";

import { useAuth } from "@/contexts/AuthContext";
import apiClient from "../../lib/api";
import { toast } from "../ui/use-toast";
import UserPermissionsModal from "./UserPermissionsModal";

interface UserProfile {
  name: string;
  email: string;
  avatar_url?: string;
  id: string;
}

// Interface matching the type used in userService.permissionsToPagePermissions
interface PagePermissions {
  edit: boolean;
  create: boolean;
  delete: boolean;
  view: boolean;
}

interface City {
  id: string;
  labelEl: string;
  labelEn: string;
  country: string;
}

const SettingsView = () => {
  const navigate = useNavigate();
  const [selectedUser, setSelectedUser] = useState<SupabaseUser | null>(null);
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  // State for page permissions, matching the return type of permissionsToPagePermissions
  const [pagePermissions, setPagePermissions] = useState<
    Record<string, PagePermissions>
  >({});
  const [activeTab, setActiveTab] = useState("profile");
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isPermissionsModalOpen, setIsPermissionsModalOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    email: "",
    first_name: "",
    last_name: "",
    address: "",
    phone_number: "",
  });
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    bio: "",
    avatar: "",
  });

  // Users management
  const [users, setUsers] = useState<SupabaseUser[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(false);
  const [isSavingPermissions, setIsSavingPermissions] = useState(false);
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const [loadingUserId, setLoadingUserId] = useState<number | null>(null);

  // Cities management
  const [cities, setCities] = useState<City[]>([]);
  const [isAddCityDialogOpen, setIsAddCityDialogOpen] = useState(false);
  const [newCity, setNewCity] = useState<City>({
    id: "",
    labelEl: "",
    labelEn: "",
    country: "",
  });
  const [editingCity, setEditingCity] = useState<City | null>(null);
  const [citySearchQuery, setCitySearchQuery] = useState("");

  useEffect(() => {
    getProfile();
    loadCities();
    loadUsers();
  }, []);

  // Load users from Supabase
  const loadUsers = async () => {
    setIsLoadingUsers(true);
    try {
      const usersData = await fetchUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("Error loading users:", error);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // Load cities from Supabase
  const loadCities = async () => {
    try {
      const citiesData = await fetchCities(true); // Force refresh to get the latest data
      setCities(citiesData);
    } catch (error) {
      console.error("Error loading cities:", error);
      // If there's an error, we'll use the cities from staticData
    }
  };

  // Add new city
  const handleAddCity = async () => {
    if (newCity.id && newCity.labelEl && newCity.labelEn && newCity.country) {
      // Generate ID if not provided
      const cityId =
        newCity.id ||
        `${newCity.labelEn.toLowerCase().replace(/\s+/g, "_")}_${newCity.country
          .toLowerCase()
          .substring(0, 2)}`;

      const cityToAdd = {
        ...newCity,
        id: cityId,
      };

      try {
        // Add city to Supabase
        const success = await addCity(cityToAdd);

        if (success) {
          // Reload cities from Supabase
          await loadCities();
          setIsAddCityDialogOpen(false);
          setNewCity({ id: "", labelEl: "", labelEn: "", country: "" });
        } else {
          console.error("Failed to add city");
        }
      } catch (error) {
        console.error("Error adding city:", error);
      }
    }
  };

  // Edit city
  const handleEditCity = (city: City) => {
    setEditingCity(city);
    setNewCity(city);
    setIsAddCityDialogOpen(true);
  };

  // Update city
  const handleUpdateCity = async () => {
    if (editingCity && newCity.labelEl && newCity.labelEn && newCity.country) {
      try {
        // Update city in Supabase
        const success = await updateCity(newCity);

        if (success) {
          // Reload cities from Supabase
          await loadCities();
          setIsAddCityDialogOpen(false);
          setEditingCity(null);
          setNewCity({ id: "", labelEl: "", labelEn: "", country: "" });
        } else {
          console.error("Failed to update city");
        }
      } catch (error) {
        console.error("Error updating city:", error);
      }
    }
  };

  // Delete city
  const handleDeleteCity = async (cityId: string) => {
    try {
      // Delete city from Supabase
      const success = await deleteCity(cityId);

      if (success) {
        // Reload cities from Supabase
        await loadCities();
      } else {
        console.error("Failed to delete city");
      }
    } catch (error) {
      console.error("Error deleting city:", error);
    }
  };

  // Filter cities based on search query
  const filteredCities = cities.filter(
    (city) =>
      citySearchQuery === "" ||
      city.labelEl.toLowerCase().includes(citySearchQuery.toLowerCase()) ||
      city.labelEn.toLowerCase().includes(citySearchQuery.toLowerCase()) ||
      city.country.toLowerCase().includes(citySearchQuery.toLowerCase())
  );

  const getProfile = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        setProfile({
          id: user.id,
          email: user.email!,
          name: user.user_metadata.name || "User",
          avatar_url: user.user_metadata.avatar_url,
        });
        setFormData({
          name: user.user_metadata.name || "",
          email: user.email!,
          password: "",
          bio: user.user_metadata.bio || "",
          avatar: user.user_metadata.avatar_url || "",
        });
      }
    } catch (error) {
      console.error("Error loading user data:", error);
    }
  };

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      navigate("/");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleProfileUpdate = async () => {
    if (formData.password && formData.password.length > 6) {
      await supabase.auth.updateUser({
        password: formData.password,
      });
    } else if (formData.password && formData.password.length <= 6) {
      toast({
        title: "Μη έγκυρος κωδικός",
        description: "Ο κωδικός πρέπει να είναι μεγαλύτερος από 6 χαρακτήρες.",
        variant: "destructive",
      });
      return;
    }
    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.updateUser({
        email: formData.email,
        data: {
          name: formData.name,
          bio: formData.bio,
          avatar_url: formData.avatar,
        },
      });

      if (error) throw error;

      if (user) {
        setProfile({
          id: user.id,
          email: user.email!,
          name: user.user_metadata.name,
          avatar_url: user.user_metadata.avatar_url,
        });
        toast({
          title: "Επιτυχία",
          description: "Το προφίλ ενημερώθηκε επιτυχώς.",
        });
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία ενημέρωσης προφίλ.",
        variant: "destructive",
      });
    }
  };

  /**
   * Handle user selection and open the permissions modal
   * @param user The selected user
   */
  const handleUserSelect = async (user: SupabaseUser) => {
    setSelectedUser(user);
    setLoadingUserId(user.id);
    await loadUserPermissions(user.id);
    setLoadingUserId(null);
    setIsPermissionsModalOpen(true);
  };

  // Load user permissions from the user service
  const loadUserPermissions = async (userId: number) => {
    setIsLoadingPermissions(true);
    setPermissionError(null);
    try {
      // Fetch permissions from the user service
      const permissions = await fetchUserPermissions(userId);
      // Convert permission IDs to page permissions using the utility function
      const pagePerms = permissionsToPagePermissions(permissions);
      // Update state with the mapped permissions
      setPagePermissions(pagePerms);
    } catch (error) {
      console.error(`Error loading permissions for user ${userId}:`, error);
      setPermissionError("Failed to load user permissions. Please try again.");
    } finally {
      setIsLoadingPermissions(false);
    }
  };

  // Save user permissions to the user service
  const saveUserPermissions = async () => {
    if (!selectedUser) return;

    setIsSavingPermissions(true);
    setPermissionError(null);
    try {
      // Convert page permissions back to permission IDs using the utility function
      const permissionIds = pagePermissionsToPermissions(pagePermissions);
      // Update permissions in the user service
      const success = await updateUserPermissions(
        selectedUser.id,
        permissionIds
      );

      if (!success) {
        throw new Error("Failed to update permissions");
      }

      return true; // Return success for the modal to handle
    } catch (error) {
      console.error(
        `Error saving permissions for user ${selectedUser.id}:`,
        error
      );
      setPermissionError("Failed to save user permissions. Please try again.");
      throw error; // Throw the error for the modal to handle
    } finally {
      setIsSavingPermissions(false);
    }
  };

  const handleAddUser = async () => {
    if (newUser.first_name && newUser.last_name && newUser.email) {
      try {
        await apiClient.post("/users/", newUser);
        toast({
          title: "Επιτυχία",
          description: "Ο χρήστης προστέθηκε επιτυχώς.",
        });
      } catch (error) {
        console.error("Error adding user:", error);
        toast({
          title: "Σφάλμα",
          description: "Αποτυχία προσθήκης χρήστη.",
          variant: "destructive",
        });
      }
      await loadUsers();
      setNewUser({
        email: "",
        first_name: "",
        last_name: "",
        address: "",
        phone_number: "",
      });
      setIsAddUserDialogOpen(false);
    }
  };

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setFormData({ ...formData, avatar: base64String });
      };
      reader.readAsDataURL(file);
    }
  };

  /**
   * Handle permission change for a specific page and action
   * @param page The page name (e.g., "dashboard", "crm", etc.)
   * @param type The permission type (e.g., "view", "create", "edit", "delete")
   * @param checked Whether the permission is checked or not
   */
  const handlePermissionChange = (
    page: string,
    type: keyof PagePermissions,
    checked: boolean
  ) => {
    setPagePermissions((prev) => ({
      ...prev,
      [page.toLowerCase()]: {
        ...prev[page.toLowerCase()],
        [type]: checked,
      },
    }));
  };

  const filteredUsers = users.filter(
    (user) =>
      searchQuery === "" ||
      (user.username &&
        user.username.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (user.email &&
        user.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
      `${user.first_name} ${user.last_name}`
        .toLowerCase()
        .includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto py-6 px-4 bg-white min-h-screen">
      {/* User Info Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mb-8">
        <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center text-lg font-semibold overflow-hidden">
          {profile?.avatar_url ? (
            <img
              src={profile.avatar_url}
              alt={profile.name}
              className="h-full w-full object-cover"
            />
          ) : (
            profile?.name[0]
          )}
        </div>
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">{profile?.name}</h2>
          <Tabs
            defaultValue={activeTab}
            onValueChange={setActiveTab}
            className="w-full sm:w-auto"
          >
            <TabsList className="w-full sm:w-auto grid grid-cols-3 sm:flex bg-light/20 border border-light/30">
              <TabsTrigger
                value="profile"
                className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
              >
                Προφίλ
              </TabsTrigger>
              <TabsTrigger
                value="permissions"
                className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
              >
                Δικαιώματα
              </TabsTrigger>
              <TabsTrigger
                value="cities"
                className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
              >
                Πόλεις
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsContent value="profile">
          <Card>
            <CardContent className="pt-6">
              <div className="max-w-2xl space-y-8">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Όνομα</label>
                    <Input
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      className="input-primary"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <Input
                      value={formData.email}
                      onChange={(e) =>
                        setFormData({ ...formData, email: e.target.value })
                      }
                      className="input-primary"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Κωδικός</label>
                    <Input
                      value={formData.password}
                      onChange={(e) =>
                        setFormData({ ...formData, password: e.target.value })
                      }
                      type="password"
                      className="input-primary"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Bio</label>
                    <Textarea
                      value={formData.bio}
                      onChange={(e) =>
                        setFormData({ ...formData, bio: e.target.value })
                      }
                      className="min-h-[100px]"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Αλλαγή εικόνας
                    </label>
                    <div className="flex flex-col sm:flex-row items-start gap-4">
                      <div className="w-20 h-20 sm:w-24 sm:h-24 border-2 border-dashed rounded-lg flex items-center justify-center overflow-hidden">
                        {formData.avatar ? (
                          <img
                            src={formData.avatar}
                            alt="Profile"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="text-muted-foreground"
                          >
                            <rect
                              width="18"
                              height="18"
                              x="3"
                              y="3"
                              rx="2"
                              ry="2"
                            />
                            <circle cx="9" cy="9" r="2" />
                            <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
                          </svg>
                        )}
                      </div>
                      <div className="space-y-2 flex-1">
                        <input
                          type="file"
                          onChange={handleAvatarChange}
                          accept="image/*"
                          className="hidden"
                          id="avatar-upload"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            document.getElementById("avatar-upload")?.click()
                          }
                          className="w-full sm:w-auto"
                        >
                          Επιλογή Αρχείου
                        </Button>
                        <p className="text-xs text-muted-foreground">
                          Το αρχείο πρέπει να μην ξεπερνά τα 100KB
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formData.avatar
                            ? "Αρχείο επιλέχθηκε."
                            : "Δεν επιλέχθηκε αρχείο"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="outline"
                    onClick={handleLogout}
                    className="w-full sm:w-auto"
                  >
                    Αποσύνδεση
                  </Button>
                  <Button
                    variant="gradient"
                    className="w-full sm:w-auto"
                    onClick={handleProfileUpdate}
                  >
                    Ανανέωση Προφίλ
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cities">
          <div className="space-y-6">
            {/* Cities Management Section */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                    Διαχείριση Πόλεων
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => {
                      setEditingCity(null);
                      setNewCity({
                        id: "",
                        labelEl: "",
                        labelEn: "",
                        country: "",
                      });
                      setIsAddCityDialogOpen(true);
                    }}
                  >
                    <PlusCircle className="h-4 w-4 mr-1" />
                    Προσθήκη Πόλης
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Προσθήκη, επεξεργασία και διαγραφή πόλεων για τις φόρμες.
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <Input
                      placeholder="Αναζήτηση πόλης..."
                      className="w-full sm:max-w-sm"
                      value={citySearchQuery}
                      onChange={(e) => setCitySearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-4">ID</th>
                          <th className="text-left py-2 px-4">Ελληνικά</th>
                          <th className="text-left py-2 px-4">Αγγλικά</th>
                          <th className="text-left py-2 px-4">Χώρα</th>
                          <th className="text-right py-2 px-4">Ενέργειες</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredCities.length === 0 ? (
                          <tr>
                            <td
                              colSpan={5}
                              className="text-center py-4 text-muted-foreground"
                            >
                              {citySearchQuery
                                ? "Δεν βρέθηκαν πόλεις."
                                : "Δεν υπάρχουν πόλεις. Προσθέστε μια νέα πόλη."}
                            </td>
                          </tr>
                        ) : (
                          filteredCities.map((city) => (
                            <tr
                              key={city.id}
                              className="border-b hover:bg-muted/50"
                            >
                              <td className="py-2 px-4">{city.id}</td>
                              <td className="py-2 px-4">{city.labelEl}</td>
                              <td className="py-2 px-4">{city.labelEn}</td>
                              <td className="py-2 px-4">{city.country}</td>
                              <td className="py-2 px-4 text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEditCity(city)}
                                  >
                                    Επεξεργασία
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-destructive"
                                    onClick={() => handleDeleteCity(city.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="permissions">
          <div className="space-y-6">
            {/* User Management Section */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                    Διαχείριση χρηστών και δικαιωμάτων
                  </CardTitle>
                  {user && user.app_metadata?.role === "admin" && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs"
                      onClick={() => setIsAddUserDialogOpen(true)}
                    >
                      <PlusCircle className="h-4 w-4 mr-1" />
                      Προσθήκη Χρήστη
                    </Button>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  Διαχείριση χρηστών και των δικαιωμάτων τους στο σύστημα.
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <Input
                    placeholder="Αναζήτηση χρήστη..."
                    className="w-full sm:max-w-sm"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadUsers}
                    className="w-full sm:w-auto"
                  >
                    Ανανέωση
                  </Button>
                </div>

                {isLoadingUsers ? (
                  <div className="flex justify-center py-8">
                    <Loader className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredUsers.length === 0 ? (
                      <p className="text-sm text-muted-foreground text-center py-4">
                        {searchQuery !== ""
                          ? "Δεν βρέθηκαν χρήστες που να ταιριάζουν με την αναζήτηση."
                          : "Δεν υπάρχουν χρήστες στο σύστημα."}
                      </p>
                    ) : (
                      filteredUsers.map((user) => (
                        <div
                          key={user.id}
                          className={`flex flex-col sm:flex-row items-start sm:items-center justify-between py-2 px-4 rounded-lg cursor-pointer hover:bg-muted/50 ${
                            selectedUser?.id === user.id ? "bg-muted" : ""
                          }`}
                          onClick={() => handleUserSelect(user)}
                        >
                          <div className="flex items-center gap-3 mb-2 sm:mb-0">
                            <Avatar>
                              <AvatarFallback>
                                {user.first_name?.[0] ||
                                  user.username?.[0] ||
                                  "U"}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">
                                {user.first_name && user.last_name
                                  ? `${user.first_name} ${user.last_name}`
                                  : user.username}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {user.email}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {loadingUserId === user.id ? (
                              <div className="flex items-center gap-1">
                                <Loader className="h-3 w-3 animate-spin text-primary" />
                                <span className="text-xs text-muted-foreground">
                                  Φόρτωση...
                                </span>
                              </div>
                            ) : (
                              <Badge variant="outline" className="text-xs">
                                {user.user_type === 1 ? "Admin" : "User"}
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )}

                {!isLoadingUsers && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle className="text-sm">
                      Επιλέξτε έναν χρήστη για να διαχειριστείτε τα δικαιώματά
                      του.
                    </AlertTitle>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Add User Dialog */}
      <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Προσθήκη Χρήστη</DialogTitle>
            <DialogDescription>
              Συμπληρώστε τα στοιχεία του νέου χρήστη.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Όνομα Χρήστη</label>
              <Input
                value={newUser.first_name}
                onChange={(e) =>
                  setNewUser({ ...newUser, first_name: e.target.value })
                }
                placeholder="π.χ. Γιώργος"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Επίθετο</label>
              <Input
                value={newUser.last_name}
                onChange={(e) =>
                  setNewUser({ ...newUser, last_name: e.target.value })
                }
                placeholder="π.χ. Παπαδόπουλος"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Email</label>
              <Input
                value={newUser.email}
                onChange={(e) =>
                  setNewUser({ ...newUser, email: e.target.value })
                }
                placeholder="π.χ. <EMAIL>"
                type="email"
              />
              <label className="text-sm font-medium">Διεύθυνση</label>
              <Input
                value={newUser.address}
                onChange={(e) =>
                  setNewUser({ ...newUser, address: e.target.value })
                }
                placeholder="π.χ. Καραολή και Παύλου 12, Αθήνα"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Τηλέφωνο</label>
              <Input
                value={newUser.phone_number}
                onChange={(e) =>
                  setNewUser({ ...newUser, phone_number: e.target.value })
                }
                placeholder="π.χ. 6941234567"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddUserDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              Ακύρωση
            </Button>
            <Button
              variant="gradient"
              onClick={handleAddUser}
              disabled={
                !newUser.first_name || !newUser.last_name || !newUser.email
              }
              className="w-full sm:w-auto"
            >
              Προσθήκη
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* User Permissions Modal */}
      <UserPermissionsModal
        isAdmin={user?.app_metadata?.role === "admin"}
        isOpen={isPermissionsModalOpen}
        onClose={() => setIsPermissionsModalOpen(false)}
        selectedUser={selectedUser}
        pagePermissions={pagePermissions}
        isLoadingPermissions={isLoadingPermissions}
        permissionError={permissionError}
        isSavingPermissions={isSavingPermissions}
        onPermissionChange={handlePermissionChange}
        onSavePermissions={saveUserPermissions}
      />

      {/* Add/Edit City Dialog */}
      <Dialog open={isAddCityDialogOpen} onOpenChange={setIsAddCityDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {editingCity ? "Επεξεργασία Πόλης" : "Προσθήκη Πόλης"}
            </DialogTitle>
            <DialogDescription>
              {editingCity
                ? "Επεξεργαστείτε τα στοιχεία της επιλεγμένης πόλης."
                : "Συμπληρώστε τα στοιχεία της νέας πόλης."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">ID</label>
              <Input
                value={newCity.id}
                onChange={(e) => setNewCity({ ...newCity, id: e.target.value })}
                placeholder="π.χ. athens_gr"
                disabled={!!editingCity}
              />
              <p className="text-xs text-muted-foreground">
                {editingCity
                  ? "Το ID δεν μπορεί να αλλάξει"
                  : "Προαιρετικό. Θα δημιουργηθεί αυτόματα αν δεν συμπληρωθεί."}
              </p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Όνομα (Ελληνικά)</label>
              <Input
                value={newCity.labelEl}
                onChange={(e) =>
                  setNewCity({ ...newCity, labelEl: e.target.value })
                }
                placeholder="π.χ. Αθήνα"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Όνομα (Αγγλικά)</label>
              <Input
                value={newCity.labelEn}
                onChange={(e) =>
                  setNewCity({ ...newCity, labelEn: e.target.value })
                }
                placeholder="π.χ. Athens"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Χώρα</label>
              <Input
                value={newCity.country}
                onChange={(e) =>
                  setNewCity({ ...newCity, country: e.target.value })
                }
                placeholder="π.χ. Greece"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAddCityDialogOpen(false);
                setEditingCity(null);
                setNewCity({ id: "", labelEl: "", labelEn: "", country: "" });
              }}
              className="w-full sm:w-auto"
            >
              Ακύρωση
            </Button>
            <Button
              variant="gradient"
              onClick={editingCity ? handleUpdateCity : handleAddCity}
              disabled={
                !newCity.labelEl || !newCity.labelEn || !newCity.country
              }
              className="w-full sm:w-auto"
            >
              {editingCity ? "Ενημέρωση" : "Προσθήκη"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SettingsView;
