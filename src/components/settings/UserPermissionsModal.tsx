import { Alert, AlertDescription } from "@/components/ui/alert";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { User as SupabaseUser } from "@/services/userService";
import { AlertCircle, Loader } from "lucide-react";
import { useEffect, useRef } from "react";

// Interface matching the type used in userService.permissionsToPagePermissions
interface PagePermissions {
  edit: boolean;
  create: boolean;
  delete: boolean;
  view: boolean;
}

interface UserPermissionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedUser: SupabaseUser | null;
  pagePermissions: Record<string, PagePermissions>;
  isLoadingPermissions: boolean;
  permissionError: string | null;
  isSavingPermissions: boolean;
  onPermissionChange: (
    page: string,
    type: keyof PagePermissions,
    checked: boolean
  ) => void;
  isAdmin: boolean;
  onSavePermissions: () => Promise<boolean | void>;
}

// Define the permission pages
const PERMISSION_PAGES = [
  {
    name: "Connect",
    page: "connect",
    desc: "Προβολή της σελίδας σύνδεσης",
    actions: ["view"], // Only view permission is available
  },
  {
    name: "CRM",
    page: "crm",
    desc: "Ενέργειες στο σύστημα διαχείρισης πελατών",
    actions: ["view", "create", "edit", "delete"], // All permissions available
  },
  {
    name: "Nannies",
    page: "nannies",
    desc: "Ενέργειες στην πλατφόρμα των nannies",
    actions: ["view", "create", "edit", "delete"], // All permissions available
  },
  {
    name: "Tutors",
    page: "tutors",
    desc: "Ενέργειες στην πλατφόρμα των καθηγητών",
    actions: ["view", "create", "edit", "delete"], // All permissions available
  },
  {
    name: "Candidates",
    page: "candidates",
    desc: "Ενέργειες στην πλατφόρμα των υποψηφίων",
    actions: ["view", "create", "edit", "delete"], // All permissions available
  },
  {
    page: "deals",
    name: "Deals",
    desc: "Ενέργειες στις συμφωνίες",
    actions: ["view", "create", "edit", "delete"], // All permissions available
  },
  {
    page: "analytics",
    name: "Analytics",
    desc: "Προβολή και επεξεργασία αναλυτικών στοιχείων",
    actions: ["view", "edit"], // Only view and edit permissions available
  },
  {
    page: "activities",
    name: "Activities Feed",
    desc: "Προβολή ροής δραστηριοτήτων",
    actions: ["view"], // Only view permission is available
  },
];

const UserPermissionsModal = ({
  isOpen,
  onClose,
  selectedUser,
  pagePermissions,
  isLoadingPermissions,
  permissionError,
  isSavingPermissions,
  onPermissionChange,
  onSavePermissions,
  isAdmin,
}: UserPermissionsModalProps) => {
  // Reference to the save button for focus management
  const saveButtonRef = useRef<HTMLButtonElement>(null);
  const cancelButtonRef = useRef<HTMLButtonElement>(null);
  const { user } = useAuth();

  // Focus the save button when the modal opens
  useEffect(() => {
    if (isOpen && !isLoadingPermissions && saveButtonRef.current) {
      // Small delay to ensure the modal is fully rendered
      const timer = setTimeout(() => {
        saveButtonRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isOpen, isLoadingPermissions]);

  /**
   * Get the selected permissions for a specific page in human-readable format
   * @param page The page name (e.g., "dashboard", "crm", etc.)
   * @returns Array of human-readable permission names
   */
  const getSelectedPermissions = (page: string): string[] => {
    // Get the permissions for the page or an empty object if not found
    const permissions = pagePermissions[page] || {};

    // Filter the permissions that are true and map them to human-readable names
    return Object.entries(permissions)
      .filter(([_, value]) => value)
      .map(([key]) => {
        // Map the permission key to a human-readable name in Greek
        switch (key) {
          case "edit":
            return "Επεξεργασία";
          case "create":
            return "Δημιουργία";
          case "delete":
            return "Διαγραφή";
          case "view":
            return "Προβολή";
          default:
            return "";
        }
      });
  };

  // Handle save with feedback
  const handleSave = async () => {
    try {
      const success = await onSavePermissions();
      if (success && selectedUser && user) {
        // Notify the affected user about the permission change
        // This will trigger the onAuthStateChange event for the affected user
        try {
          console.debug(
            `Permission change notification sent to user ${selectedUser.id}`
          );
        } catch (error) {
          console.error("Error notifying user about permission change:", error);
        }

        toast({
          title: "Επιτυχία",
          description: "Τα δικαιώματα ενημερώθηκαν επιτυχώς.",
        });
        onClose();
      }
    } catch (error) {
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία ενημέρωσης δικαιωμάτων.",
        variant: "destructive",
      });
    }
  };

  if (!selectedUser) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={`sm:max-w-[800px] max-h-[90vh] overflow-y-auto ${
          isLoadingPermissions ? "opacity-90" : ""
        }`}
      >
        <DialogHeader>
          <DialogTitle>Διαχείριση Δικαιωμάτων Χρήστη</DialogTitle>
          <DialogDescription>
            Διαχειριστείτε τα δικαιώματα του χρήστη για κάθε ενότητα της
            εφαρμογής.
          </DialogDescription>
        </DialogHeader>

        {permissionError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{permissionError}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback>
                {selectedUser.first_name?.[0] ||
                  selectedUser.username?.[0] ||
                  "U"}
              </AvatarFallback>
            </Avatar>
            <div>
              <span className="font-medium">
                {selectedUser.first_name && selectedUser.last_name
                  ? `${selectedUser.first_name} ${selectedUser.last_name}`
                  : selectedUser.username}
              </span>
              <p className="text-sm text-muted-foreground">
                {selectedUser.email}
              </p>
            </div>
          </div>

          {isLoadingPermissions ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Loader className="h-8 w-8 animate-spin text-primary mb-2" />
              <p className="text-sm text-muted-foreground">
                Φόρτωση δικαιωμάτων...
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {PERMISSION_PAGES.map((page) => (
                <div
                  key={page.name}
                  className="flex flex-col sm:flex-row items-start sm:items-center justify-between py-2 border-b pb-4"
                >
                  <div className="mb-2 sm:mb-0">
                    <p className="font-medium">{page.name}</p>
                    <p className="text-sm text-muted-foreground">{page.desc}</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {getSelectedPermissions(page.page).map((permission) => (
                        <Badge
                          key={permission}
                          variant="secondary"
                          className="text-xs"
                        >
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    {/* View permission - available for all pages */}
                    {page.actions.includes("view") && (
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id={`modal-view-${page.name}`}
                          checked={pagePermissions[page.page]?.view}
                          onCheckedChange={(checked) =>
                            onPermissionChange(
                              page.page,
                              "view",
                              checked as boolean
                            )
                          }
                        />
                        <label
                          htmlFor={`modal-view-${page.name}`}
                          className="text-sm"
                        >
                          Προβολή
                        </label>
                      </div>
                    )}

                    {/* Create permission - only for pages that support it */}
                    {page.actions.includes("create") && (
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id={`modal-create-${page.name}`}
                          checked={pagePermissions[page.page]?.create}
                          onCheckedChange={(checked) =>
                            onPermissionChange(
                              page.page,
                              "create",
                              checked as boolean
                            )
                          }
                        />
                        <label
                          htmlFor={`modal-create-${page.name}`}
                          className="text-sm"
                        >
                          Δημιουργία
                        </label>
                      </div>
                    )}

                    {/* Edit permission - only for pages that support it */}
                    {page.actions.includes("edit") && (
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id={`modal-edit-${page.name}`}
                          checked={pagePermissions[page.page]?.edit}
                          onCheckedChange={(checked) =>
                            onPermissionChange(
                              page.page,
                              "edit",
                              checked as boolean
                            )
                          }
                        />
                        <label
                          htmlFor={`modal-edit-${page.name}`}
                          className="text-sm"
                        >
                          Επεξεργασία
                        </label>
                      </div>
                    )}

                    {/* Delete permission - only for pages that support it */}
                    {page.actions.includes("delete") && (
                      <div className="flex items-center gap-2">
                        <Checkbox
                          id={`modal-delete-${page.name}`}
                          checked={pagePermissions[page.page]?.delete}
                          onCheckedChange={(checked) =>
                            onPermissionChange(
                              page.page,
                              "delete",
                              checked as boolean
                            )
                          }
                        />
                        <label
                          htmlFor={`modal-delete-${page.name}`}
                          className="text-sm"
                        >
                          Διαγραφή
                        </label>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter className="mt-6 flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            ref={cancelButtonRef}
            disabled={isSavingPermissions}
          >
            Ακύρωση
          </Button>
          <Button
            variant="gradient"
            onClick={handleSave}
            disabled={isSavingPermissions || isLoadingPermissions || !isAdmin}
            ref={saveButtonRef}
          >
            {isSavingPermissions ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Αποθήκευση...
              </>
            ) : (
              "Αποθήκευση Δικαιωμάτων"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserPermissionsModal;
