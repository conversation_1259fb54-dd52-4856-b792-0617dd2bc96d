import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import TutorsTable from "./TutorsTable";

interface TutorsTabsProps {
  onTabChange?: (value: string) => void;
}

const TutorsTabs = ({ onTabChange }: TutorsTabsProps) => {
  return (
    <Tabs defaultValue="tutors" className="w-full" onValueChange={onTabChange}>
      <TabsList className="w-full justify-start border-b rounded-none h-12 bg-transparent backdrop-blur-sm">
        <TabsTrigger
          value="tutors"
          className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
        >
          Tutors
        </TabsTrigger>
        <TabsTrigger
          value="activities"
          className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
        >
          Activities
        </TabsTrigger>
      </TabsList>
      <TabsContent value="tutors" className="p-0 border-none">
        <TutorsTable />
      </TabsContent>
      <TabsContent value="activities" className="p-0 border-none"></TabsContent>
    </Tabs>
  );
};

export default TutorsTabs;
