import { Badge } from "@/components/ui/badge";
import { languageLevels } from "@/lib/staticData";
import {
  getCityLabel,
  getLanguageLabel,
  getLanguageOptions,
  getPositionLabel,
  getPositionOptions,
} from "@/lib/tableUtils";
import { startDateOptions } from "@/schemas/FormSchema";
import {
  Candidate,
  fetchTutors,
  rejectCandidate,
} from "@/services/candidateService";
import { User, UserCircle, UserRound } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import NannyBlueTable from "../common/NannyBlueTable";
import { ColumnConfig } from "../crm/BasicTable";
import CandidateContextMenu from "../nannies/CandidateContextMenu";
import { toast } from "../ui/use-toast";

const TutorsTable = () => {
  const navigate = useNavigate();
  const [shouldReload, setShouldReload] = useState(false);

  const columns: ColumnConfig<Candidate | any>[] = [
    {
      key: "id",
      header: "ID",
      filterable: true,
      filterType: "text",
      sortable: true,
      render: (item) => {
        const gender = item.form_data?.gender?.toLowerCase();

        return (
          <div className="flex items-center gap-2">
            {gender === "male" ? (
              <UserCircle className="h-4 w-4 text-blue-500" />
            ) : gender === "female" ? (
              <UserRound className="h-4 w-4 text-pink-500" />
            ) : (
              <User className="h-4 w-4 text-gray-500" />
            )}
            <span>TUTOR-{item.id}</span>
          </div>
        );
      },
    },
    {
      key: "form_data.name",
      header: "Όνομα",
      filterable: true,
      filterType: "text",
      render: (item) => item.form_data?.name || "-",
    },
    {
      key: "form_data.surname",
      header: "Επώνυμο",
      filterable: true,
      filterType: "text",
      render: (item) => item.form_data?.surname || "-",
    },
    {
      key: "form_data.duration_interests",
      header: "Διάρκεια",
      filterable: true,
      filterType: "select",
      options: ["Long-Term", "Short-Term"],

      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {Array.isArray(item.form_data.duration_interests) &&
          item.form_data.duration_interests.length > 0 ? (
            item.form_data.duration_interests.map((duration) => (
              <Badge key={duration} variant="secondary">
                {duration
                  .split("-")
                  .map((word) => word[0].toUpperCase() + word.slice(1))
                  .join("-")}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.schedule_interests",
      header: "Είδος",
      filterable: true,
      filterType: "select",
      options: ["Full-Time", "Part-Time", "Occasional"],
      filterFn: (row, _columnId, filterValue) => {
        const schedules = row.form_data?.schedule_interests;
        if (!schedules || !Array.isArray(schedules) || schedules.length === 0)
          return false;

        // Format the schedule strings to match the display format
        const formattedSchedules = schedules.map((schedule) =>
          schedule
            .split("-")
            .map((word) => word[0].toUpperCase() + word.slice(1))
            .join("-")
        );

        return formattedSchedules.some((schedule) =>
          schedule.includes(filterValue)
        );
      },
      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {Array.isArray(item.form_data?.schedule_interests) &&
          item.form_data?.schedule_interests.length > 0 ? (
            item.form_data?.schedule_interests.map((schedule) => (
              <Badge key={schedule} variant="outline">
                {schedule
                  .split("-")
                  .map((word) => word[0].toUpperCase() + word.slice(1))
                  .join("-")}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.position_interests",
      header: "Θέση",
      filterable: true,
      filterType: "select",
      options: getPositionOptions(),
      filterFn: (row, _columnId, filterValue) => {
        const positions = row.form_data?.position_interests;
        if (!positions || !Array.isArray(positions) || positions.length === 0)
          return false;
        return positions.some((position) =>
          getPositionLabel(position).includes(filterValue)
        );
      },
      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {Array.isArray(item.form_data?.position_interests) &&
          item.form_data?.position_interests.length > 0 ? (
            item.form_data?.position_interests.map((position) => (
              <Badge key={position} variant="outline">
                {getPositionLabel(position)}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.languages",
      header: "Γλώσσα",
      filterable: true,
      filterType: "select",
      options: getLanguageOptions(),

      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {item.form_data?.languages &&
            Array.isArray(item.form_data?.languages) &&
            item.form_data?.languages.map((lang, index) => (
              <Badge key={index} variant="outline">
                <span>{getLanguageLabel(lang.language)}</span>
                <span className="text-xs text-gray-500">
                  (
                  {languageLevels.find((level) => level.id === lang.level)
                    ?.labelEl || lang.level}
                  )
                </span>
              </Badge>
            ))}
        </div>
      ),
    },
    {
      key: "form_data.start_availability",
      header: "Έναρξη",
      filterable: true,
      filterType: "select",
      options: startDateOptions.map((option) => option.labelEl),
      render: (item) => {
        const startDateOption = startDateOptions.find(
          (option) => option.id === item.form_data?.start_availability
        );
        return (
          <div className="flex flex-wrap gap-1">
            {startDateOption ? startDateOption.labelEl : "Δεν δηλώθηκε"}
          </div>
        );
      },
    },

    {
      key: "form_data.city",
      header: "Τοποθεσία",
      filterable: true,
      filterType: "city-select",
      render: (item) => getCityLabel(item.form_data?.city),
    },
  ];

  const handleReject = async (nanny: Candidate) => {
    try {
      const success = await rejectCandidate(nanny.id.toString());

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Απορρίφθηκε: ${nanny.form_data?.name} ${nanny.form_data?.surname}`,
        });
        setShouldReload((prev) => !prev);
      } else {
        throw new Error("Failed to reject tutor");
      }
    } catch (error) {
      console.error("Error rejecting tutor:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η απόρριψη του δασκάλου.",
        variant: "destructive",
      });
    }
  };

  // Use the reusable context menu component
  const { contextMenuItems, dialogComponents } = CandidateContextMenu({
    candidateType: "tutor",
    onReject: handleReject,
    onReload: () => setShouldReload((prev) => !prev),
  });

  return (
    <div className="space-y-4">
      <>
        <NannyBlueTable
          fetchData={fetchTutors}
          columns={columns}
          onRowClick={(row) => navigate(`/tutors/${row.id}`)}
          contextMenuItems={contextMenuItems}
          shouldReload={shouldReload}
        />
        {dialogComponents}
      </>
    </div>
  );
};

export default TutorsTable;
