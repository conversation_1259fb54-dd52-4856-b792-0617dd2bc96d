import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Sparkles } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useFormContext as useRHFFormContext } from "react-hook-form";
import { useCandidateFormContext } from "../../contexts/CandidateFormContext";
import { useFormContext } from "../../contexts/FormContext";
import { useNannyRequestFormContext } from "../../contexts/NannyRequestFormContext";
import {
  FormData,
  formSteps,
  nannyRequestFormSteps,
} from "../../schemas/FormSchema";
import { getText } from "../../utils/form-utils";
import { Button } from "./button";

interface FormControlsProps {
  submitForm: () => void;
  isLastStep: boolean;
  isCandidateForm?: boolean;
}

export const FormControls: React.FC<FormControlsProps> = ({
  submitForm,
  isLastStep,
  isCandidateForm = true,
}) => {
  // Determine which context to use based on form type
  const useAppropriateContext = () => {
    if (isCandidateForm) {
      try {
        return useCandidateFormContext();
      } catch (error) {
        return useFormContext(); // Fallback to the original context
      }
    } else {
      try {
        return useNannyRequestFormContext();
      } catch (error) {
        return useFormContext(); // Fallback to the original context
      }
    }
  };

  const {
    prevStep,
    nextStep,
    isFirstStep,
    language,
    currentStep,
    validateCurrentStep,
    setValidationAttempted,
  } = useAppropriateContext();
  const form = useRHFFormContext<FormData>();

  // Get the current step's schema based on form type
  const steps = isCandidateForm ? formSteps : nannyRequestFormSteps; // Use appropriate steps based on form type
  const currentStepSchema =
    steps[Math.min(currentStep, steps.length - 1)].schema;

  // Extract field names from the current step's schema
  const currentStepFields = Object.keys(currentStepSchema.shape || {});

  // Track local validation state
  const [localValidationAttempted, setLocalValidationAttempted] =
    useState(false);

  // Reset validation attempted flag when changing steps
  useEffect(() => {
    setLocalValidationAttempted(false);
    setValidationAttempted(false);
  }, [currentStep, setValidationAttempted]);

  // Keep track of steps that have been visited/validated
  const [validatedSteps, setValidatedSteps] = useState<number[]>([0]);

  // Setup to clear errors on change for individual fields once validation has been attempted
  useEffect(() => {
    if (localValidationAttempted) {
      // Setup field-by-field validation after the user has attempted to proceed
      const subscription = form.watch((_, { name, type }) => {
        // If a field has changed and validation was attempted, validate just that field
        if (name && type === "change" && currentStepFields.includes(name)) {
          // Validate the field immediately when it changes
          setTimeout(() => {
            form.trigger(name as any);
          }, 100); // Small delay to ensure the value has been updated
        }
      });

      // Cleanup subscription
      return () => subscription.unsubscribe();
    }
  }, [form, currentStepFields, localValidationAttempted]);

  // State for confetti effect on submit
  const [showConfetti, setShowConfetti] = useState(false);

  // We'll always allow navigation, but validation will be checked before submission

  // Button styling - consistent for both form types
  const getButtonStyles = () => {
    return {
      prev: "rounded-full px-6 border-light/30 hover:bg-primary/10 hover:text-primary transition-all duration-300 group hover-float",
      next: "rounded-full px-8 py-2 bg-gradient-to-r from-primary to-secondary hover:from-primary-hover hover:to-secondary-hover text-white transition-all duration-300 group hover-float",
      submit:
        "relative rounded-full px-8 py-6 bg-gradient-to-r from-primary to-secondary hover:from-primary-hover hover:to-secondary-hover text-white transition-all duration-300 group overflow-hidden hover-float",
    };
  };

  const buttonStyles = getButtonStyles();

  return (
    <div className="flex justify-between mt-12 relative">
      {/* Moving sparkles for back button on hover - for both form types */}
      <div className="absolute -left-5 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <Sparkles className="h-5 w-5 text-primary/50 animate-spin-slow" />
      </div>

      <Button
        type="button"
        variant="outline"
        onClick={prevStep}
        disabled={isFirstStep}
        className={buttonStyles.prev}
      >
        <ArrowLeft className="mr-2 h-4 w-4 group-hover:animate-bounce-subtle" />
        {getText("Προηγούμενο / Previous", language)}
      </Button>

      {isLastStep ? (
        <div className="relative">
          {showConfetti && (
            <div className="absolute -top-24 left-1/2 transform -translate-x-1/2 pointer-events-none">
              <div className="relative w-40 h-40">
                {Array.from({ length: 20 }).map((_, i) => {
                  const size = Math.random() * 8 + 4;
                  const angle = Math.random() * 360;
                  const distance = Math.random() * 40 + 40;
                  const duration = Math.random() * 1 + 1.5;
                  const delay = Math.random() * 0.5;
                  const color =
                    i % 2 === 0
                      ? "hsl(var(--primary))"
                      : "hsl(var(--secondary))";

                  return (
                    <div
                      key={i}
                      className="absolute rounded-full animate-float"
                      style={{
                        width: `${size}px`,
                        height: `${size}px`,
                        backgroundColor: color,
                        transform: `rotate(${angle}deg) translateY(-${distance}px)`,
                        opacity: 0,
                        animation: `float ${duration}s ease-out ${delay}s forwards`,
                      }}
                    />
                  );
                })}
              </div>
            </div>
          )}

          <Button
            type="button"
            onClick={async () => {
              setLocalValidationAttempted(true);
              setValidationAttempted(true);
              const allValid = await form.trigger(); // Trigger validation for all fields
              if (allValid) {
                setShowConfetti(true);
                setTimeout(() => {
                  submitForm();
                }, 400);
              } else {
                // If validation fails, scroll to the first error
                const firstError = document.querySelector(
                  '[aria-invalid="true"]'
                );
                if (firstError) {
                  firstError.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                }
              }
            }}
            className={buttonStyles.submit}
          >
            <span className="relative z-10 flex items-center">
              {getText("Υποβολή / Submit", language)}
              <Send className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </span>

            {/* Animated styles for both form types */}
            <>
              <span className="absolute inset-0 bg-gradient-to-r from-primary-light to-secondary-light opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0"></span>
              <Heart
                className="absolute -top-2 -right-2 h-4 w-4 text-white opacity-0 group-hover:opacity-70 transition-opacity animate-float"
                style={{ animationDelay: "0.3s" }}
              />
              <Heart
                className="absolute -bottom-2 -left-2 h-3 w-3 text-white opacity-0 group-hover:opacity-70 transition-opacity animate-float"
                style={{ animationDelay: "0.7s" }}
              />
              <Sparkles
                className="absolute top-1 -left-3 h-4 w-4 text-white opacity-0 group-hover:opacity-70 transition-opacity animate-float"
                style={{ animationDelay: "0.5s" }}
              />
            </>
          </Button>
        </div>
      ) : (
        <Button
          type="button"
          onClick={async () => {
            try {
              // Use the validateCurrentStep function from the context
              setValidationAttempted(true);
              const isValid = await validateCurrentStep.current();

              if (isValid) {
                // Add current step to validated steps if not already included
                if (!validatedSteps.includes(currentStep)) {
                  setValidatedSteps([...validatedSteps, currentStep]);
                }
                nextStep();
              } else {
                console.debug("Validation failed for step", currentStep);
              }
            } catch (error) {
              console.error("Error during validation:", error);
            }
          }}
          className={buttonStyles.next}
        >
          <span className="flex items-center">
            {getText("Επόμενο / Next", language)}
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </span>

          {/* Add a subtle sparkle effect that shows on hover */}
          <Sparkles className="absolute top-0 right-0 h-4 w-4 text-white opacity-0 group-hover:opacity-70 animate-pulse-soft" />
        </Button>
      )}
    </div>
  );
};
