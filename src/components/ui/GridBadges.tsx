import React from "react";
import { Badge } from "./badge";

interface GridBadgesProps {
  items: string[];
  variant?: "default" | "primary" | "secondary" | "outline" | "destructive";
  maxItemsPerRow?: number;
  renderItem?: (item: string) => React.ReactNode;
  emptyMessage?: string;
}

/**
 * GridBadges component displays a collection of badges in a grid format
 * with a specified maximum number of items per row.
 */
const GridBadges: React.FC<GridBadgesProps> = ({
  items,
  variant = "outline",
  maxItemsPerRow = 2,
  renderItem,
  emptyMessage = "Δεν δηλώθηκε",
}) => {
  if (!items || items.length === 0) {
    return <Badge variant="destructive">{emptyMessage}</Badge>;
  }

  return (
    <div className="grid grid-cols-2 gap-1 w-full max-w-[200px]">
      {items.map((item, index) => (
        <div key={`${item}-${index}`} className="overflow-hidden text-ellipsis">
          {renderItem ? (
            renderItem(item)
          ) : (
            <Badge variant={variant} className="truncate max-w-full">
              {item}
            </Badge>
          )}
        </div>
      ))}
    </div>
  );
};

export default GridBadges;
