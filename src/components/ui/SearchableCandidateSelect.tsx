import { Candidate } from "@/components/nannies/models/Candidate";
import { cn } from "@/lib/utils";
import { fetchNannies, fetchTutors } from "@/services/candidateService";
import { useQuery } from "@tanstack/react-query";
import {
  Check,
  ChevronDown,
  ChevronUp,
  Search,
  User,
  UserCircle,
  UserRound,
  X,
} from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button } from "./button";
import { Card } from "./card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "./command";

interface SearchableCandidateSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowReset?: boolean;
  candidateType?: "nanny" | "tutor" | "both";
  required?: boolean;
}

// Helper function to normalize text for better searching
const normalizeText = (text: string): string => {
  if (!text) return "";
  return text
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "");
};

export function SearchableCandidateSelect({
  value,
  onValueChange,
  label,
  placeholder = "Επιλέξτε υποψήφιο",
  className,
  disabled = false,
  allowReset = true,
  candidateType = "both",
}: SearchableCandidateSelectProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Fetch nannies
  const { data: nannies = [], isLoading: isLoadingNannies } = useQuery<
    Candidate[]
  >({
    queryKey: ["nannies", "select"],
    queryFn: async () => {
      const response = await fetchNannies();
      return Array.isArray(response) ? response : response.data;
    },
    staleTime: 60 * 1000, // 1 minute
    enabled: candidateType === "nanny" || candidateType === "both",
  });

  // Fetch tutors
  const { data: tutors = [], isLoading: isLoadingTutors } = useQuery<
    Candidate[]
  >({
    queryKey: ["tutors", "select"],
    queryFn: async () => {
      const response = await fetchTutors();
      return Array.isArray(response) ? response : response.data;
    },
    staleTime: 60 * 1000, // 1 minute
    enabled: candidateType === "tutor",
  });

  // Combine candidates based on the selected type and deduplicate
  const candidates = useMemo(() => {
    if (candidateType === "nanny") return nannies;
    if (candidateType === "tutor") return tutors;

    // Create a map to deduplicate candidates by ID
    const candidateMap = new Map();

    // Add nannies to the map
    nannies.forEach((nanny) => {
      candidateMap.set(String(nanny.id), {
        ...nanny,
        // Add a unique key for each candidate that combines ID and type
        uniqueKey: `nanny-${nanny.id}`,
      });
    });

    // Add tutors to the map, potentially overwriting duplicates
    tutors.forEach((tutor) => {
      // If this ID already exists as a nanny, create a unique key
      if (candidateMap.has(String(tutor.id))) {
        candidateMap.set(`tutor-${tutor.id}`, {
          ...tutor,
          uniqueKey: `tutor-${tutor.id}`,
        });
      } else {
        candidateMap.set(String(tutor.id), {
          ...tutor,
          uniqueKey: `tutor-${tutor.id}`,
        });
      }
    });

    // Convert the map back to an array
    return Array.from(candidateMap.values());
  }, [nannies, tutors, candidateType]);

  // Filter candidates based on search input
  const filteredCandidates = useMemo(() => {
    if (!searchValue) {
      return candidates;
    }

    // Normalize the search value
    const normalizedSearch = normalizeText(searchValue);

    return candidates.filter((candidate) => {
      // Get candidate details for searching
      const id = String(candidate.id);
      const name = normalizeText(candidate.form_data?.name || "");
      const surname = normalizeText(candidate.form_data?.surname || "");
      const fullName = `${name} ${surname}`;

      // Search in ID, name, and surname
      return (
        id.includes(normalizedSearch) ||
        name.includes(normalizedSearch) ||
        surname.includes(normalizedSearch) ||
        fullName.includes(normalizedSearch)
      );
    });
  }, [candidates, searchValue]);

  // Get the selected candidate
  const selectedCandidate = useMemo(() => {
    if (!value) return null;
    return candidates.find((candidate) => String(candidate.id) === value);
  }, [candidates, value]);

  // Handle reset
  const handleReset = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onValueChange("");
      setSearchValue("");
    },
    [onValueChange]
  );

  // Get candidate display name
  const getCandidateDisplayName = useCallback((candidate: Candidate | null) => {
    if (!candidate) return "";
    const name = candidate.form_data?.name || "";
    const surname = candidate.form_data?.surname || "";
    const id = candidate.id;
    const type = candidate.is_nanny_approved ? "NANNY" : "TUTOR";

    return `${type}-${id}: ${name} ${surname}`;
  }, []);

  // Get candidate gender icon
  const getCandidateGenderIcon = useCallback((candidate: Candidate | null) => {
    if (!candidate) return <User className="h-4 w-4 text-gray-500" />;

    const gender = candidate.form_data?.gender?.toLowerCase();

    if (gender === "male") {
      return <UserCircle className="h-4 w-4 text-blue-500" />;
    } else if (gender === "female") {
      return <UserRound className="h-4 w-4 text-pink-500" />;
    } else {
      return <User className="h-4 w-4 text-gray-500" />;
    }
  }, []);

  const isLoading = isLoadingNannies || isLoadingTutors;

  // Focus the search input when expanded
  useEffect(() => {
    if (isExpanded) {
      const timer = setTimeout(() => {
        if (searchInputRef.current) {
          try {
            searchInputRef.current.focus();
          } catch (e) {
            console.error("Failed to focus search input:", e);
          }
        }
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [isExpanded]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isExpanded]);

  // Handle keyboard events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setIsExpanded(false);
    }
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    if (!disabled) {
      setIsExpanded(!isExpanded);
      if (!isExpanded) {
        setSearchValue("");
      }
    }
  };

  return (
    <div
      className={cn("flex flex-col space-y-1.5 relative", className)}
      ref={containerRef}
    >
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
        </label>
      )}

      {/* Trigger Button */}
      <Button
        type="button"
        variant="outline"
        onClick={toggleDropdown}
        disabled={disabled}
        className={cn(
          "w-full justify-between rounded-lg focus:border-primary focus:ring-primary hover-border-grow",
          !value && "text-muted-foreground"
        )}
      >
        <div className="flex items-center gap-2 truncate">
          {selectedCandidate ? (
            <>
              {getCandidateGenderIcon(selectedCandidate)}
              <span className="truncate">
                {getCandidateDisplayName(selectedCandidate)}
              </span>
            </>
          ) : (
            <span>{placeholder}</span>
          )}
        </div>
        <div className="flex items-center">
          {allowReset && value && (
            <div
              className="h-6 w-6 p-0 mr-1 rounded-full hover:bg-muted flex items-center justify-center cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleReset(e);
              }}
              aria-label="Clear selection"
            >
              <X className="h-3 w-3" />
            </div>
          )}
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 shrink-0 opacity-50" />
          ) : (
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          )}
        </div>
      </Button>

      {/* Dropdown Content */}
      {isExpanded && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 p-0 shadow-md border rounded-md overflow-hidden">
          <Command onKeyDown={handleKeyDown} className="w-full">
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Αναζήτηση με ID, όνομα ή επώνυμο"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                autoComplete="off"
                autoCorrect="off"
                spellCheck="false"
                onClick={(e) => {
                  e.stopPropagation();
                  e.currentTarget.select();
                }}
              />
              {searchValue && (
                <div
                  className="h-6 w-6 p-0 rounded-full hover:bg-muted flex items-center justify-center cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSearchValue("");
                    searchInputRef.current?.focus();
                  }}
                  aria-label="Clear search"
                >
                  <X className="h-3 w-3" />
                </div>
              )}
            </div>
            <CommandList className="max-h-[200px] overflow-auto">
              {isLoading ? (
                <CommandEmpty>Φόρτωση υποψηφίων...</CommandEmpty>
              ) : filteredCandidates.length === 0 ? (
                <CommandEmpty>Δεν βρέθηκαν υποψήφιοι</CommandEmpty>
              ) : (
                <CommandGroup>
                  {filteredCandidates.map((candidate) => (
                    <CommandItem
                      key={candidate.uniqueKey || `candidate-${candidate.id}`}
                      value={String(candidate.id)}
                      onSelect={() => {
                        onValueChange(String(candidate.id));
                        setIsExpanded(false);
                      }}
                    >
                      <div className="flex items-center gap-2 w-full">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value === String(candidate.id)
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                        {getCandidateGenderIcon(candidate)}
                        <span className="truncate">
                          {getCandidateDisplayName(candidate)}
                        </span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </Card>
      )}
    </div>
  );
}
