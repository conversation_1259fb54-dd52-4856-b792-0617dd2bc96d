import { getLocalized<PERSON><PERSON><PERSON> } from "@/lib/staticData";
import { cn } from "@/lib/utils";
import { City, fetchCities } from "@/services/cityService";
import { useQuery } from "@tanstack/react-query";
import { Check, ChevronDown, ChevronUp, MapPin, Search, X } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button } from "./button";
import { Card } from "./card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "./command";

interface SearchableCitySelectInlineProps {
  value: string;
  onValueChange: (value: string) => void;
  label?: string;
  language?: "el" | "en";
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowReset?: boolean;
  dropdownHeight?: string; // Custom height for the dropdown
}

// Helper function to normalize text for Greek character search
const normalizeText = (text: string): string => {
  if (!text) return "";

  // Convert to lowercase first
  const lowerText = text.toLowerCase();

  // Map of Greek characters with accents to their non-accented versions
  const greekAccentMap: Record<string, string> = {
    ά: "α",
    έ: "ε",
    ή: "η",
    ί: "ι",
    ό: "ο",
    ύ: "υ",
    ώ: "ω",
    ϊ: "ι",
    ϋ: "υ",
    ΐ: "ι",
    ΰ: "υ",
    Ά: "α",
    Έ: "ε",
    Ή: "η",
    Ί: "ι",
    Ό: "ο",
    Ύ: "υ",
    Ώ: "ω",
    Ϊ: "ι",
    Ϋ: "υ",
  };

  // Replace each accented character with its non-accented version
  return Array.from(lowerText)
    .map((char) => greekAccentMap[char] || char)
    .join("");
};

export function SearchableCitySelectInline({
  value,
  onValueChange,
  label,
  language = "el",
  placeholder = "Επιλέξτε πόλη / Select city",
  className,
  disabled = false,
  allowReset = true,
  dropdownHeight = "200px", // Default height
}: SearchableCitySelectInlineProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Use React Query to fetch cities with a long stale time (1 hour)
  const { data: cities = [], isLoading } = useQuery<City[]>({
    queryKey: ["cities"],
    queryFn: async () => {
      return await fetchCities();
    },
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
  });

  // Memoize filtered cities based on search input
  const filteredCities = useMemo(() => {
    if (!searchValue) {
      return cities;
    }

    // Normalize the search value to handle Greek characters properly
    const normalizedSearch = normalizeText(searchValue);

    return cities.filter((city: City) => {
      // Normalize city names for better matching with Greek characters
      const cityNameEl = normalizeText(city.labelEl);
      const cityNameEn = normalizeText(city.labelEn);
      const countryName = city.country ? normalizeText(city.country) : "";

      // Direct match with the full search term
      if (
        cityNameEl.includes(normalizedSearch) ||
        cityNameEn.includes(normalizedSearch) ||
        countryName.includes(normalizedSearch)
      ) {
        return true;
      }

      // Also check for partial word matches
      const searchWords = normalizedSearch
        .split(/\s+/)
        .filter((w) => w.length > 0);

      // If no valid search words, return false
      if (searchWords.length === 0) return false;

      // Check if all search words are found in either Greek or English city name or country
      return searchWords.every(
        (word) =>
          cityNameEl.includes(word) ||
          cityNameEn.includes(word) ||
          countryName.includes(word)
      );
    });
  }, [searchValue, cities]);

  // Memoize grouped cities
  const groupedCitiesData = useMemo(() => {
    // Group cities by country
    const grouped = filteredCities.reduce<Record<string, City[]>>(
      (acc: Record<string, City[]>, city: City) => {
        const country = city.country || "Other";
        if (!acc[country]) {
          acc[country] = [];
        }
        acc[country].push(city);
        return acc;
      },
      {}
    );

    // Sort countries alphabetically
    const sortedCountries = Object.keys(grouped).sort();

    return { groupedCities: grouped, sortedCountries };
  }, [filteredCities]);

  const { groupedCities, sortedCountries } = groupedCitiesData;

  // Memoize selected city label
  const selectedCityData = useMemo(() => {
    const selectedCity = cities.find((city: City) => city.id === value);
    return selectedCity
      ? getLocalizedLabel(selectedCity, language)
      : value && value !== ""
      ? value
      : "";
  }, [cities, value, language]);

  // Handle reset with useCallback
  const handleReset = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent the dropdown from opening
      onValueChange(""); // Clear the value
      setSearchValue(""); // Clear the search input
    },
    [onValueChange]
  );

  // Effect to handle external reset (when value changes to empty string)
  useEffect(() => {
    if (value === "") {
      setSearchValue(""); // Also clear the search input when value is reset externally
    }
  }, [value]);

  // Add a special "All" option at the top of the list
  const handleSelectAll = useCallback(() => {
    onValueChange(""); // Clear the value to represent "All"
    setIsExpanded(false);
  }, [onValueChange]);

  // Focus the search input when expanded
  useEffect(() => {
    if (isExpanded) {
      const timer = setTimeout(() => {
        if (searchInputRef.current) {
          try {
            searchInputRef.current.focus();
          } catch (e) {
            console.error("Failed to focus search input:", e);
          }
        }
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [isExpanded]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isExpanded]);

  // Handle keyboard events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setIsExpanded(false);
    }
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    if (!disabled) {
      setIsExpanded(!isExpanded);
      if (!isExpanded) {
        setSearchValue("");
      }
    }
  };

  return (
    <div
      className={cn("flex flex-col space-y-1.5 relative", className)}
      ref={containerRef}
    >
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
        </label>
      )}

      {/* Trigger Button */}
      <Button
        type="button"
        variant="outline"
        onClick={toggleDropdown}
        disabled={disabled}
        className={cn(
          "w-full justify-between rounded-lg focus:border-primary focus:ring-primary hover-border-grow",
          !value && "text-muted-foreground"
        )}
      >
        <div className="flex items-center">
          {value ? (
            <>
              <MapPin className="mr-2 h-4 w-4 shrink-0 text-primary" />
              {isLoading ? (
                <span className="flex items-center">
                  <div className="animate-spin mr-2">
                    <svg
                      className="h-4 w-4 text-primary"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </div>
                  {value}
                </span>
              ) : (
                selectedCityData
              )}
            </>
          ) : (
            <span>{placeholder}</span>
          )}
        </div>
        <div className="flex items-center">
          {allowReset && value && (
            <div
              className="h-6 w-6 p-0 mr-1 rounded-full hover:bg-muted flex items-center justify-center cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleReset(e);
              }}
              aria-label="Clear selection"
            >
              <X className="h-3 w-3" />
            </div>
          )}
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 shrink-0 opacity-50" />
          ) : (
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          )}
        </div>
      </Button>

      {/* Dropdown Content */}
      {isExpanded && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 p-0 shadow-md border rounded-md overflow-hidden max-h-[80vh] w-full">
          <Command onKeyDown={handleKeyDown} className="w-full">
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder={
                  language === "el" ? "Αναζήτηση πόλης" : "Search city"
                }
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                lang={language === "el" ? "el" : "en"}
                autoComplete="off"
                autoCorrect="off"
                spellCheck="false"
                onClick={(e) => {
                  e.stopPropagation();
                  e.currentTarget.select();
                }}
              />
              {searchValue && (
                <div
                  className="h-6 w-6 p-0 rounded-full hover:bg-muted flex items-center justify-center cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSearchValue("");
                    searchInputRef.current?.focus();
                  }}
                  aria-label="Clear search"
                >
                  <X className="h-3 w-3" />
                </div>
              )}
            </div>
            <CommandList className={`max-h-[${dropdownHeight}] overflow-auto`}>
              {isLoading ? (
                <div className="flex items-center justify-center py-6">
                  <div className="flex flex-col items-center gap-2">
                    <div className="animate-spin">
                      <svg
                        className="h-6 w-6 text-primary"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {language === "el"
                        ? "Φόρτωση πόλεων..."
                        : "Loading cities..."}
                    </span>
                  </div>
                </div>
              ) : (
                <>
                  <CommandEmpty>
                    {language === "el"
                      ? "Δεν βρέθηκαν πόλεις"
                      : "No cities found"}
                  </CommandEmpty>

                  {/* Add "All" option at the top */}
                  <CommandItem
                    value="all"
                    onSelect={handleSelectAll}
                    className="font-medium text-primary"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        !value ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {language === "el" ? "Όλες" : "All"}
                  </CommandItem>

                  {sortedCountries.map((country) => (
                    <CommandGroup key={country} heading={country}>
                      {groupedCities[country].map((city: City) => (
                        <CommandItem
                          key={city.id}
                          value={city.id}
                          onSelect={() => {
                            onValueChange(city.id);
                            setIsExpanded(false);
                          }}
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              value === city.id ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {getLocalizedLabel(city, language)}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  ))}
                </>
              )}
            </CommandList>
          </Command>
        </Card>
      )}
    </div>
  );
}
