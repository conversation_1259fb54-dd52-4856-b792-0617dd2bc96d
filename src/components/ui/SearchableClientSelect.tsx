import { Client } from "@/components/crm/clients/ClientsTable";
import { CLIENT_STATUS } from "@/lib/clientUtils";
import { cn } from "@/lib/utils";
import { fetchLeadsAndClients } from "@/services/clientService";
import { useQuery } from "@tanstack/react-query";
import { Check, ChevronDown, ChevronUp, Search, Users, X } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button } from "./button";
import { Card } from "./card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "./command";

interface SearchableClientSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  allowReset?: boolean;
  required?: boolean;
}

// Helper function to normalize text for better searching
const normalizeText = (text: string): string => {
  if (!text) return "";
  return text
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "");
};

export function SearchableClientSelect({
  value,
  onValueChange,
  label,
  placeholder = "Επιλέξτε πελάτη",
  className,
  disabled = false,
  allowReset = true,
}: SearchableClientSelectProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Fetch clients with status active client, past client, or lead
  const { data: clients = [], isLoading } = useQuery<Client[]>({
    queryKey: ["clients", "select"],
    queryFn: async () => {
      const response = await fetchLeadsAndClients();
      const clientsData = Array.isArray(response) ? response : response.data;

      // Filter clients by status
      return clientsData.filter(
        (client) =>
          client.status === CLIENT_STATUS.LEAD ||
          client.status === CLIENT_STATUS.PAST_CLIENT ||
          client.status === CLIENT_STATUS.ACTIVE_CLIENT
      );
    },
    staleTime: 60 * 1000, // 1 minute
  });

  // Filter clients based on search input
  const filteredClients = useMemo(() => {
    if (!clients || clients.length === 0) return [];

    if (!searchValue.trim()) {
      return clients;
    }

    const normalizedSearch = normalizeText(searchValue);

    return clients.filter((client) => {
      const fatherName = normalizeText(client.form_data?.father_name || "");
      const motherName = normalizeText(client.form_data?.mother_name || "");
      const clientId = `CL-${client.id}`;

      return (
        fatherName.includes(normalizedSearch) ||
        motherName.includes(normalizedSearch) ||
        clientId.includes(normalizedSearch)
      );
    });
  }, [clients, searchValue]);

  // Get client display name
  const getClientDisplayName = (client: Client): string => {
    const fatherName = client.form_data?.father_name || "";
    const motherName = client.form_data?.mother_name || "";

    if (fatherName && motherName) {
      return `${fatherName} & ${motherName}`;
    } else if (fatherName) {
      return fatherName;
    } else if (motherName) {
      return motherName;
    } else {
      return `CL-${client.id}`;
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsExpanded(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown is expanded
  useEffect(() => {
    if (isExpanded && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isExpanded]);

  // Reset search value when dropdown is closed
  useEffect(() => {
    if (!isExpanded) {
      setSearchValue("");
    }
  }, [isExpanded]);

  // Handle reset button click
  const handleReset = useCallback(() => {
    onValueChange("");
    setSearchValue("");
    setIsExpanded(false);
  }, [onValueChange]);

  return (
    <div className={cn("relative w-full", className)} ref={containerRef}>
      {label && (
        <div className="text-sm font-medium mb-2 text-foreground">{label}</div>
      )}
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={isExpanded}
        className={cn(
          "w-full justify-between",
          !value && "text-muted-foreground",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onClick={() => !disabled && setIsExpanded(!isExpanded)}
        disabled={disabled}
      >
        {value ? (
          <span className="truncate">
            {clients.find((client) => client.id.toString() === value)
              ? getClientDisplayName(
                  clients.find((client) => client.id.toString() === value)!
                )
              : value}
          </span>
        ) : (
          <span>{placeholder}</span>
        )}
        <div className="flex items-center">
          {value && allowReset && (
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 mr-1 hover:bg-accent hover:text-accent-foreground rounded-full"
              onClick={(e) => {
                e.stopPropagation();
                handleReset();
              }}
              disabled={disabled}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Clear</span>
            </Button>
          )}
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 shrink-0 opacity-50" />
          ) : (
            <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
          )}
        </div>
      </Button>

      {isExpanded && (
        <Card className="absolute z-50 mt-1 w-full overflow-hidden">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              ref={searchInputRef}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              placeholder="Αναζήτηση πελάτη..."
              className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
          <Command>
            <CommandList className="max-h-[200px] overflow-auto">
              {isLoading ? (
                <CommandEmpty>Φόρτωση πελατών...</CommandEmpty>
              ) : filteredClients.length === 0 ? (
                <CommandEmpty>Δεν βρέθηκαν πελάτες</CommandEmpty>
              ) : (
                <CommandGroup>
                  {filteredClients.map((client) => (
                    <CommandItem
                      key={`client-${client.id}`}
                      value={String(client.id)}
                      onSelect={() => {
                        onValueChange(String(client.id));
                        setIsExpanded(false);
                      }}
                    >
                      <div className="flex items-center gap-2 w-full">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            value === String(client.id)
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="truncate">
                          {getClientDisplayName(client)}
                        </span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </Card>
      )}
    </div>
  );
}
