import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TableSkeletonProps {
  columns: number;
  rows: number;
  showHeader?: boolean;
  showFilters?: boolean;
  showPagination?: boolean;
}

export function TableSkeleton({
  columns = 5,
  rows = 10,
  showHeader = true,
  showFilters = true,
  showPagination = true,
}: TableSkeletonProps) {
  return (
    <div className="space-y-4 animate-pulse">
      {/* Filters */}
      {showFilters && (
        <div className="flex flex-wrap gap-2 mb-4">
          {Array(Math.min(columns, 3))
            .fill(0)
            .map((_, i) => (
              <Skeleton key={`filter-${i}`} className="h-9 w-40" />
            ))}
          <div className="flex-1"></div>
          <Skeleton className="h-9 w-32" />
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          {showHeader && (
            <TableHeader>
              <TableRow>
                {Array(columns)
                  .fill(0)
                  .map((_, i) => (
                    <TableHead key={`header-${i}`}>
                      <Skeleton className="h-5 w-24" />
                    </TableHead>
                  ))}
              </TableRow>
            </TableHeader>
          )}
          <TableBody>
            {Array(rows)
              .fill(0)
              .map((_, rowIndex) => (
                <TableRow key={`row-${rowIndex}`}>
                  {Array(columns)
                    .fill(0)
                    .map((_, colIndex) => (
                      <TableCell key={`cell-${rowIndex}-${colIndex}`}>
                        <Skeleton
                          className={`h-5 ${
                            colIndex === 0
                              ? "w-16"
                              : colIndex === 1 || colIndex === 2
                              ? "w-24"
                              : "w-full max-w-[120px]"
                          }`}
                        />
                        {/* Add badges for some columns */}
                        {(colIndex === 4 || colIndex === 5 || colIndex === 6) && (
                          <div className="flex gap-1 mt-1">
                            <Skeleton className="h-5 w-16 rounded-full" />
                            {Math.random() > 0.5 && (
                              <Skeleton className="h-5 w-16 rounded-full" />
                            )}
                          </div>
                        )}
                      </TableCell>
                    ))}
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {showPagination && (
        <div className="flex items-center justify-between mt-4">
          <Skeleton className="h-9 w-32" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-8 rounded-md" />
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>
        </div>
      )}
    </div>
  );
}
