import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2 py-1 text-sm font-bold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-slate-200 bg-slate-100 text-slate-700 hover:bg-slate-200",
        primary:
          "border-primary/20 bg-primary/10 text-primary hover:bg-primary/20",
        secondary:
          "border-secondary/20 bg-secondary/10 text-secondary hover:bg-secondary/20",
        tertiary:
          "border-tertiary/20 bg-tertiary/10 text-tertiary hover:bg-tertiary/20",
        accent: "border-accent/20 bg-accent/10 text-accent hover:bg-accent/20",
        light:
          "border-light/20 bg-light/10 text-light-foreground hover:bg-light/20",
        destructive:
          "border-destructive/20 bg-destructive/10 text-destructive hover:bg-destructive/20",
        outline: "border-slate-200 bg-white text-slate-700 hover:bg-slate-100",
        success:
          "border-green-100 bg-green-100 text-green-700 hover:bg-green-200",
        warning: "border-light/30 bg-light/20 text-primary hover:bg-light/30",
        info: "border-sky-100 bg-sky-100 text-sky-700 hover:bg-sky-200",
        outlinePrimary:
          "border-primary/30 bg-white text-primary hover:bg-primary/5",
        outlineSecondary:
          "border-secondary/30 bg-white text-secondary hover:bg-secondary/5",
        outlineTertiary:
          "border-tertiary/30 bg-white text-tertiary hover:bg-tertiary/5",
        outlineAccent:
          "border-accent/30 bg-white text-accent hover:bg-accent/5",
        outlineLight:
          "border-light/30 bg-white text-light-foreground hover:bg-light/5",
        navy: "border-primary/20 bg-primary/10 text-primary hover:bg-primary/20",
        taupe:
          "border-secondary/20 bg-secondary/10 text-secondary hover:bg-secondary/20",
        beige:
          "border-tertiary/20 bg-tertiary/10 text-tertiary hover:bg-tertiary/20",
        lightBlue:
          "border-accent/20 bg-accent/10 text-accent hover:bg-accent/20",
        cream:
          "border-light/20 bg-light/10 text-light-foreground hover:bg-light/20",
        // Solid variants with stronger colors
        solidPrimary:
          "border-primary bg-primary text-white hover:bg-primary/90",
        solidSecondary:
          "border-secondary bg-secondary text-white hover:bg-secondary/90",
        solidTertiary:
          "border-tertiary bg-tertiary text-tertiary-foreground hover:bg-tertiary/90",
        solidAccent:
          "border-accent bg-accent text-accent-foreground hover:bg-accent/90",
        solidLight:
          "border-light bg-light text-light-foreground hover:bg-light/90",
        // Pastel variants
        pastelBlue:
          "border-blue-200 bg-blue-100 text-blue-800 hover:bg-blue-200",
        pastelGreen:
          "border-green-200 bg-green-100 text-green-800 hover:bg-green-200",
        pastelYellow:
          "border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
        pastelPink:
          "border-pink-200 bg-pink-100 text-pink-800 hover:bg-pink-200",
        pastelPurple:
          "border-purple-200 bg-purple-100 text-purple-800 hover:bg-purple-200",
        // Gradient variants
        gradientBlue:
          "border-blue-400 bg-gradient-to-r from-blue-400 to-indigo-500 text-white",
        gradientGreen:
          "border-green-400 bg-gradient-to-r from-green-400 to-emerald-500 text-white",
        gradientPurple:
          "border-purple-400 bg-gradient-to-r from-purple-400 to-pink-500 text-white",
        gradientOrange:
          "border-orange-400 bg-gradient-to-r from-orange-400 to-red-500 text-white",
        gradientTeal:
          "border-teal-400 bg-gradient-to-r from-teal-400 to-cyan-500 text-white",
        // Neutral variants
        neutral: "border-gray-200 bg-gray-100 text-gray-800 hover:bg-gray-200",
        neutralDark:
          "border-gray-700 bg-gray-800 text-gray-100 hover:bg-gray-700",
      },
    },
    defaultVariants: {
      variant: "primary",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div
      className={cn(
        badgeVariants({ variant }),
        "whitespace-nowrap overflow-hidden text-ellipsis",
        className
      )}
      style={{
        display: "inline-block",
        maxWidth: "100%",
      }}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
