import * as React from "react";

import { cn } from "@/lib/utils";

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  gradient?: boolean;
  hoverEffect?: boolean;
  variant?:
    | "default"
    | "primary"
    | "secondary"
    | "tertiary"
    | "accent"
    | "light";
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      gradient = false,
      hoverEffect = false,
      variant = "default",
      ...props
    },
    ref
  ) => (
    <div
      ref={ref}
      className={cn(
        "rounded-xl border shadow transition-all duration-300",
        variant === "default" && "bg-card text-card-foreground",
        variant === "primary" &&
          "bg-primary/5 border-primary/20 text-primary-foreground",
        variant === "secondary" &&
          "bg-secondary/5 border-secondary/20 text-secondary-foreground",
        variant === "tertiary" &&
          "bg-tertiary/5 border-tertiary/20 text-tertiary-foreground",
        variant === "accent" &&
          "bg-accent/5 border-accent/20 text-accent-foreground",
        variant === "light" &&
          "bg-light/5 border-light/20 text-light-foreground",
        gradient &&
          variant === "default" &&
          "bg-gradient-to-br from-white to-light border-light/20",
        gradient &&
          variant === "primary" &&
          "bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20",
        gradient &&
          variant === "secondary" &&
          "bg-gradient-to-br from-secondary/5 to-secondary/10 border-secondary/20",
        gradient &&
          variant === "tertiary" &&
          "bg-gradient-to-br from-tertiary/5 to-tertiary/10 border-tertiary/20",
        gradient &&
          variant === "accent" &&
          "bg-gradient-to-br from-accent/5 to-accent/10 border-accent/20",
        gradient &&
          variant === "light" &&
          "bg-gradient-to-br from-light/5 to-light/10 border-light/20",
        hoverEffect && "hover:shadow-lg hover:scale-[1.01]",
        className
      )}
      {...props}
    />
  )
);
Card.displayName = "Card";

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("font-semibold leading-none tracking-tight", className)}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
));
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

export {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
};
