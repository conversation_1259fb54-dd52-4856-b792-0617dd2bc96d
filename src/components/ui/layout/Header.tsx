import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { usePermissions } from "@/contexts/PermissionsContext";
import { getNavigationItems } from "@/lib/routePermissions";
import { supabase } from "@/lib/supabase";
import { Bell, Menu, Settings, X } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "../button";

interface UserProfile {
  name: string;
  email: string;
  avatar_url?: string;
  id: string;
}

const Header = () => {
  const location = useLocation();
  const { hasPagePermission } = usePermissions();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Get navigation items based on user permissions
  const navigationPaths = useMemo(() => {
    // Use the centralized function to get navigation items
    const navItems = getNavigationItems(hasPagePermission);
    console.log("Navigation items based on permissions:", navItems);
    return navItems.filter((item) => item.path !== "/settings");
  }, [hasPagePermission]);

  useEffect(() => {
    getProfile();
  }, []);

  const getProfile = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        setProfile({
          id: user.id,
          email: user.email!,
          name: user.user_metadata.name || "User",
          avatar_url: user.user_metadata.avatar_url,
        });
      }
    } catch (error) {
      console.error("Error loading user data:", error);
    }
  };
  function capitalize(s: string): string {
    return s && String(s[0]).toUpperCase() + String(s).slice(1);
  }
  return (
    <header className="w-full border-b border-light/20 bg-background shadow-sm">
      <div className="flex items-center justify-between px-4 h-16">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          <Link to="/dashboard" className="flex items-center gap-2">
            <div className=" p-2 rounded-md flex items-center justify-center">
              <div className=" bg-transparent p-1 rounded-sm flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="Nanny Blue Logo"
                  className="h-7 w-auto"
                />
              </div>
            </div>
          </Link>
          <div className="h-6 w-px bg-light mx-2 hidden md:block"></div>
          <Avatar className="border-2 border-primary/20">
            <AvatarImage src={profile?.avatar_url} />
            <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white">
              {profile?.name?.[0]}
            </AvatarFallback>
          </Avatar>
          <h1 className="text-lg font-semibold hidden sm:block text-primary">
            {profile?.name || "User"}
          </h1>
        </div>

        {/* Mobile Menu Toggle */}
        <Button
          variant="ghost"
          size="icon"
          className="sm:hidden text-primary hover:bg-primary/10"
          onClick={() => setMobileMenuOpen((prev) => !prev)}
        >
          {mobileMenuOpen ? (
            <X className="h-5 w-5" />
          ) : (
            <Menu className="h-5 w-5" />
          )}
        </Button>

        {/* Desktop Navigation */}
        <nav className="hidden sm:flex items-center gap-6 ml-auto">
          {navigationPaths.map((item: { path: string; label: string }) => (
            <Link
              key={item.path}
              to={item.path}
              className={`text-sm ${
                location.pathname === item.path
                  ? "text-primary font-semibold"
                  : "text-secondary hover:text-primary transition-colors"
              }`}
            >
              {item.label}
            </Link>
          ))}
          <Button
            variant="ghost"
            size="icon"
            className="text-primary hover:bg-primary/10"
          >
            <Bell className="h-5 w-5" />
          </Button>
          <Link to="/settings">
            <Button
              variant="ghost"
              size="icon"
              className="text-primary hover:bg-primary/10"
            >
              <Settings className="h-5 w-5" />
            </Button>
          </Link>
        </nav>
      </div>

      {/* Mobile Navigation */}
      {mobileMenuOpen ? (
        <div className="sm:hidden bg-background border-t border-light/20 px-4 py-2">
          <nav className="flex flex-col gap-2">
            {navigationPaths.map((item: { path: string; label: string }) => (
              <Link
                key={item.path}
                to={item.path}
                className={`text-sm py-2 ${
                  location.pathname === item.path
                    ? "text-primary font-semibold"
                    : "text-secondary hover:text-primary transition-colors"
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                {item.label}
              </Link>
            ))}
          </nav>
          <div className="flex justify-between mt-4">
            <Button
              variant="ghost"
              size="icon"
              className="text-primary hover:bg-primary/10"
            >
              <Bell className="h-5 w-5" />
            </Button>
            <Link to="/settings">
              <Button
                variant="ghost"
                size="icon"
                className="text-primary hover:bg-primary/10"
              >
                <Settings className="h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      ) : (
        <div className="sm:hidden px-4 py-2 text-center text-sm text-primary bg-background">
          {location.pathname === "/crm"
            ? "CRM"
            : location.pathname === "/tutors"
            ? "Tutors/Activities"
            : capitalize(location.pathname.replace("/", ""))}
        </div>
      )}
    </header>
  );
};

export default Header;
