import { useAuth } from "@/contexts/AuthContext";
import { LogOut } from "lucide-react";
import { ReactNode } from "react";
import { Button } from "../button";
import { Toaster } from "../toaster";

interface NannyLayoutProps {
  children: ReactNode;
}

const NannyLayout = ({ children }: NannyLayoutProps) => {
  const { signOut } = useAuth();

  return (
    <div className="min-h-screen bg-background">
      {/* Simple header with just the logo and logout button */}
      <header className="w-full border-b border-light/20 bg-background shadow-sm">
        <div className="flex items-center justify-between px-4 h-16">
          {/* Left Section - Logo */}
          <div className="flex items-center gap-4">
            <div className="p-2 rounded-md flex items-center justify-center">
              <div className="bg-transparent p-1 rounded-sm flex items-center justify-center">
                <img
                  src="/logo.png"
                  alt="Nanny Blue Logo"
                  className="h-7 w-auto"
                />
              </div>
            </div>
          </div>

          {/* Right Section - Logout Button */}
          <Button
            variant="ghost"
            className="flex items-center gap-2 text-primary hover:bg-primary/10"
            onClick={signOut}
          >
            <LogOut size={16} />
            <span>Αποσύνδεση</span>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main>{children}</main>
      <Toaster />
    </div>
  );
};

export default NannyLayout;
