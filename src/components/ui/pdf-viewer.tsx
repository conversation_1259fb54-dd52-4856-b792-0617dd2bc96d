import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Download, ExternalLink, FileText } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface PDFViewerProps {
  url: string;
  fileName?: string;
  height?: string;
  className?: string;
}

/**
 * A component for viewing PDF files with fallback options
 * Uses multiple approaches to maximize browser compatibility
 */
export function PDFViewer({
  url,
  fileName = "document.pdf",
  height = "500px",
  className = "",
}: PDFViewerProps) {
  const [viewerType, setViewerType] = useState<"iframe" | "object" | "fallback">("iframe");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const objectRef = useRef<HTMLObjectElement>(null);

  useEffect(() => {
    // Try to detect if iframe will work
    const checkIframe = () => {
      setLoading(false);
      
      // If we're in Chrome, prefer object tag as it's more compatible
      const isChrome = navigator.userAgent.indexOf("Chrome") > -1;
      if (isChrome) {
        setViewerType("object");
        return;
      }
      
      // Otherwise try iframe first
      setViewerType("iframe");
    };

    // Check after a short delay to allow component to mount
    const timer = setTimeout(checkIframe, 500);
    return () => clearTimeout(timer);
  }, [url]);

  // Handle errors in iframe or object
  const handleError = () => {
    console.error("Error loading PDF in primary viewer, falling back to alternative");
    setViewerType("fallback");
    setError("Δεν ήταν δυνατή η προβολή του PDF απευθείας.");
  };

  // Handle download
  const handleDownload = () => {
    if (url) {
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Open in new tab
  const handleOpenInNewTab = () => {
    if (url) {
      window.open(url, "_blank");
    }
  };

  if (loading) {
    return (
      <div className={`w-full flex items-center justify-center ${className}`} style={{ height }}>
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Card className={`p-0 overflow-hidden border border-light/30 rounded-lg ${className}`}>
      {/* PDF Viewer Controls */}
      <div className="flex items-center justify-between bg-muted/20 p-2 border-b border-light/20">
        <div className="flex items-center space-x-2">
          <FileText className="h-5 w-5 text-primary" />
          <span className="text-sm font-medium">
            {fileName}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={handleDownload}
            title="Download"
          >
            <Download size={16} />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={handleOpenInNewTab}
            title="Open in New Tab"
          >
            <ExternalLink size={16} />
          </Button>
        </div>
      </div>

      {/* PDF Viewer Content */}
      <div className="w-full bg-muted/10" style={{ height: `calc(${height} - 80px)` }}>
        {viewerType === "iframe" && (
          <iframe
            ref={iframeRef}
            src={url}
            className="w-full h-full border-0"
            title="PDF Preview"
            onError={handleError}
          />
        )}

        {viewerType === "object" && (
          <object
            ref={objectRef}
            data={url}
            type="application/pdf"
            className="w-full h-full"
            aria-label="PDF Preview"
            onError={handleError}
          >
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-center p-4">
                <p className="text-muted-foreground mb-4">
                  Ο browser σας δεν μπορεί να εμφανίσει το PDF απευθείας.
                </p>
                <Button 
                  variant="default" 
                  onClick={handleOpenInNewTab}
                  className="mb-2 w-full"
                >
                  <ExternalLink size={16} className="mr-2" />
                  Άνοιγμα σε νέα καρτέλα
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleDownload}
                  className="w-full"
                >
                  <Download size={16} className="mr-2" />
                  Κατέβασμα PDF
                </Button>
              </div>
            </div>
          </object>
        )}

        {viewerType === "fallback" && (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center p-4">
              <p className="text-muted-foreground mb-4">
                {error || "Ο browser σας δεν μπορεί να εμφανίσει το PDF απευθείας."}
              </p>
              <Button 
                variant="default" 
                onClick={handleOpenInNewTab}
                className="mb-2 w-full"
              >
                <ExternalLink size={16} className="mr-2" />
                Άνοιγμα σε νέα καρτέλα
              </Button>
              <Button 
                variant="outline" 
                onClick={handleDownload}
                className="w-full"
              >
                <Download size={16} className="mr-2" />
                Κατέβασμα PDF
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="p-2 text-center text-xs text-muted-foreground border-t border-light/20">
        <p>
          Αν το PDF δεν εμφανίζεται, χρησιμοποιήστε τα κουμπιά για άνοιγμα σε νέα καρτέλα ή κατέβασμα
        </p>
      </div>
    </Card>
  );
}
