import { CheckCircle, CloudUpload, Loader2 } from "lucide-react";
import { Card, CardContent } from "./card";
import { Progress } from "./progress";

interface UploadProgressProps {
  isUploading: boolean;
  progress: number;
  isSubmitting: boolean;
  message?: string;
  errorMessage?: string;
  hasError?: boolean;
  handleError?: () => void;
  language?: string;
}

export const UploadProgress = ({
  isUploading,
  progress,
  isSubmitting,
  message = "Uploading files...",
  errorMessage,
  handleError,
  language = "el",
}: UploadProgressProps) => {
  // Don't show anything if not uploading or submitting and no error
  if (!isUploading && !isSubmitting && !errorMessage) return null;

  // Show error message if there is one
  if (errorMessage) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
        <Card className="w-[90%] max-w-md bg-gradient-to-br from-white to-light/20">
          <CardContent className="p-6">
            <div className="flex flex-col items-center space-y-4">
              <CloudUpload className="h-12 w-12 text-destructive" />
              <h3 className="text-xl font-bold text-destructive">
                {language === "el" ? "Σφάλμα" : "Error"}
              </h3>
              <div className="text-center">
                <p className="text-sm text-primary font-medium mb-2">
                  {language === "el"
                    ? "Προέκυψε σφάλμα κατά την υποβολή της φόρμας:"
                    : "An error occurred during form submission:"}
                </p>
                <p className="text-sm text-destructive">{errorMessage}</p>
              </div>
              <button
                onClick={() => {
                  // Call the error handler to reset state and clean up file inputs
                  if (handleError) handleError();
                }}
                className="bg-primary hover:bg-primary-hover text-white font-bold py-2 px-4 rounded"
              >
                {language === "el" ? "Επιστροφή" : "Back"}
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <Card className="w-[90%] max-w-md bg-gradient-to-br from-white to-light/20">
        <CardContent className="p-6">
          <div className="flex flex-col items-center space-y-4">
            {isUploading ? (
              <>
                <CloudUpload className="h-12 w-12 text-primary animate-pulse" />
                <h3 className="text-xl font-bold text-primary">{message}</h3>
                <div className="w-full">
                  <Progress
                    value={Math.min(progress, 99)}
                    className="h-2 bg-light/20"
                    indicatorClassName="bg-gradient-to-r from-primary to-secondary"
                  />
                  <p className="text-right text-sm mt-1 text-primary">
                    {Math.min(progress, 99)}%
                  </p>
                </div>
              </>
            ) : isSubmitting ? (
              <>
                {progress >= 100 ? (
                  <CheckCircle className="h-12 w-12 text-green-500" />
                ) : (
                  <Loader2 className="h-12 w-12 text-primary animate-spin" />
                )}
                <h3 className="text-xl font-bold text-primary">
                  {progress === 100
                    ? language === "el"
                      ? "Επεξεργασία υποβολής..."
                      : "Processing submission..."
                    : language === "el"
                    ? "Υποβάλλεται η αίτησή σας..."
                    : "Submitting your application..."}
                </h3>
                <p className="text-sm text-primary text-center">
                  {progress >= 100
                    ? language === "el"
                      ? "Η αίτησή σας υποβάλλεται. Παρακαλώ περιμένετε."
                      : "Your application is being processed. Please wait a moment."
                    : language === "el"
                    ? "Παρακαλωσή περιμένετε."
                    : "Please wait while we submit your application."}
                </p>
              </>
            ) : null}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
