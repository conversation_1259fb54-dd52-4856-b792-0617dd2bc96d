import { invalidateSessionCache, updateSessionCache } from "@/lib/authUtils";
import { supabase } from "@/lib/supabase";
import { Session, User } from "@supabase/supabase-js";
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useNavigate } from "react-router-dom";

// Define the shape of our auth context
type AuthContextType = {
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAnonymous: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signInWithGoogle: () => Promise<void>;
  signInAnonymously: () => Promise<{ error: any | null }>;
  signOut: () => Promise<void>;
  upgradeAnonymousUser: (
    email: string,
    password: string
  ) => Promise<{ error: any | null }>;
  reloadUser: () => Promise<{ error: any | null }>;
};

// Create the context with a default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider props
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // Computed properties
  const isAuthenticated = !!session && !!user;

  // Determine if the user is anonymous
  const isAnonymous = useMemo(() => {
    if (!isAuthenticated || !user) return false;

    // Check multiple indicators of an anonymous user
    return (
      user.is_anonymous ||
      // Check user_metadata.is_anonymous flag
      user.user_metadata?.is_anonymous === true ||
      // Check if provider is 'anonymous'
      user.app_metadata?.provider === "anonymous" ||
      // Check if email is null (anonymous users don't have emails)
      user.email === null ||
      // Check if the user was created with signInAnonymously
      user.app_metadata?.providers?.includes("anonymous")
    );
  }, [isAuthenticated, user]);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      try {
        // Get the current session
        const { data } = await supabase.auth.getSession();

        if (data.session) {
          setSession(data.session);
          setUser(data.session.user);
          // Update the session cache
          updateSessionCache(data.session);

          // Check if this is an anonymous user
          const isUserAnonymous =
            data.session.user.user_metadata?.is_anonymous === true ||
            data.session.user.app_metadata?.provider === "anonymous" ||
            data.session.user.email === null;

          if (!isUserAnonymous) {
            console.log("Non-anonymous user detected in initial auth check");
          }
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.debug("Auth state changed:", event, newSession);
        console.debug("User metadata:", newSession?.user?.user_metadata);
        console.debug("App metadata:", newSession?.user?.app_metadata);

        // Check if this is an anonymous user
        const isUserAnonymous =
          newSession?.user?.user_metadata?.is_anonymous === true ||
          newSession?.user?.app_metadata?.provider === "anonymous" ||
          newSession?.user?.email === null;

        if (isUserAnonymous) {
          console.log("Anonymous user detected in auth state change");

          // If the user is anonymous but not marked as such in metadata, update it
          if (
            newSession?.user &&
            newSession.user.user_metadata?.is_anonymous !== true
          ) {
            console.debug("Marking user as anonymous in metadata");
            await supabase.auth.updateUser({
              data: {
                is_anonymous: true,
              },
            });
          }
        }

        // Check for app_metadata changes that might affect permissions
        if (event === "USER_UPDATED" && newSession?.user) {
          console.log(
            "User updated event detected, checking for permission changes"
          );

          // Use setTimeout to ensure this runs after the current callback completes
          setTimeout(async () => {
            try {
              // Reload user to get updated permissions
              await reloadUser();

              // We can't directly use the usePermissions hook here because we're in a callback
              // Instead, we'll dispatch a custom event that components can listen for
              const permissionChangeEvent = new CustomEvent(
                "permission-change",
                {
                  detail: { userId: newSession.user.id },
                }
              );
              window.dispatchEvent(permissionChangeEvent);
              console.debug("Permission change event dispatched");

              // Show a toast notification to inform the user
              const { toast } = await import("@/components/ui/use-toast");
              toast({
                title: "Ενημέρωση Δικαιωμάτων",
                description: "Τα δικαιώματά σας έχουν ενημερωθεί.",
                duration: 5000,
              });
            } catch (error) {
              console.error("Error handling permission changes:", error);
            }
          }, 0);
        }

        setSession(newSession);
        setUser(newSession?.user || null);

        // Update the session cache
        if (newSession) {
          updateSessionCache(newSession);
        } else {
          invalidateSessionCache();
        }

        // Handle specific auth events
        if (event === "SIGNED_OUT") {
          // Clear any auth-related storage
          sessionStorage.removeItem("lastRoute");
          sessionStorage.removeItem("redirectAfterLogin");
        }
      }
    );

    // Cleanup
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { error };
      }

      return { error: null };
    } catch (error) {
      console.error("Error signing in:", error);
      return { error };
    }
  };

  // Sign in with Google
  const signInWithGoogle = async () => {
    try {
      await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${window.location.origin}/dashboard`,
        },
      });
    } catch (error) {
      console.error("Error signing in with Google:", error);
    }
  };

  // Sign in anonymously
  const signInAnonymously = async () => {
    try {
      // Create anonymous session
      const { data, error } = await supabase.auth.signInAnonymously();

      if (error) {
        return { error };
      }

      // Explicitly mark the user as anonymous in user_metadata
      if (data?.session?.user) {
        console.log("Marking new user as anonymous");
        await supabase.auth.updateUser({
          data: {
            is_anonymous: true,
          },
        });
      }

      return { error: null };
    } catch (error) {
      console.error("Error signing in anonymously:", error);
      return { error };
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      // Log the current state before signing out
      console.log("Signing out user:", user?.id);
      console.debug("User is anonymous:", isAnonymous);
      console.debug("User role:", user?.app_metadata?.role);

      try {
        // Sign out the current user
        await supabase.auth.signOut();
      } catch (error) {
        console.error("Error during supabase.auth.signOut():", error);

        // If we get a session_not_found error, we can still proceed with local cleanup
        if (
          error.code === "session_not_found" ||
          (error.message && error.message.includes("session_not_found"))
        ) {
          console.error(
            "Session not found error caught, proceeding with local cleanup"
          );
        } else {
          throw error; // Re-throw other errors
        }
      }

      // Clear the session cache
      invalidateSessionCache();

      // Clear session state
      setSession(null);
      setUser(null);

      // Clear any auth-related storage
      sessionStorage.removeItem("lastRoute");
      sessionStorage.removeItem("redirectAfterLogin");

      // For nanny users, we want to ensure a clean state
      if (
        user?.app_metadata?.role === "nanny" ||
        user?.app_metadata?.role === "both"
      ) {
        console.debug("Nanny user detected, performing additional cleanup");
        localStorage.removeItem("supabase.auth.token");
        sessionStorage.clear();
      }

      // Navigate to the home page
      navigate("/");
    } catch (error) {
      console.error("Error signing out:", error);

      // Even if there's an error, try to navigate away
      navigate("/");
    }
  };

  // Upgrade anonymous user to registered user
  const upgradeAnonymousUser = async (email: string, password: string) => {
    try {
      if (!user || !isAnonymous) {
        return { error: new Error("No anonymous user to upgrade") };
      }

      const { error } = await supabase.auth.updateUser({
        email,
        password,
        data: {
          is_anonymous: false,
        },
      });

      if (error) {
        return { error };
      }

      return { error: null };
    } catch (error) {
      console.error("Error upgrading anonymous user:", error);
      return { error };
    }
  };

  // Reload the current user to get updated metadata
  const reloadUser = async () => {
    try {
      console.debug("Reloading user to get updated metadata");
      setIsLoading(true);

      // Get the current session from Supabase
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error("Error reloading user:", error);
        return { error };
      }

      if (data.session) {
        // Update the session and user state
        setSession(data.session);
        setUser(data.session.user);

        // Update the session cache
        updateSessionCache(data.session);

        console.log("User reloaded successfully:", data.session.user);
      } else {
        console.warn("No session found when reloading user");
      }

      return { error: null };
    } catch (error) {
      console.error("Error reloading user:", error);
      return { error };
    } finally {
      setIsLoading(false);
    }
  };

  // Provide the auth context value
  const value = {
    session,
    user,
    isLoading,
    isAuthenticated,
    isAnonymous,
    signIn,
    signInWithGoogle,
    signInAnonymously,
    signOut,
    upgradeAnonymousUser,
    reloadUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
};
