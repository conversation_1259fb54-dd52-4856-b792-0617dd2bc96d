import React, { createContext, ReactNode, useContext, useState } from "react";
import { formSteps } from "../schemas/FormSchema";

interface CandidateFormContextType {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  validateCurrentStep: React.MutableRefObject<() => Promise<boolean>>;
  isFirstStep: boolean;
  isLastStep: boolean;
  totalSteps: number;
  language: "el" | "en";
  setLanguage: (lang: "el" | "en") => void;
  visitedSteps: number[];
  languages: { language: string; level: string }[];
  setLanguages: React.Dispatch<
    React.SetStateAction<{ language: string; level: string }[]>
  >;
  workExperience: {
    period: string;
    location: string;
    position: string;
    children: string;
    schedule: string;
    endingReason: string;
  }[];
  setWorkExperience: React.Dispatch<
    React.SetStateAction<
      {
        period: string;
        location: string;
        position: string;
        children: string;
        schedule: string;
        endingReason: string;
      }[]
    >
  >;
  references: { name: string; phone: string; email: string }[];
  setReferences: React.Dispatch<
    React.SetStateAction<{ name: string; phone: string; email: string }[]>
  >;
  validationAttempted: boolean;
  setValidationAttempted: React.Dispatch<React.SetStateAction<boolean>>;
}

const CandidateFormContext = createContext<
  CandidateFormContextType | undefined
>(undefined);

// Helper function to save form state to localStorage
const saveFormState = (state: any) => {
  try {
    localStorage.setItem("candidateFormContextState", JSON.stringify(state));
  } catch (error) {
    console.error("Error saving form state:", error);
  }
};

// Helper function to get saved form state from localStorage
const getSavedFormState = () => {
  try {
    const savedState = localStorage.getItem("candidateFormContextState");
    return savedState ? JSON.parse(savedState) : null;
  } catch (error) {
    console.error("Error parsing saved form state:", error);
    return null;
  }
};

export const CandidateFormProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const savedState = getSavedFormState();

  // Initialize state with saved values or defaults
  const [currentStep, setCurrentStep] = useState(savedState?.currentStep || 0);
  const [language, setLanguage] = useState<"el" | "en">(
    savedState?.language || "el"
  );
  const [languages, setLanguages] = useState<
    { language: string; level: string }[]
  >(savedState?.languages || [{ language: "", level: "" }]);

  const [validationAttempted, setValidationAttempted] = useState(
    savedState?.validationAttempted || false
  );

  // Create a ref for the validateCurrentStep function
  const validateCurrentStep = React.useRef<() => Promise<boolean>>(async () => {
    // Default implementation that validates the current step
    setValidationAttempted(true);

    // This will be overridden by the form component with actual validation logic
    console.debug("Default validation in CandidateFormContext");
    return true;
  });

  // Initialize work experience and references
  const [workExperience, setWorkExperience] = useState<
    {
      period: string;
      location: string;
      position: string;
      children: string;
      schedule: string;
      endingReason: string;
    }[]
  >(
    savedState?.workExperience || [
      {
        period: "",
        location: "",
        position: "",
        children: "",
        schedule: "",
        endingReason: "",
      },
    ]
  );

  const [references, setReferences] = useState<
    { name: string; phone: string; email: string }[]
  >(savedState?.references || [{ name: "", phone: "", email: "" }]);

  // Track visited steps
  const [visitedSteps, setVisitedSteps] = useState<number[]>(
    savedState?.visitedSteps || [0]
  );

  // Calculate total steps based on form type
  const totalSteps = formSteps.length;

  // Derived state
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  // Navigation functions
  const nextStep = () => {
    if (currentStep < totalSteps - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);

      // Add the new step to visited steps if not already there
      if (!visitedSteps.includes(newStep)) {
        setVisitedSteps([...visitedSteps, newStep]);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      setCurrentStep(step);

      // Add the new step to visited steps if not already there
      if (!visitedSteps.includes(step)) {
        setVisitedSteps([...visitedSteps, step]);
      }
    }
  };

  // Save state whenever key values change
  React.useEffect(() => {
    const state = {
      currentStep,
      language,
      languages,
      workExperience,
      references,
      visitedSteps,
      validationAttempted,
    };
    saveFormState(state);
  }, [
    currentStep,
    language,
    languages,
    workExperience,
    references,
    visitedSteps,
    validationAttempted,
  ]);

  return (
    <CandidateFormContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        nextStep,
        prevStep,
        isFirstStep,
        isLastStep,
        totalSteps,
        language,
        setLanguage,
        goToStep,
        visitedSteps,
        languages,
        setLanguages,
        workExperience,
        setWorkExperience,
        references,
        setReferences,
        validateCurrentStep,
        validationAttempted,
        setValidationAttempted,
      }}
    >
      {children}
    </CandidateFormContext.Provider>
  );
};

export const useCandidateFormContext = () => {
  const context = useContext(CandidateFormContext);
  if (context === undefined) {
    throw new Error(
      "useCandidateFormContext must be used within a CandidateFormProvider"
    );
  }
  return context;
};
