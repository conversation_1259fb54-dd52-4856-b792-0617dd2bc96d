import React, { create<PERSON>ontext, ReactNode, useContext, useState } from "react";
import { formSteps, nannyRequestFormSteps } from "../schemas/FormSchema";

interface FormContextType {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  validateCurrentStep: React.MutableRefObject<() => Promise<boolean>>;
  isFirstStep: boolean;
  isLastStep: boolean;
  totalSteps: number;
  language: "el" | "en";
  setLanguage: (lang: "el" | "en") => void;
  visitedSteps: number[];
  formType?: "candidate" | "nannyRequest";
  setFormType?: (type: "candidate" | "nannyRequest") => void;
  languages: { language: string; level: string }[];
  setLanguages: React.Dispatch<
    React.SetStateAction<{ language: string; level: string }[]>
  >;
  workExperience: {
    period: string;
    location: string;
    position: string;
    children: string;
    schedule: string;
    endingReason: string;
  }[];
  setWorkExperience: React.Dispatch<
    React.SetStateAction<
      {
        period: string;
        location: string;
        position: string;
        children: string;
        schedule: string;
        endingReason: string;
      }[]
    >
  >;
  references: { name: string; phone: string; email: string }[];
  setReferences: React.Dispatch<
    React.SetStateAction<{ name: string; phone: string; email: string }[]>
  >;
  validationAttempted: boolean;
  setValidationAttempted: React.Dispatch<React.SetStateAction<boolean>>;
}

export const FormContext = createContext<FormContextType | undefined>(
  undefined
);

export const useFormContext = () => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error("useFormContext must be used within a FormProvider");
  }
  return context;
};

interface FormProviderProps {
  children: ReactNode;
}

export const FormProvider: React.FC<FormProviderProps> = ({ children }) => {
  // Load form state from localStorage if available
  const loadSavedState = () => {
    try {
      const savedState = localStorage.getItem("formContextState");
      return savedState ? JSON.parse(savedState) : null;
    } catch (error) {
      console.error("Error loading saved form state:", error);
      return null;
    }
  };

  const savedState = loadSavedState();

  const [currentStep, setCurrentStep] = useState(savedState?.currentStep || 0);
  const [language, setLanguage] = useState<"el" | "en">(
    savedState?.language || "el"
  );
  const [formType, setFormType] = useState<"candidate" | "nannyRequest">(
    savedState?.formType || "candidate"
  );
  const [languages, setLanguages] = useState<
    { language: string; level: string }[]
  >(savedState?.languages || [{ language: "", level: "" }]);

  const [validationAttempted, setValidationAttempted] = useState(
    savedState?.validationAttempted || false
  );

  // Create a ref for the validateCurrentStep function
  const validateCurrentStep = React.useRef<() => Promise<boolean>>(async () => {
    // Default implementation that always returns true
    setValidationAttempted(true);
    return true;
  });

  const [workExperience, setWorkExperience] = useState<
    {
      period: string;
      location: string;
      position: string;
      children: string;
      schedule: string;
      endingReason: string;
    }[]
  >(
    savedState?.workExperience || [
      {
        period: "",
        location: "",
        position: "",
        children: "",
        schedule: "",
        endingReason: "",
      },
    ]
  );

  const [references, setReferences] = useState<
    { name: string; phone: string; email: string }[]
  >(savedState?.references || [{ name: "", phone: "", email: "" }]);

  // Determine total steps based on form type
  const totalSteps =
    formType === "nannyRequest"
      ? nannyRequestFormSteps.length
      : formSteps.length;
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  // Ensure visitedSteps is always initialized with at least the first step
  const [visitedSteps, setVisitedSteps] = useState<number[]>(
    savedState?.visitedSteps || [0]
  );

  // History of previously visited steps (for navigation) - moved above

  // Save form context state to localStorage
  const saveFormState = (state: any) => {
    try {
      localStorage.setItem("formContextState", JSON.stringify(state));
    } catch (error) {
      console.error("Error saving form state:", error);
    }
  };

  // Save state whenever key values change
  React.useEffect(() => {
    const state = {
      currentStep,
      language,
      formType,
      languages,
      workExperience,
      references,
      visitedSteps,
      validationAttempted,
    };
    saveFormState(state);
  }, [
    currentStep,
    language,
    formType,
    languages,
    workExperience,
    references,
    visitedSteps,
    validationAttempted,
  ]);

  const nextStep = () => {
    if (currentStep < totalSteps - 1) {
      const nextStepIndex = currentStep + 1;
      setCurrentStep(nextStepIndex);
      // Update visited steps
      if (!visitedSteps.includes(nextStepIndex)) {
        setVisitedSteps([...visitedSteps, nextStepIndex]);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      setCurrentStep(step);
    }
  };

  // The validateCurrentStep function is now a ref that can be overridden by form components

  return (
    <FormContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        nextStep,
        prevStep,
        isFirstStep,
        isLastStep,
        totalSteps,
        language,
        setLanguage,
        goToStep,
        visitedSteps,
        formType,
        setFormType,
        languages,
        setLanguages,
        workExperience,
        setWorkExperience,
        references,
        setReferences,
        validateCurrentStep,
        validationAttempted,
        setValidationAttempted,
      }}
    >
      {children}
    </FormContext.Provider>
  );
};
