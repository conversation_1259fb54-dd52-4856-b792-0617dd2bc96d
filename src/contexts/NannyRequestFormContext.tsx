import React, { createContext, ReactNode, useContext, useState } from "react";
import { nannyRequestFormSteps } from "../schemas/FormSchema";

interface NannyRequestFormContextType {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  validateCurrentStep: React.MutableRefObject<() => Promise<boolean>>;
  isFirstStep: boolean;
  isLastStep: boolean;
  totalSteps: number;
  language: "el" | "en";
  setLanguage: (lang: "el" | "en") => void;
  visitedSteps: number[];
  languages: { language: string; level: string }[];
  setLanguages: React.Dispatch<
    React.SetStateAction<{ language: string; level: string }[]>
  >;
  validationAttempted: boolean;
  setValidationAttempted: React.Dispatch<React.SetStateAction<boolean>>;
}

const NannyRequestFormContext = createContext<
  NannyRequestFormContextType | undefined
>(undefined);

// Helper function to save form state to localStorage
const saveFormState = (state: any) => {
  try {
    localStorage.setItem("nannyRequestFormContextState", JSON.stringify(state));
  } catch (error) {
    console.error("Error saving form state:", error);
  }
};

// Helper function to get saved form state from localStorage
const getSavedFormState = () => {
  try {
    const savedState = localStorage.getItem("nannyRequestFormContextState");
    return savedState ? JSON.parse(savedState) : null;
  } catch (error) {
    console.error("Error parsing saved form state:", error);
    return null;
  }
};

export const NannyRequestFormProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const savedState = getSavedFormState();

  // Initialize state with saved values or defaults
  const [currentStep, setCurrentStep] = useState(savedState?.currentStep || 0);
  const [language, setLanguage] = useState<"el" | "en">(
    savedState?.language || "el"
  );
  const [languages, setLanguages] = useState<
    { language: string; level: string }[]
  >(savedState?.languages || [{ language: "", level: "" }]);

  const [validationAttempted, setValidationAttempted] = useState(
    savedState?.validationAttempted || false
  );

  // Create a ref for the validateCurrentStep function
  const validateCurrentStep = React.useRef<() => Promise<boolean>>(async () => {
    // Default implementation that validates the current step
    setValidationAttempted(true);

    // This will be overridden by the form component with actual validation logic
    console.debug("Default validation in NannyRequestFormContext");
    return true;
  });

  // Track visited steps
  const [visitedSteps, setVisitedSteps] = useState<number[]>(
    savedState?.visitedSteps || [0]
  );

  // Calculate total steps based on form type
  const totalSteps = nannyRequestFormSteps.length;

  // Derived state
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  // Navigation functions
  const nextStep = () => {
    if (currentStep < totalSteps - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);

      // Add the new step to visited steps if not already there
      if (!visitedSteps.includes(newStep)) {
        setVisitedSteps([...visitedSteps, newStep]);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      setCurrentStep(step);

      // Add the new step to visited steps if not already there
      if (!visitedSteps.includes(step)) {
        setVisitedSteps([...visitedSteps, step]);
      }
    }
  };

  // Save state whenever key values change
  React.useEffect(() => {
    const state = {
      currentStep,
      language,
      languages,
      visitedSteps,
      validationAttempted,
    };
    saveFormState(state);
  }, [currentStep, language, languages, visitedSteps, validationAttempted]);

  return (
    <NannyRequestFormContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        nextStep,
        prevStep,
        isFirstStep,
        isLastStep,
        totalSteps,
        language,
        setLanguage,
        goToStep,
        visitedSteps,
        languages,
        setLanguages,
        validateCurrentStep,
        validationAttempted,
        setValidationAttempted,
      }}
    >
      {children}
    </NannyRequestFormContext.Provider>
  );
};

export const useNannyRequestFormContext = () => {
  const context = useContext(NannyRequestFormContext);
  if (context === undefined) {
    throw new Error(
      "useNannyRequestFormContext must be used within a NannyRequestFormProvider"
    );
  }
  return context;
};
