import { useAuth } from "@/contexts/AuthContext";
import { permissionsToPagePermissions } from "@/services/userService";
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from "react";

// Define the shape of our permissions context
type PagePermissions = Record<
  string,
  { edit: boolean; create: boolean; delete: boolean; view: boolean }
>;

type PermissionsContextType = {
  permissions: number[];
  pagePermissions: PagePermissions;
  isLoading: boolean;
  hasPermission: (permissionId: number) => boolean;
  hasPagePermission: (
    page: string,
    action: "view" | "create" | "edit" | "delete"
  ) => boolean;
  refreshPermissions: () => Promise<void>;
};

// Create the context with a default value
const PermissionsContext = createContext<PermissionsContextType | undefined>(
  undefined
);

// Provider props
interface PermissionsProviderProps {
  children: ReactNode;
}

export const PermissionsProvider = ({ children }: PermissionsProviderProps) => {
  const {
    user,
    isAuthenticated,
    isAnonymous,
    isLoading: isLoadingAuth,
  } = useAuth();
  const [permissions, setPermissions] = useState<number[]>([]);
  const [pagePermissions, setPagePermissions] = useState<PagePermissions>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Function to fetch user permissions
  const fetchPermissions = async () => {
    setIsLoading(true);
    try {
      if (!isAuthenticated || isAnonymous || !user) {
        // Anonymous or unauthenticated users have no permissions
        setPermissions([]);
        setPagePermissions({});
        return;
      }

      // Check if permissions are in app_metadata
      const metadataPermissions = user.app_metadata?.permissions || [];
      if (
        Array.isArray(metadataPermissions) &&
        metadataPermissions.length > 0
      ) {
        setPermissions(metadataPermissions);
        setPagePermissions(permissionsToPagePermissions(metadataPermissions));
        return;
      }

      // If not in metadata, set permissions to an empty array
      setPermissions([]);
      setPagePermissions({});
      return;
    } catch (error) {
      console.error("Error fetching permissions:", error);
      setPermissions([]);
      setPagePermissions({});
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch permissions when auth state changes
  useEffect(() => {
    fetchPermissions();
  }, [isAuthenticated, isAnonymous, user]);

  // Check if user has a specific permission
  const hasPermission = (permissionId: number): boolean => {
    return permissions.includes(permissionId);
  };

  // Check if user has a specific page permission
  const hasPagePermission = (
    page: string,
    action: "view" | "create" | "edit" | "delete"
  ): boolean => {
    // If the page doesn't exist in pagePermissions, user doesn't have permission
    if (!pagePermissions[page]) {
      return false;
    }

    // Return the specific action permission
    return pagePermissions[page][action];
  };

  // Refresh permissions
  const refreshPermissions = async (): Promise<void> => {
    await fetchPermissions();
  };

  // Provide the permissions context value
  const value = {
    permissions,
    pagePermissions,
    isLoading,
    hasPermission,
    hasPagePermission,
    refreshPermissions,
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};

// Custom hook to use the permissions context
export const usePermissions = (): PermissionsContextType => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error("usePermissions must be used within a PermissionsProvider");
  }
  return context;
};
