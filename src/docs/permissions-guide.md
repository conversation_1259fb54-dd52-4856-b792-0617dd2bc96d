# Permissions System Guide

This guide explains how to use the permissions system in the application, including how to check for view, edit, delete, and create permissions.

## Permission Types

The application uses a permission system based on page names and action types:

- **View**: Permission to view a page or resource
- **Create**: Permission to create new resources on a page
- **Edit**: Permission to edit existing resources on a page
- **Delete**: Permission to delete resources on a page

## Permission Hooks

The application provides several hooks for checking permissions:

### 1. usePermissionCheck

This hook provides methods to check if a user has permission to perform specific actions on a page:

```typescript
import { usePermissionCheck } from "@/hooks/usePermissionCheck";

const MyComponent = () => {
  const { canView, canCreate, canEdit, canDelete } = usePermissionCheck();

  // Check if user has permission to view the "nannies" page
  const hasViewPermission = canView("nannies");

  // Check if user has permission to create on the "nannies" page
  const hasCreatePermission = canCreate("nannies");

  // Check if user has permission to edit on the "nannies" page
  const hasEditPermission = canEdit("nannies");

  // Check if user has permission to delete on the "nannies" page
  const hasDeletePermission = canDelete("nannies");

  return (
    <div>
      {/* Use the permissions to conditionally render UI elements */}
      {hasCreatePermission && <Button>Create New Nanny</Button>}
      {hasEditPermission && <Button>Edit Nanny</Button>}
      {hasDeletePermission && <Button>Delete Nanny</Button>}
    </div>
  );
};
```

### 2. useRoutePermissions

This hook provides methods to check if a user has permission to perform specific actions on the current route:

```typescript
import { useRoutePermissions } from "@/hooks/useRoutePermissions";

const MyComponent = () => {
  const {
    canCreateOnCurrentRoute,
    canEditOnCurrentRoute,
    canDeleteOnCurrentRoute,
    getCurrentPermissionPage,
  } = useRoutePermissions();

  // Get the current permission page (e.g., "nannies", "crm", etc.)
  const permissionPage = getCurrentPermissionPage();

  // Check if user has permission to create on the current route
  const hasCreatePermission = canCreateOnCurrentRoute();

  // Check if user has permission to edit on the current route
  const hasEditPermission = canEditOnCurrentRoute();

  // Check if user has permission to delete on the current route
  const hasDeletePermission = canDeleteOnCurrentRoute();

  return (
    <div>
      {/* Use the permissions to conditionally render UI elements */}
      {hasCreatePermission && <Button>Create</Button>}
      {hasEditPermission && <Button>Edit</Button>}
      {hasDeletePermission && <Button>Delete</Button>}
    </div>
  );
};
```

## Route Permission Functions

The application also provides functions for checking permissions on specific routes:

```typescript
import {
  canCreateOnRoute,
  canEditOnRoute,
  canDeleteOnRoute,
} from "@/lib/routePermissions";
import { usePermissions } from "@/contexts/PermissionsContext";

const MyComponent = () => {
  const { hasPagePermission } = usePermissions();

  // Check if user has permission to create on a specific route
  const hasCreatePermission = canCreateOnRoute("/nannies", hasPagePermission);

  // Check if user has permission to edit on a specific route
  const hasEditPermission = canEditOnRoute("/nannies", hasPagePermission);

  // Check if user has permission to delete on a specific route
  const hasDeletePermission = canDeleteOnRoute("/nannies", hasPagePermission);

  return (
    <div>
      {/* Use the permissions to conditionally render UI elements */}
      {hasCreatePermission && <Button>Create</Button>}
      {hasEditPermission && <Button>Edit</Button>}
      {hasDeletePermission && <Button>Delete</Button>}
    </div>
  );
};
```

## Best Practices

1. **Use the hooks**: The hooks provide a convenient way to check permissions and should be used whenever possible.

2. **Disable buttons**: Instead of hiding buttons, consider disabling them when the user doesn't have permission. This provides better UX by showing what actions are possible but currently unavailable.

3. **Show feedback**: When a user tries to perform an action they don't have permission for, show a toast or other feedback explaining why the action was denied.

4. **Check permissions on the server**: Always check permissions on the server side as well, as client-side checks can be bypassed.

5. **Use the permission system consistently**: Make sure all actions that require permissions use the permission system consistently.

## Example Component

See the `PermissionActionExample` component for a complete example of how to use the permission system:

```typescript
import { useRoutePermissions } from "@/hooks/useRoutePermissions";

const MyComponent = () => {
  const { canCreateOnCurrentRoute, canEditOnCurrentRoute, canDeleteOnCurrentRoute } = useRoutePermissions();

  // Handle create action
  const handleCreate = () => {
    if (canCreateOnCurrentRoute()) {
      // Perform create action
    } else {
      // Show error message
    }
  };

  return (
    <div>
      <Button onClick={handleCreate} disabled={!canCreateOnCurrentRoute()}>
        Create
      </Button>
    </div>
  );
};
```
