import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/contexts/PermissionsContext";
import { useEffect } from "react";

/**
 * Hook to listen for permission changes and refresh permissions when they change
 *
 * @param onPermissionChange Optional callback to run when permissions change
 * @returns void
 */
export const usePermissionChangeListener = (
  onPermissionChange?: () => void
): void => {
  const { user } = useAuth();
  const { refreshPermissions } = usePermissions();

  useEffect(() => {
    if (!user) return;

    // Function to handle permission change events
    const handlePermissionChange = async (event: CustomEvent) => {
      // Check if this event is for the current user
      if (event.detail?.userId === user.id) {
        console.log("Permission change event received for current user");

        // Refresh permissions
        await refreshPermissions();

        // Call the callback if provided
        if (onPermissionChange) {
          onPermissionChange();
        }
      }
    };

    // Add event listener
    window.addEventListener(
      "permission-change",
      handlePermissionChange as EventListener
    );

    // Cleanup
    return () => {
      window.removeEventListener(
        "permission-change",
        handlePermissionChange as EventListener
      );
    };
  }, [user, refreshPermissions, onPermissionChange]);
};
