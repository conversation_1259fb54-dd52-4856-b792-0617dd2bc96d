import { usePermissions } from "@/contexts/PermissionsContext";

/**
 * Custom hook for checking user permissions
 * Provides convenient methods to check if a user has specific permissions
 */
export const usePermissionCheck = () => {
  const { hasPermission, hasPagePermission } = usePermissions();

  /**
   * Check if user has permission to view a specific page
   * @param page The page name (Dashboard, CRM, Nannies, etc.)
   * @returns Whether the user has view permission for the page
   */
  const canView = (page: string): boolean => {
    return hasPagePermission(page, "view");
  };

  /**
   * Check if user has permission to create in a specific page
   * @param page The page name (Dashboard, CRM, Nannies, etc.)
   * @returns Whether the user has create permission for the page
   */
  const canCreate = (page: string): boolean => {
    return hasPagePermission(page, "create");
  };

  /**
   * Check if user has permission to edit in a specific page
   * @param page The page name (Dashboard, CRM, Nannies, etc.)
   * @returns Whether the user has edit permission for the page
   */
  const canEdit = (page: string): boolean => {
    return hasPagePermission(page, "edit");
  };

  /**
   * Check if user has permission to delete in a specific page
   * @param page The page name (Dashboard, CRM, Nannies, etc.)
   * @returns Whether the user has delete permission for the page
   */
  const canDelete = (page: string): boolean => {
    return hasPagePermission(page, "delete");
  };

  /**
   * Check if user has a specific permission code
   * @param code The permission code from PERMISSION_CODES
   * @returns Whether the user has the specific permission
   */
  const hasSpecificPermission = (code: number): boolean => {
    return hasPermission(code);
  };

  return {
    canView,
    canCreate,
    canEdit,
    canDelete,
    hasSpecificPermission,
  };
};
