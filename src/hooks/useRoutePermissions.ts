import { useLocation } from "react-router-dom";
import { usePermissions } from "@/contexts/PermissionsContext";
import {
  canCreateOnRoute,
  canDeleteOnRoute,
  canEditOnRoute,
  getRoutePermission,
} from "@/lib/routePermissions";

/**
 * Custom hook for checking permissions on the current route
 * Provides convenient methods to check if a user has specific permissions for the current route
 */
export const useRoutePermissions = () => {
  const location = useLocation();
  const { hasPagePermission } = usePermissions();
  const currentPath = location.pathname;

  /**
   * Check if user has permission to view the current route
   * @returns Whether the user has view permission for the current route
   */
  const canViewCurrentRoute = (): boolean => {
    const routePermission = getRoutePermission(currentPath);
    
    // If no route permission found or not a permission-restricted route, return false
    if (!routePermission || 
        !routePermission.permissionPage) {
      return false;
    }
    
    // Check if user has view permission for this page
    return hasPagePermission(routePermission.permissionPage, "view");
  };

  /**
   * Check if user has permission to create on the current route
   * @returns Whether the user has create permission for the current route
   */
  const canCreateOnCurrentRoute = (): boolean => {
    return canCreateOnRoute(currentPath, hasPagePermission);
  };

  /**
   * Check if user has permission to edit on the current route
   * @returns Whether the user has edit permission for the current route
   */
  const canEditOnCurrentRoute = (): boolean => {
    return canEditOnRoute(currentPath, hasPagePermission);
  };

  /**
   * Check if user has permission to delete on the current route
   * @returns Whether the user has delete permission for the current route
   */
  const canDeleteOnCurrentRoute = (): boolean => {
    return canDeleteOnRoute(currentPath, hasPagePermission);
  };

  /**
   * Get the permission page name for the current route
   * @returns The permission page name or undefined if not found
   */
  const getCurrentPermissionPage = (): string | undefined => {
    const routePermission = getRoutePermission(currentPath);
    return routePermission?.permissionPage;
  };

  return {
    canViewCurrentRoute,
    canCreateOnCurrentRoute,
    canEditOnCurrentRoute,
    canDeleteOnCurrentRoute,
    getCurrentPermissionPage,
  };
};
