@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 210 29% 14%; /* Based on Navy Blue */

    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 210 29% 14%;

    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 210 29% 14%;

    /* Primary colors - Navy Blue */
    --primary: 210 100% 14%; /* #002347 */
    --primary-foreground: 0 0% 100%;

    /* Secondary colors - Taupe/Brown */
    --secondary: 24 10% 49%; /* #8a7a71 */
    --secondary-foreground: 0 0% 100%;

    /* Tertiary colors - Beige */
    --tertiary: 33 20% 75%; /* #ccc3b4 */
    --tertiary-foreground: 0 0% 20%;

    /* Accent colors - Light Blue */
    --accent: 214 50% 84%; /* #c5d4e8 */
    --accent-foreground: 0 0% 20%;

    /* Light colors - Cream */
    --light: 36 70% 92%; /* #f8eedf */
    --light-foreground: 0 0% 20%;



    /* Destructive colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Border and input colors */
    --border: 210 20% 90%;
    --input: 210 20% 90%;
    --ring: 210 100% 14%;

    --radius: 0.5rem;
  }

  .dark {
    /* Base colors */
    --background: 210 29% 10%;
    --foreground: 0 0% 95%;

    /* Card colors */
    --card: 210 29% 12%;
    --card-foreground: 0 0% 95%;

    /* Popover colors */
    --popover: 210 29% 12%;
    --popover-foreground: 0 0% 95%;

    /* Primary colors - Navy Blue (lighter for dark mode) */
    --primary: 210 70% 30%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors - Taupe/Brown (lighter for dark mode) */
    --secondary: 24 15% 60%;
    --secondary-foreground: 0 0% 100%;

    /* Tertiary colors - Beige (darker for dark mode) */
    --tertiary: 33 15% 50%;
    --tertiary-foreground: 0 0% 95%;

    /* Accent colors - Light Blue (darker for dark mode) */
    --accent: 214 30% 50%;
    --accent-foreground: 0 0% 95%;

    /* Light colors - Cream (darker for dark mode) */
    --light: 36 30% 40%;
    --light-foreground: 0 0% 95%;

    /* Muted colors */
    --muted: 36 30% 30%;
    --muted-foreground: 36 50% 80%;

    /* Destructive colors */
    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 100%;

    /* Border and input colors */
    --border: 210 15% 30%;
    --input: 210 15% 30%;
    --ring: 210 70% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Styles */
.btn-primary {
  @apply bg-primary hover:bg-primary-hover text-primary-foreground;
}

.btn-secondary {
  @apply bg-secondary hover:bg-secondary-hover text-secondary-foreground;
}

.btn-tertiary {
  @apply bg-tertiary hover:bg-tertiary-hover text-tertiary-foreground;
}

.btn-accent {
  @apply bg-accent hover:bg-accent-hover text-accent-foreground;
}

.btn-light {
  @apply bg-light hover:bg-light-hover text-light-foreground;
}

.input-primary {
  @apply h-12 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
}

.card-hover {
  @apply transition-all duration-200 hover:shadow-md;
}

.nav-link {
  @apply text-muted-foreground hover:text-foreground transition-colors;
}

.badge-primary {
  @apply bg-primary/10 text-primary rounded-full px-2 py-1 text-xs font-medium;
}

.badge-secondary {
  @apply bg-secondary/10 text-secondary rounded-full px-2 py-1 text-xs font-medium;
}

.badge-tertiary {
  @apply bg-tertiary/10 text-tertiary rounded-full px-2 py-1 text-xs font-medium;
}

.badge-accent {
  @apply bg-accent/10 text-accent rounded-full px-2 py-1 text-xs font-medium;
}

.badge-light {
  @apply bg-light/10 text-light-foreground rounded-full px-2 py-1 text-xs font-medium;
}

/* Custom Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse-soft {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce-subtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 5s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.5) 50%,
    rgba(255,255,255,0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
}

.animate-spin-slow {
  animation: spin-slow 10s linear infinite;
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

.shake-animation {
  animation: shake 0.5s ease-in-out;
}

.transition-all-medium {
  transition: all 0.5s ease;
}

.hover-scale {
  @apply transition-transform duration-300;
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-float:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 15px hsla(var(--primary), 0.3);
  transition: box-shadow 0.3s ease;
}

.hover-border-grow:hover {
  @apply border-primary;
  border-width: 2px;
  transition: all 0.3s ease;
}

.hover-tertiary:hover {
  @apply bg-tertiary/10;
  transition: background-color 0.3s ease;
}

.hover-light:hover {
  @apply bg-light/10;
  transition: background-color 0.3s ease;
}