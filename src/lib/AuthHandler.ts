import { getAuthSession } from "./authUtils";
import { supabase } from "./supabase";

/**
 * Upgrades an anonymous user to a registered user
 *
 * @param email - The user's email address
 * @param password - The user's password
 * @returns {Promise<boolean>} - Whether the upgrade was successful
 */
export const upgradeAnonymousUser = async (
  email: string,
  password: string
): Promise<boolean> => {
  try {
    // Check if we have an anonymous user using cached function
    const session = await getAuthSession();

    if (!session?.user || !session.user.is_anonymous) {
      return false;
    }

    // Convert the anonymous user to a registered user
    const { data, error } = await supabase.auth.updateUser({
      email,
      password,
      data: {
        is_anonymous: false,
      },
    });

    if (error) throw error;

    return true;
  } catch (error) {
    console.error("Error upgrading anonymous user:", error);
    // delete the anonymous session
    return false;
  }
};

/**
 * Handles Supabase OAuth redirects with URL hash fragments
 * This solves the issue with 404 errors when redirected from OAuth providers
 */
export const handleAuthRedirect = () => {
  if (window.location.hash && window.location.hash.includes("access_token")) {
    const path = window.location.pathname;

    if (path !== "/dashboard") {
      sessionStorage.setItem("supabaseAuthHash", window.location.hash);

      window.location.href = "/dashboard";
      return true;
    }
  }

  return false;
};

/**
 * Processes stored auth hash data and handles Supabase session
 * Call this in your dashboard component on mount
 */
export const processStoredAuthHash = async (supabaseClient = supabase) => {
  const storedHash = sessionStorage.getItem("supabaseAuthHash");

  if (storedHash) {
    try {
      sessionStorage.removeItem("supabaseAuthHash");

      // Process the auth hash
      const { error } = await supabaseClient.auth.getSession();

      if (error) {
        console.error("Error processing auth redirect:", error);
        return false;
      }

      return true;
    } catch (err) {
      console.error("Failed to process auth redirect:", err);
      return false;
    }
  }

  return false;
};
