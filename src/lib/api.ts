import axios from "axios";
import { getAuthSession, updateSessionCache } from "./authUtils";
import { supabase } from "./supabase";
import { toSnakeCase } from "./utils";

const API_BASE_URL = "https://nanny-blue-app.onrender.com/api";

const client = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to include auth token and convert data to snake_case
client.interceptors.request.use(async (config) => {
  try {
    // Get the current session using our cached function
    const session = await getAuthSession();

    // Define paths that should always be accessible with anonymous users
    const alwaysAccessiblePaths = ["/candidate-form", "/lead-form"];
    const currentPath = window.location.pathname;

    if (!session) {
      console.warn("No active session found.");
      // Check if we're on a form path that should create anonymous session
      if (alwaysAccessiblePaths.includes(currentPath)) {
        // Create anonymous user
        const { data, error } = await supabase.auth.signInAnonymously();
        if (error) {
          console.error("Error creating anonymous user:", error);
        } else if (data.session?.access_token) {
          config.headers.Authorization = `Bearer ${data.session.access_token}`;
          // Update cache
          updateSessionCache(data.session);
        }
      } else {
        // For other paths, try to refresh
        const { data } = await supabase.auth.refreshSession();
        if (data?.session?.access_token) {
          config.headers.Authorization = `Bearer ${data.session.access_token}`;
          // Update cache
          updateSessionCache(data.session);
        }
      }
    } else {
      // If session exists, add the access token as Authorization header
      if (session?.access_token) {
        config.headers.Authorization = `Bearer ${session.access_token}`;
      }
    }

    // Convert request data from camelCase to snake_case
    if (config.data) {
      config.data = toSnakeCase(config.data);
    }

    return config;
  } catch (error) {
    console.error("Error in request interceptor:", error);
    return config;
  }
});

// Add interceptors for handling errors
client.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error("API Error:", error.response || error.message);
    return Promise.reject(error);
  }
);

export default client;
