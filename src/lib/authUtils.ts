import { Session } from "@supabase/supabase-js";
import { supabase } from "./supabase";

// Cache for auth session to avoid duplicate requests
let cachedSession: Session | null = null;
let session: Session | null = null;

/**
 * Get the current auth session with caching to avoid duplicate requests
 * @returns The current session or null if not authenticated
 */
export const getAuthSession = async (): Promise<Session | null> => {
  try {
    // If there's already a request in progress, wait for it
    if (session) {
      console.log("Waiting for in-progress session request");
      try {
        // Don't return here, let the code below handle the result
        // This ensures consistent processing of the session
      } catch (error) {
        console.error("Error in pending session request:", error);
        session = null;
        // Continue to make a new request
      }
    }

    // Make a new request and cache it
    console.debug("Making new session request");
    session = (await supabase.auth.getSession()).data.session;

    if (!session || !session.user) {
      console.error("Invalid session response:", session);
      return null;
    }

    // Cache the session
    cachedSession = session;

    console.debug(
      "Session retrieved:",
      cachedSession ? "Valid session" : "No session"
    );
    return cachedSession;
  } catch (error) {
    console.error("Error getting auth session:", error);
    return null;
  } finally {
    // Always clear the promise to prevent hanging
    session = null;
  }
};

/**
 * Invalidate the session cache to force a fresh request next time
 */
export const invalidateSessionCache = () => {
  cachedSession = null;
};

/**
 * Update the session cache with a new session
 * @param session The new session to cache
 */
export const updateSessionCache = (session: Session | null) => {
  cachedSession = session;
};
