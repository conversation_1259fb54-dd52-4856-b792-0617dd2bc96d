// Client status mapping
export const CLIENT_STATUS = {
  LEAD: 1,
  REJECTED: 2,
  PAST_CLIENT: 3,
  ACTIVE_CLIENT: 4,
};

/**
 * Get the status label for a client status code
 * @param status The status code
 * @returns The status label
 */
export const getClientStatusLabel = (status?: number): string => {
  switch (status) {
    case CLIENT_STATUS.LEAD:
      return "Lead";
    case CLIENT_STATUS.REJECTED:
      return "Rejected";
    case CLIENT_STATUS.PAST_CLIENT:
      return "Past Client";
    case CLIENT_STATUS.ACTIVE_CLIENT:
      return "Active Client";
    default:
      return "Unknown";
  }
};

/**
 * Check if a client is considered a candidate based on status
 * @param status The status code
 * @returns True if the client is a candidate (Lead or Rejected)
 */
export const isClientCandidate = (status?: number): boolean => {
  return status === CLIENT_STATUS.LEAD || status === CLIENT_STATUS.REJECTED;
};

/**
 * Get the badge variant for a client status
 * @param status The status code
 * @returns The badge variant
 */
export const getClientStatusVariant = (
  status?: number
): "default" | "destructive" | "secondary" | "success" | "outline" => {
  switch (status) {
    case CLIENT_STATUS.LEAD:
      return "default";
    case CLIENT_STATUS.REJECTED:
      return "destructive";
    case CLIENT_STATUS.PAST_CLIENT:
      return "secondary";
    case CLIENT_STATUS.ACTIVE_CLIENT:
      return "success";
    default:
      return "outline";
  }
};
