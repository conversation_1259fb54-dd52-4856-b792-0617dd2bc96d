/**
 * Converts data to CSV format and triggers a download
 * @param data Array of objects to convert to CSV
 * @param filename Name of the file to download
 */
export const downloadCSV = <T extends Record<string, any>>(
  data: T[],
  filename: string
): void => {
  if (!data || data.length === 0) {
    console.error("No data to download");
    return;
  }

  try {
    // Get headers from the first object
    const headers = Object.keys(data[0]);

    // Create CSV content
    const csvContent = [
      // Headers row
      headers.join(","),
      // Data rows
      ...data.map((item) =>
        headers
          .map((header) => {
            // Handle special cases like objects, arrays, etc.
            const value = item[header];
            if (value === null || value === undefined) {
              return "";
            } else if (typeof value === "object") {
              return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
            } else if (typeof value === "string" && value.includes(",")) {
              return `"${value.replace(/"/g, '""')}"`;
            } else {
              return value;
            }
          })
          .join(",")
      ),
    ].join("\n");

    // Create a Blob with the CSV content
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

    // Create a download link
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `${filename}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);

    // Trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error downloading CSV:", error);
  }
};
