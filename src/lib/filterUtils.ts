import { useCallback, useRef } from "react";
import { PaginationParams } from "./paginationUtils";

// Debounce configuration
const DEBOUNCE_DELAY = 500; // 500ms debounce delay for text-based filters
const pendingDebounces: Record<string, NodeJS.Timeout> = {};

/**
 * Updates pagination with new filters and returns the updated pagination object
 * @param currentPagination Current pagination state
 * @param newFilters New filters to apply
 * @param mergeFilters Whether to merge new filters with existing ones (default: true)
 * @returns Updated pagination object
 */
export const updatePaginationWithFilters = (
  currentPagination: PaginationParams,
  newFilters: Record<string, any>,
  mergeFilters: boolean = true
): PaginationParams => {
  // Track which keys in newFilters are explicitly set to empty/"Όλα" to remove them
  const keysToRemove = new Set<string>();

  // Process filters to remove any "Όλα" or empty values
  const cleanedFilters: Record<string, any> = {};

  Object.entries(newFilters).forEach(([key, value]) => {
    // Skip empty values, "Όλα", or null/undefined
    if (
      value === "" ||
      value === "Όλα" ||
      value === null ||
      value === undefined
    ) {
      console.debug(`Marking filter for removal: ${key}=${value}`);
      keysToRemove.add(key);
      return;
    }
    cleanedFilters[key] = value;
  });

  // If merging, we need to handle filter removal properly
  let finalFilters: Record<string, any> = {};

  if (mergeFilters && currentPagination.filters) {
    // Start with current filters
    finalFilters = { ...currentPagination.filters };

    // First, remove any keys that were explicitly set to empty/"Όλα"
    keysToRemove.forEach((key) => {
      if (key in finalFilters) {
        console.debug(`Removing filter: ${key}`);
        delete finalFilters[key];
      }
    });

    // Then update with new values
    Object.entries(cleanedFilters).forEach(([key, value]) => {
      finalFilters[key] = value;
    });
  } else {
    // Just use the cleaned filters
    finalFilters = cleanedFilters;
  }

  console.debug("Final filters after cleaning:", finalFilters);
  console.debug("Keys removed:", Array.from(keysToRemove));

  return {
    ...currentPagination,
    page: 1, // Reset to page 1 when filters change
    filters: finalFilters,
  };
};

/**
 * Creates a filter function that updates pagination state with the filter value
 * @param key Filter key
 * @param setPagination Function to update pagination state
 * @param filterFn Optional custom filter function
 * @returns Filter function for the column
 */
export const createFilterFunction = <T>(
  key: keyof T,
  setPagination: (
    updater: (prev: PaginationParams) => PaginationParams
  ) => void,
  filterFn?: (row: T, columnId: string, filterValue: string) => boolean
) => {
  return (row: T, columnId: string, filterValue: string) => {
    // Update pagination filters
    setPagination((prev) => ({
      ...prev,
      filters: {
        ...prev.filters,
        [key]: filterValue,
      },
    }));

    // If a custom filter function is provided, use it
    if (filterFn) {
      return filterFn(row, columnId, filterValue);
    }

    // Default filter behavior for string values
    const value = row[key];
    if (typeof value === "string") {
      return value.toLowerCase().includes(filterValue.toLowerCase());
    }

    // For other types, try to convert to string and compare
    return String(value).includes(filterValue);
  };
};

/**
 * Creates a debounced filter function that updates pagination state with the filter value after a delay
 * @param key Filter key
 * @param setPagination Function to update pagination state
 * @param isTextFilter Whether this is a text-based filter that should be debounced
 * @returns Filter update function
 */
export const createDebouncedFilterUpdate = (
  key: string,
  setPagination: (
    updater: (prev: PaginationParams) => PaginationParams
  ) => void,
  isTextFilter: boolean = false
) => {
  return (value: string) => {
    // For text-based filters, use debounce
    if (isTextFilter) {
      // Clear any existing timeout for this key
      if (pendingDebounces[key]) {
        clearTimeout(pendingDebounces[key]);
      }

      // Set a new timeout
      pendingDebounces[key] = setTimeout(() => {
        console.debug(`Debounced filter applied: ${key} = ${value}`);

        // Update pagination filters after debounce
        setPagination((prev) => {
          // If value is empty or "Όλα", remove the filter
          if (value === "" || value === "Όλα") {
            const { [key]: _, ...restFilters } = prev.filters || {};
            return {
              ...prev,
              filters: restFilters,
            };
          }

          // Otherwise, add/update the filter
          return {
            ...prev,
            filters: {
              ...prev.filters,
              [key]: value,
            },
          };
        });

        delete pendingDebounces[key];
      }, DEBOUNCE_DELAY);
    } else {
      // For non-text filters, update immediately
      setPagination((prev) => {
        // If value is empty or "Όλα", remove the filter
        if (value === "" || value === "Όλα") {
          const { [key]: _, ...restFilters } = prev.filters || {};
          return {
            ...prev,
            filters: restFilters,
          };
        }

        // Otherwise, add/update the filter
        return {
          ...prev,
          filters: {
            ...prev.filters,
            [key]: value,
          },
        };
      });
    }
  };
};

/**
 * React hook for debounced filter updates
 * @param setPagination Function to update pagination state
 * @param delay Debounce delay in milliseconds (default: 500ms)
 * @returns Object with updateFilter function
 */
export const useDebouncedFilters = (
  setPagination: (
    updater: (prev: PaginationParams) => PaginationParams
  ) => void,
  delay: number = DEBOUNCE_DELAY
) => {
  // Store timeouts in a ref to persist between renders
  const timeoutsRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Function to update a filter with debounce
  const updateFilter = useCallback(
    (key: string, value: string, isTextFilter: boolean = false) => {
      // For text-based filters, use debounce
      if (isTextFilter) {
        // Clear any existing timeout for this key
        if (timeoutsRef.current[key]) {
          clearTimeout(timeoutsRef.current[key]);
        }

        // Set a new timeout
        timeoutsRef.current[key] = setTimeout(() => {
          console.debug(`Debounced filter applied: ${key} = ${value}`);

          // Update pagination filters after debounce
          setPagination((prev) => {
            // If value is empty or "Όλα", remove the filter
            if (value === "" || value === "Όλα") {
              const { [key]: _, ...restFilters } = prev.filters || {};
              return {
                ...prev,
                filters: restFilters,
              };
            }

            // Otherwise, add/update the filter
            return {
              ...prev,
              filters: {
                ...prev.filters,
                [key]: value,
              },
            };
          });

          // Clean up the timeout reference
          delete timeoutsRef.current[key];
        }, delay);
      } else {
        // For non-text filters, update immediately
        setPagination((prev) => {
          // If value is empty or "Όλα", remove the filter
          if (value === "" || value === "Όλα") {
            const { [key]: _, ...restFilters } = prev.filters || {};
            return {
              ...prev,
              filters: restFilters,
            };
          }

          // Otherwise, add/update the filter
          return {
            ...prev,
            filters: {
              ...prev.filters,
              [key]: value,
            },
          };
        });
      }
    },
    [setPagination, delay]
  );

  return { updateFilter };
};
