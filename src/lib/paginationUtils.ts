export interface PaginationParams {
  page: number;
  itemsPerPage: number;
  filters?: Record<string, any>;
}

export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
}

/**
 * Applies pagination to a Supabase query
 * @param query The Supabase query to paginate
 * @param params Pagination parameters (page and itemsPerPage)
 * @returns A promise that resolves to a paginated response
 */
export async function paginateQuery<T>(
  data: T[],
  params: PaginationParams,
  totalCount: number,
  totalPages: number
): Promise<PaginatedResponse<T>> {
  const { page, itemsPerPage, filters } = params;

  // Calculate range for Postgres range query
  const from = (page - 1) * itemsPerPage;
  const to = from + itemsPerPage - 1;

  try {
    console.debug("Paginating query with params:", {
      page,
      itemsPerPage,
      filters,
      from,
      to,
    });

    const dataResponse = Array.from(data).slice(from, to + 1);

    return {
      data: dataResponse,
      page,
      itemsPerPage,
      totalCount: totalCount || 0,
      totalPages: totalPages || 0,
    };
  } catch (error) {
    console.error("Error in paginateQuery:", error);
    throw error;
  }
}
