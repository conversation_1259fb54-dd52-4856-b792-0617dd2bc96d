// Define the types of routes in the application
export enum RouteType {
  // Routes that require authentication (non-anonymous)
  PROTECTED = "protected",

  // Routes that are accessible to everyone, but redirect authenticated users to dashboard
  PUBLIC = "public",

  // Routes that are accessible to everyone, including anonymous users
  ANONYMOUS = "anonymous",

  // Routes that are accessible to nannies (their own profile)
  NANNY_PROFILE = "my-profile",
}

// Define the route configuration type
export interface RouteConfig {
  path: string;
  type: RouteType;
  // Optional redirect path if access is denied
  redirectTo?: string;
}

// Define the route configurations
export const routes: RouteConfig[] = [
  // Public routes (redirect authenticated users to dashboard)
  { path: "/", type: RouteType.PUBLIC, redirectTo: "/dashboard" },

  // Anonymous routes (accessible to everyone)
  { path: "/candidate-form", type: RouteType.ANONYMOUS },
  { path: "/nanny-request-form", type: RouteType.ANONYMOUS },

  // Protected routes (require authentication)
  { path: "/dashboard", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/crm", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/crm/client/:id", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/crm/contact/:id", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/deals", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/deals/:id", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/tutors", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/candidates", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/nannies", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/nannies/:id", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/my-profile/:id", type: RouteType.NANNY_PROFILE, redirectTo: "/" },
  { path: "/candidates/:id", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/connect", type: RouteType.PROTECTED, redirectTo: "/" },
  { path: "/settings", type: RouteType.PROTECTED, redirectTo: "/" },
];

/**
 * Get the route configuration for a given path
 * @param path The current path
 * @returns The route configuration or undefined if not found
 */
export const getRouteConfig = (path: string): RouteConfig | undefined => {
  // First try to find an exact match
  let route = routes.find((r) => r.path === path);

  if (route) {
    return route;
  }

  // If no exact match, try to match patterns with parameters
  // For example, "/crm/client/123" should match "/crm/client/:id"
  return routes.find((r) => {
    // Convert route path to regex pattern
    // Replace :param with a regex pattern that matches any character except /
    const pattern = r.path.replace(/:[^/]+/g, "[^/]+").replace(/\//g, "\\/");

    const regex = new RegExp(`^${pattern}$`);
    return regex.test(path);
  });
};

/**
 * Check if a route is accessible based on auth state
 * @param routeType The type of route
 * @param isAuthenticated Whether the user is authenticated
 * @param isAnonymous Whether the user is anonymous
 * @returns Whether the route is accessible
 */
export const isRouteAccessible = (
  routeType: RouteType,
  isAuthenticated: boolean,
  isAnonymous: boolean,
  path: string,
  isNanny: boolean
): boolean => {
  switch (routeType) {
    case RouteType.NANNY_PROFILE:
      // For nannies, they can only access their own profile (handled in RouteGuard)
      // For non-nanny authenticated users, they can access any nanny profile
      return isNanny;

    case RouteType.PROTECTED:
      // Protected routes require authentication and non-anonymous users
      // Anonymous users should never access protected routes
      // Allow all authenticated non-anonymous users to access protected routes
      return isAuthenticated && !isAnonymous && !isNanny;

    case RouteType.PUBLIC:
      // Public routes are accessible to unauthenticated users and anonymous users
      // Authenticated non-anonymous users will be redirected (handled in the route guard)
      return !isAuthenticated || isAnonymous;

    case RouteType.ANONYMOUS:
      // Anonymous routes are accessible to everyone (unauthenticated, anonymous, and authenticated)
      return true;

    default:
      return false;
  }
};
