/**
 * This file defines the permission requirements for each route in the application.
 * It serves as a centralized place to configure which routes require specific permissions
 * and which routes are accessible to all authenticated users.
 */

// Define the types of route permission requirements
export enum RoutePermissionType {
  // Routes that require specific permissions to access
  REQUIRES_PERMISSION = "requires_permission",

  // Routes that are accessible to all authenticated users without specific permissions
  ALL_AUTHENTICATED = "all_authenticated",

  // Routes that are accessible to everyone (anonymous and authenticated)
  PUBLIC = "public",
}

// Interface for route permission configuration
export interface RoutePermissionConfig {
  // The route path (should match the path in routeConfig.ts)
  path: string;

  // The permission type for this route
  type: RoutePermissionType;

  // The permission page name required (only for REQUIRES_PERMISSION type)
  // This should match the page name in userService.ts PERMISSION_MAPPING
  permissionPage?: string;

  // Display name for the route (used in navigation)
  label: string;

  // Icon name or identifier (optional)
  icon?: string;
}

/**
 * The centralized route permission configuration
 * This defines which routes require specific permissions and which are accessible to all authenticated users
 */
export const routePermissions: RoutePermissionConfig[] = [
  // Dashboard route - accessible to all authenticated users
  {
    path: "/dashboard",
    type: RoutePermissionType.ALL_AUTHENTICATED,
    label: "Dashboard",
  },

  // CRM route - requires CRM view permission
  {
    path: "/crm",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "crm",
    label: "CRM",
  },
  // Deals route - requires deals view permission
  {
    path: "/deals",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "deals",
    label: "Deals",
  },

  // Candidates route - requires candidates view permission
  {
    path: "/candidates",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "candidates",
    label: "Candidates",
  },
  // Nannies route - requires nannies view permission
  {
    path: "/nannies",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "nannies",
    label: "Nannies",
  },

  // Tutors route - requires tutors view permission
  {
    path: "/tutors",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "tutors",
    label: "Tutors/Activities",
  },

  // Connect route - requires dashboard view permission
  {
    path: "/connect",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "connect",
    label: "Connect",
  },

  // Settings route - accessible to all authenticated users
  {
    path: "/settings",
    type: RoutePermissionType.ALL_AUTHENTICATED,
    label: "Settings",
  },
  {
    path: "/candidates/:id",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "candidates",
    label: "Candidate Profile",
  },
  // Profile routes - accessible to all authenticated users
  {
    path: "/nannies/:id",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "nannies",
    label: "Nanny Profile",
  },

  {
    path: "/tutors/:id",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "tutors",
    label: "Tutor Profile",
  },
  {
    path: "/crm/client/:id",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "crm",
    label: "Client Profile",
  },
  {
    path: "/crm/contact/:id",
    type: RoutePermissionType.REQUIRES_PERMISSION,
    permissionPage: "crm",
    label: "Contact Profile",
  },

  // My profile route - special case for nannies
  {
    path: "/my-profile/:id",
    type: RoutePermissionType.ALL_AUTHENTICATED,
    label: "My Profile",
  },

  // Forms - public routes
  {
    path: "/candidate-form",
    type: RoutePermissionType.PUBLIC,
    label: "Candidate Form",
  },
  {
    path: "/nanny-request-form",
    type: RoutePermissionType.PUBLIC,
    label: "Nanny Request Form",
  },
];

/**
 * Get the permission configuration for a given path
 * @param path The current path
 * @returns The route permission configuration or undefined if not found
 */
export const getRoutePermission = (
  path: string
): RoutePermissionConfig | undefined => {
  // First try to find an exact match
  let routePermission = routePermissions.find((r) => r.path === path);

  if (routePermission) {
    return routePermission;
  }

  // If no exact match, try to split the path and find a match
  const pathSegments = path.split("/");
  for (let i = 1; i < pathSegments.length; i++) {
    const partialPath = pathSegments.slice(0, i + 1).join("/");
    routePermission = routePermissions.find((r) => r.path === partialPath);
    if (routePermission) {
      console.debug("Found route permission for partial path:", partialPath);
      return routePermission;
    }
  }

  routePermission = routePermissions.find((r) => {
    // Convert route path to regex pattern
    // Replace :param with a regex pattern that matches any character except /
    const pattern = r.path.replace(/:[^/]+/g, "[^/]+").replace(/\//g, "\\/");

    const regex = new RegExp(`^${pattern}$`);
    return regex.test(path);
  });

  return routePermission;
};

/**
 * Get all navigation items that should be displayed in the header/sidebar
 * based on the user's permissions
 *
 * @param hasPagePermission Function to check if user has permission for a page
 * @returns Array of route permission configs that should be displayed
 */
export const getNavigationItems = (
  hasPagePermission: (
    page: string,
    action: "view" | "create" | "edit" | "delete"
  ) => boolean
): RoutePermissionConfig[] => {
  return routePermissions.filter((route) => {
    // Only include routes that should appear in navigation (exclude profile routes)
    if (route.path.includes("/:id")) {
      return false;
    }

    // Check if user has permission to access this route
    if (
      route.type === RoutePermissionType.REQUIRES_PERMISSION &&
      route.permissionPage
    ) {
      return hasPagePermission(route.permissionPage, "view");
    }

    // All authenticated routes are always included
    return route.type === RoutePermissionType.ALL_AUTHENTICATED;
  });
};

/**
 * Check if the user has permission to create on the current route
 *
 * @param path The current path
 * @param hasPagePermission Function to check if user has permission for a page
 * @returns Whether the user has create permission for the current route
 */
export const canCreateOnRoute = (
  path: string,
  hasPagePermission: (
    page: string,
    action: "view" | "create" | "edit" | "delete"
  ) => boolean
): boolean => {
  const routePermission = getRoutePermission(path);

  // If no route permission found or not a permission-restricted route, return false
  if (
    !routePermission ||
    routePermission.type !== RoutePermissionType.REQUIRES_PERMISSION ||
    !routePermission.permissionPage
  ) {
    return false;
  }

  // Check if user has create permission for this page
  return hasPagePermission(routePermission.permissionPage, "create");
};

/**
 * Check if the user has permission to edit on the current route
 *
 * @param path The current path
 * @param hasPagePermission Function to check if user has permission for a page
 * @returns Whether the user has edit permission for the current route
 */
export const canEditOnRoute = (
  path: string,
  hasPagePermission: (
    page: string,
    action: "view" | "create" | "edit" | "delete"
  ) => boolean
): boolean => {
  const routePermission = getRoutePermission(path);
  console.log("routePermission", routePermission);
  // If no route permission found or not a permission-restricted route, return false
  if (
    !routePermission ||
    routePermission.type !== RoutePermissionType.REQUIRES_PERMISSION ||
    !routePermission.permissionPage
  ) {
    return false;
  }

  // Check if user has edit permission for this page
  const hasEditPermission = hasPagePermission(
    routePermission.permissionPage,
    "edit"
  );
  console.debug(
    "hasEditPermission for " + routePermission.permissionPage,
    hasEditPermission
  );
  return hasEditPermission;
};

/**
 * Check if the user has permission to delete on the current route
 *
 * @param path The current path
 * @param hasPagePermission Function to check if user has permission for a page
 * @returns Whether the user has delete permission for the current route
 */
export const canDeleteOnRoute = (
  path: string,
  hasPagePermission: (
    page: string,
    action: "view" | "create" | "edit" | "delete"
  ) => boolean
): boolean => {
  const routePermission = getRoutePermission(path);

  // If no route permission found or not a permission-restricted route, return false
  if (
    !routePermission ||
    routePermission.type !== RoutePermissionType.REQUIRES_PERMISSION ||
    !routePermission.permissionPage
  ) {
    return false;
  }

  // Check if user has delete permission for this page
  return hasPagePermission(routePermission.permissionPage, "delete");
};
