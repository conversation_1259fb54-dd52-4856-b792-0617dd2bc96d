// Static data for forms and filters

// City type definition
export interface City {
  id: string;
  labelEl: string;
  labelEn: string;
  country?: string;
  created_at?: string;
}

// Cities array that will be populated from the API
// Initially set to the default cities, but will be updated when the API data is fetched
export let cities: City[] = [];

// Languages with proficiency levels
export const languages = [
  {
    id: "sign_language_gr",
    labelEl: "Νοηματική Γλώσσα (Ελληνικά)",
    labelEn: "Sign Language (Greek)",
  },
  {
    id: "sign_language_en",
    labelEl: "Νοηματική Γλώσσα (Αγγλικά)",
    labelEn: "Sign Language (English)",
  },
  {
    id: "sign_language_intl",
    labelEl: "Νοηματική Γλώσσα (Διεθνής)",
    labelEn: "Sign Language (International)",
  },
  {
    id: "braille_system_gr",
    labelEl: "Γρα<PERSON><PERSON> (Ελληνικά)",
    labelEn: "Braille System (Greek)",
  },
  {
    id: "braille_system_en",
    labelEl: "Γρα<PERSON><PERSON> Braille (Αγγλικά)",
    labelEn: "Braille System (English)",
  },
  { id: "greek", labelEl: "Ελληνικά", labelEn: "Greek" },
  { id: "english", labelEl: "Αγγλικά", labelEn: "English" },
  { id: "french", labelEl: "Γαλλικά", labelEn: "French" },
  { id: "german", labelEl: "Γερμανικά", labelEn: "German" },
  { id: "italian", labelEl: "Ιταλικά", labelEn: "Italian" },
  { id: "spanish", labelEl: "Ισπανικά", labelEn: "Spanish" },
  { id: "russian", labelEl: "Ρωσικά", labelEn: "Russian" },
  { id: "turkish", labelEl: "Τουρκικά", labelEn: "Turkish" },
  { id: "arabic", labelEl: "Αραβικά", labelEn: "Arabic" },
  { id: "chinese", labelEl: "Κινέζικα", labelEn: "Chinese" },
  { id: "albanian", labelEl: "Αλβανικά", labelEn: "Albanian" },
  { id: "bulgarian", labelEl: "Βουλγαρικά", labelEn: "Bulgarian" },
  { id: "romanian", labelEl: "Ρουμανικά", labelEn: "Romanian" },
  { id: "serbian", labelEl: "Σερβικά", labelEn: "Serbian" },
  { id: "ukrainian", labelEl: "Ουκρανικά", labelEn: "Ukrainian" },
];

// Language proficiency levels
export const languageLevels = [
  { id: "native", labelEl: "Μητρική", labelEn: "Native" },
  { id: "fluent", labelEl: "Άριστα", labelEn: "Fluent" },
  { id: "advanced", labelEl: "Προχωρημένο", labelEn: "Advanced" },
  { id: "intermediate", labelEl: "Μέτριο", labelEn: "Intermediate" },
  { id: "basic", labelEl: "Βασικό", labelEn: "Basic" },
];

// Helper function to get label based on language
export const getLocalizedLabel = (
  item: { labelEl: string; labelEn: string },
  language: "el" | "en"
): string => {
  return language === "el" ? item.labelEl : item.labelEn;
};

// Update cities array with data from the API
export const updateCities = (newCities: City[]): void => {
  cities = [...newCities];
};
