import {
  candidateTypeOptions,
  childrenAgeOptions,
  durationOptions,
  educationOptions,
  positionOptions,
  scheduleOptions,
  specializationOptions,
} from "../schemas/FormSchema";
import { cities, languages } from "./staticData";

// Generic function to get options from any option array
type OptionItem =
  | { id: string; label: string }
  | { id: string; labelEl: string; labelEn: string };

/**
 * Get options for table filters from any option array
 * @param options The options array
 * @param language The current language (el or en)
 * @returns Array of option values in the specified language
 */
export const getOptionsFromArray = (
  options: OptionItem[],
  language: "el" | "en" = "el"
): string[] => {
  return options.map((option) => {
    if ("labelEl" in option && "labelEn" in option) {
      return language === "el" ? option.labelEl : option.labelEn;
    } else if ("label" in option) {
      return option.label;
    }
    return String(option);
  });
};

/**
 * Get label from ID for any option array
 * @param options The options array
 * @param id The option ID
 * @param language The current language (el or en)
 * @returns The label for the given ID
 */
export const getLabelFromId = (
  options: OptionItem[],
  id: string,
  language: "el" | "en" = "el"
): string => {
  const option = options.find((o) => o.id === id);
  if (!option) return id; // Return the ID if not found

  if ("labelEl" in option && "labelEn" in option) {
    return language === "el" ? option.labelEl : option.labelEn;
  } else if ("label" in option) {
    return option.label;
  }
  return id;
};

// Specific helper functions for each option type

/**
 * Get city options for table filters
 * @param language The current language (el or en)
 * @returns Array of city names in the specified language
 */
export const getCityOptions = (language: "el" | "en" = "el"): string[] => {
  return getOptionsFromArray(cities, language);
};

/**
 * Get language options for table filters
 * @param language The current language (el or en)
 * @returns Array of language names in the specified language
 */
export const getLanguageOptions = (language: "el" | "en" = "el"): string[] => {
  return getOptionsFromArray(languages, language);
};

/**
 * Get position options for table filters
 * @returns Array of position names
 */
export const getPositionOptions = (): string[] => {
  return getOptionsFromArray(positionOptions);
};

export const getScheduleOptions = (): string[] => {
  return getOptionsFromArray(scheduleOptions);
};

export const getDurationInterestsOptions = (): string[] => {
  return getOptionsFromArray(durationOptions);
};

/**
 * Get education options for table filters
 * @returns Array of education names
 */
export const getEducationOptions = (): string[] => {
  return getOptionsFromArray(educationOptions);
};

/**
 * Get specialization options for table filters
 * @returns Array of specialization names
 */
export const getSpecializationOptions = (): string[] => {
  return getOptionsFromArray(specializationOptions);
};

/**
 * Get candidate type options for table filters
 * @returns Array of candidate type names
 */
export const getCandidateTypeOptions = (): string[] => {
  return getOptionsFromArray(candidateTypeOptions);
};

/**
 * Get children age options for table filters
 * @returns Array of children age names
 */
export const getChildrenAgeOptions = (
  language: "el" | "en" = "el"
): string[] => {
  return getOptionsFromArray(childrenAgeOptions, language);
};

/**
 * Convert city ID to localized label
 * @param cityId The city ID
 * @param language The current language (el or en)
 * @returns The localized city name
 */
export const getCityLabel = (
  cityId: string,
  language: "el" | "en" = "el"
): string => {
  return getLabelFromId(cities, cityId, language);
};

/**
 * Convert language ID to localized label
 * @param languageId The language ID
 * @param language The current language (el or en)
 * @returns The localized language name
 */
export const getLanguageLabel = (
  languageId: string,
  language: "el" | "en" = "el"
): string => {
  return getLabelFromId(languages, languageId, language);
};

/**
 * Convert position ID to label
 * @param positionId The position ID
 * @returns The position name
 */
export const getPositionLabel = (positionId: string): string => {
  return getLabelFromId(positionOptions, positionId);
};

/**
 * Convert education ID to label
 * @param educationId The education ID
 * @returns The education name
 */
export const getEducationLabel = (educationId: string): string => {
  return getLabelFromId(educationOptions, educationId);
};

/**
 * Convert specialization ID to label
 * @param specializationId The specialization ID
 * @returns The specialization name
 */
export const getSpecializationLabel = (specializationId: string): string => {
  return getLabelFromId(specializationOptions, specializationId);
};

/**
 * Convert candidate type ID to label
 * @param candidateTypeId The candidate type ID
 * @returns The candidate type name
 */
export const getCandidateTypeLabel = (candidateTypeId: string): string => {
  return getLabelFromId(candidateTypeOptions, candidateTypeId);
};
