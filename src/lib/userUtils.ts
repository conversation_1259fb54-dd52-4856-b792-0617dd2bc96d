import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/contexts/PermissionsContext";

/**
 * Custom hook that provides a function to reload the user and refresh permissions
 * This is useful when app_metadata changes, such as when permissions are updated
 * 
 * @returns A function that reloads the user and refreshes permissions
 */
export const useReloadUserAndPermissions = () => {
  const { reloadUser } = useAuth();
  const { refreshPermissions } = usePermissions();

  /**
   * Reloads the user and refreshes permissions
   * This ensures that any changes to app_metadata are reflected in the UI
   * 
   * @returns A promise that resolves when the user and permissions have been reloaded
   */
  const reloadUserAndPermissions = async (): Promise<{ success: boolean; error: any | null }> => {
    try {
      // First reload the user to get updated app_metadata
      const { error: userError } = await reloadUser();
      
      if (userError) {
        console.error("Error reloading user:", userError);
        return { success: false, error: userError };
      }
      
      // Then refresh permissions based on the updated app_metadata
      await refreshPermissions();
      
      return { success: true, error: null };
    } catch (error) {
      console.error("Error reloading user and permissions:", error);
      return { success: false, error };
    }
  };

  return reloadUserAndPermissions;
};

/**
 * Utility function to check if a user has a specific role
 * 
 * @param user The user object from Supabase Auth
 * @param role The role to check for
 * @returns Whether the user has the specified role
 */
export const hasRole = (
  user: any,
  role: "admin" | "staff" | "nanny" | "both"
): boolean => {
  if (!user || !user.app_metadata) {
    return false;
  }

  return user.app_metadata.role === role;
};

/**
 * Utility function to check if a user is a nanny
 * 
 * @param user The user object from Supabase Auth
 * @returns Whether the user is a nanny
 */
export const isNanny = (user: any): boolean => {
  if (!user || !user.app_metadata) {
    return false;
  }

  return (
    user.app_metadata.role === "nanny" || 
    user.app_metadata.role === "both"
  );
};

/**
 * Utility function to check if a user is an admin
 * 
 * @param user The user object from Supabase Auth
 * @returns Whether the user is an admin
 */
export const isAdmin = (user: any): boolean => {
  if (!user || !user.app_metadata) {
    return false;
  }

  return user.app_metadata.role === "admin";
};

/**
 * Utility function to check if a user is a staff member
 * 
 * @param user The user object from Supabase Auth
 * @returns Whether the user is a staff member
 */
export const isStaff = (user: any): boolean => {
  if (!user || !user.app_metadata) {
    return false;
  }

  return user.app_metadata.role === "staff";
};
