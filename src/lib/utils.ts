import { type ClassValue, clsx } from "clsx";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { supabase } from "./supabase";

/**
 * Merges class values using clsx and tailwind-merge
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Uploads a file to Supabase storage
 *
 * @param file - The file to upload
 * @param bucket - The storage bucket name
 * @param path - The path within the bucket (including filename)
 * @param onProgress - Optional callback for upload progress (0-100)
 * @returns - The storage path of the uploaded file (not the public URL)
 */
export const uploadFileToSupabase = async (
  file: File,
  bucketName: string,
  path: string,
  onProgress?: ((progress: number) => void) | boolean
): Promise<string> => {
  // Create a local variable that can be modified
  let bucket = bucketName;

  // Handle the case where onProgress is a function
  const progressCallback =
    typeof onProgress === "function" ? onProgress : undefined;

  try {
    // Upload the file
    const { error, data: uploadData } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: "3600",
        contentType: file.type,
      });

    // Call progress callback with 100% when done
    if (progressCallback) {
      progressCallback(100);
    }

    if (error) {
      if (error.message === "File already exists") {
        console.error("File already exists, skipping upload");
        return path;
      }

      console.error("Supabase upload error:", error);
      throw error;
    }

    // Return the storage path, not the public URL
    return path;
  } catch (error) {
    console.error("Error uploading file:", error);
    throw error;
  }
};

/**
 * Uploads multiple files to Supabase storage
 *
 * @param files - Array of files to upload
 * @param bucket - The storage bucket name
 * @param basePath - The base path within the bucket (folder path)
 * @param onProgress - Optional callback for overall upload progress (0-100)
 * @returns - Array of storage paths of the uploaded files (not public URLs)
 */
export const uploadMultipleFiles = async (
  files: File[] | FileList,
  bucket: string,
  basePath: string,
  onProgress?: (progress: number) => void
): Promise<string[]> => {
  if (!files || files.length === 0) {
    console.log("No files to upload");
    return [];
  }

  // Convert FileList to array if needed
  const fileArray = Array.isArray(files) ? files : Array.from(files);
  const totalFiles = fileArray.length;

  const storagePaths: string[] = [];

  for (let i = 0; i < totalFiles; i++) {
    const file = fileArray[i];
    const path = `${basePath}/${i + 1}_${Date.now()}`;

    const fileProgress = (progress: number) => {
      if (onProgress) {
        // Calculate overall progress: completed files + current file progress
        const overallProgress = Math.round(
          (i / totalFiles + progress / 100 / totalFiles) * 100
        );
        onProgress(overallProgress);
      }
    };

    try {
      const storagePath = await uploadFileToSupabase(
        file,
        bucket,
        path,
        fileProgress
      );
      storagePaths.push(storagePath);
    } catch (error) {
      console.error(`Error uploading file ${i + 1}/${totalFiles}:`, error);
      throw error;
    }

    // Update overall progress after each file completes
    if (onProgress) {
      onProgress(Math.round(((i + 1) / totalFiles) * 100));
    }
  }

  return storagePaths;
};

// Function to convert file input path to File object
export const getFileFromInput = async (
  fileInput: HTMLInputElement
): Promise<File | null> => {
  if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
    return null;
  }
  return fileInput.files[0];
};

// Function to handle multiple files from an input
export const getFilesFromInput = async (
  fileInput: HTMLInputElement
): Promise<File[]> => {
  if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
    return [];
  }
  console.debug("Files from input:", fileInput.files);
  return Array.from(fileInput.files);
};

/**
 * Converts object keys from camelCase to snake_case
 * @param obj - The object with camelCase keys
 * @returns A new object with snake_case keys
 */
export const toSnakeCase = (obj: Record<string, any>): Record<string, any> => {
  if (!obj || typeof obj !== "object" || Array.isArray(obj)) return obj;

  return Object.keys(obj).reduce((result, key) => {
    const snakeKey = key.replace(
      /[A-Z]/g,
      (letter) => `_${letter.toLowerCase()}`
    );
    const value = obj[key];

    // Recursively convert nested objects
    result[snakeKey] =
      value && typeof value === "object" && !Array.isArray(value)
        ? toSnakeCase(value)
        : value;

    return result;
  }, {} as Record<string, any>);
};

/**
 * Creates a debounced function that delays invoking the provided function
 * until after the specified wait time has elapsed since the last time it was invoked.
 *
 * @param func - The function to debounce
 * @param wait - The number of milliseconds to delay
 * @returns A debounced version of the provided function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function (...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout !== null) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(later, wait);
  };
};

/**
 * A React hook that returns a debounced version of the provided value.
 * The debounced value will only update after the specified delay has passed
 * without any new updates to the original value.
 *
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Gets a download URL for a file from Supabase storage
 *
 * @param bucket - The storage bucket name
 * @param path - The path within the bucket (including filename)
 * @returns - The download URL for the file
 */
export const getFileDownloadUrl = async (
  bucket: string,
  path: string
): Promise<string> => {
  try {
    const { data } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, 60);

    if (!data?.signedUrl) {
      throw new Error("Failed to create signed URL");
    }

    return data.signedUrl;
  } catch (error) {
    console.error("Error getting file download URL:", error);
    throw error;
  }
};

/**
 * Gets a preview URL for a file from Supabase storage
 * Uses signed URLs for private buckets and public URLs for public buckets
 * Adds special handling for PDF files to ensure they display in the browser
 *
 * @param bucket - The storage bucket name
 * @param path - The path within the bucket (including filename)
 * @param expiresIn - Expiration time in seconds for signed URLs (default: 3600 - 1 hour)
 * @returns - Promise that resolves to the URL for the file
 */
export const getFilePreviewUrl = async (
  bucket: string,
  path: string,
  expiresIn: number = 3600
): Promise<string> => {
  try {
    // Check if the file is a PDF
    const isPdf = path.toLowerCase().endsWith(".pdf");

    // For PDFs, we want to ensure they display in the browser
    // Setting download=false helps with this
    const options = isPdf
      ? {
          download: false,
        }
      : undefined;

    // First try to get a signed URL (works for both public and private buckets)
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn, options);

    if (error) {
      console.error("Error creating signed URL:", error);
      // Fallback to public URL if signed URL fails
      const { data: publicData } = supabase.storage
        .from(bucket)
        .getPublicUrl(path);
      return publicData.publicUrl;
    }

    // For PDFs, ensure the URL has the right parameters for browser display
    if (isPdf && data.signedUrl) {
      // Don't add parameters if they're already there
      if (!data.signedUrl.includes("#")) {
        return `${data.signedUrl}`;
      }
    }

    return data.signedUrl;
  } catch (error) {
    console.error("Error getting file preview URL:", error);
    throw error;
  }
};

/**
 * Lists all files in a Supabase storage bucket with a given prefix
 *
 * @param bucket - The storage bucket name
 * @param prefix - The prefix/folder path to list files from
 * @returns - Array of file objects with name, size, etc.
 */
export const listFilesInBucket = async (
  bucket: string,
  prefix: string
): Promise<any[]> => {
  try {
    const { data, error } = await supabase.storage.from(bucket).list(prefix);

    if (error) {
      console.error("Error listing files:", error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error("Error listing files in bucket:", error);
    throw error;
  }
};

/**
 * Gets a file from Supabase storage with automatic retry if URL has expired
 *
 * @param bucket - The storage bucket name
 * @param path - The path within the bucket (including filename)
 * @param expiresIn - Expiration time in seconds for signed URLs (default: 3600 - 1 hour)
 * @param onSuccess - Callback function to execute with the URL
 * @param onError - Callback function to execute if an error occurs
 */
export const getFileWithRetry = async (
  bucket: string,
  path: string,
  expiresIn: number = 3600,
  onSuccess: (url: string) => void,
  onError: (error: any) => void
): Promise<void> => {
  try {
    // If it's already a full URL, use it directly
    if (path.startsWith("http")) {
      onSuccess(path);
      return;
    }

    // Get a signed URL
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn);

    if (error) {
      // If there's an error and it's related to expiration, try again
      if (
        error.message?.includes("expired") ||
        (error as any).statusCode === 401
      ) {
        console.error("URL expired, retrying with a new signed URL");
        // Try again with a longer expiration
        const { data: newData, error: newError } = await supabase.storage
          .from(bucket)
          .createSignedUrl(path, expiresIn * 2);

        if (newError) {
          throw newError;
        }

        onSuccess(newData.signedUrl);
        return;
      }

      throw error;
    }

    onSuccess(data.signedUrl);
  } catch (error) {
    console.error("Error accessing file:", error);
    onError(error);
  }
};
