import { PaginatedResponse, PaginationParams } from "@/lib/paginationUtils";
import { supabase } from "@/lib/supabase";
import { fetchUsers, User } from "./userService";

// Interface for raw audit log data from the database
export interface AuditLogEntry {
  id: number;
  table_name: string;
  operation: string;
  performed_by: string | null; // UUID
  changed_data: any;
  performed_at: string;
}

// Interface for processed activity data to be displayed in the UI
export interface Activity {
  id: string;
  user: {
    name: string;
    email: string;
    avatar: string;
  };
  action: string;
  timestamp: string;
  table_name: string;
  operation: string;
}

// Cache for users to avoid multiple requests
let usersCache: User[] = [];

/**
 * Fetches recent activities from the audit_log table with pagination and filtering
 * @param pagination Optional pagination parameters with filters
 * @returns Paginated response of activities
 */
export const fetchActivities = async (
  pagination?: PaginationParams
): Promise<PaginatedResponse<Activity>> => {
  try {
    // Fetch users first if not cached
    if (usersCache.length === 0) {
      usersCache = await fetchUsers();
    }

    // Create the base query
    let query = supabase
      .from("audit_log")
      .select("*", { count: "exact" })
      .order("performed_at", { ascending: false });

    // Apply filters if provided
    if (pagination?.filters) {
      // Filter by table name
      if (pagination.filters.table_name) {
        query = query.eq("table_name", pagination.filters.table_name);
      }

      // Filter by operation
      if (pagination.filters.operation) {
        query = query.eq("operation", pagination.filters.operation);
      }

      // Filter by user
      if (pagination.filters.performed_by) {
        query = query.eq("performed_by", pagination.filters.performed_by);
      }

      // Filter by date range
      if (
        pagination.filters.date_range &&
        Array.isArray(pagination.filters.date_range)
      ) {
        const [startDate, endDate] = pagination.filters.date_range;
        if (startDate) {
          query = query.gte("performed_at", startDate);
        }
        if (endDate) {
          query = query.lte("performed_at", endDate);
        }
      }
    }

    // Apply pagination
    if (pagination) {
      const from = (pagination.page - 1) * pagination.itemsPerPage;
      const to = from + pagination.itemsPerPage - 1;
      query = query.range(from, to);
    }

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      throw new Error(error.message);
    }

    // Process the audit log entries into activities
    const activities: Activity[] =
      data?.map((entry: AuditLogEntry) => {
        return processAuditLogEntry(entry, usersCache);
      }) || [];

    // Return paginated response
    return {
      data: activities,
      totalCount: count || 0,
      page: pagination?.page || 1,
      itemsPerPage: pagination?.itemsPerPage || 10,
      totalPages: count
        ? Math.ceil(count / (pagination?.itemsPerPage || 10))
        : 0,
    };
  } catch (error) {
    console.error("Error fetching activities:", error);
    return {
      data: [],
      totalCount: 0,
      page: pagination?.page || 1,
      itemsPerPage: pagination?.itemsPerPage || 10,
      totalPages: 0,
    };
  }
};

/**
 * Fetches recent activities from the audit_log table
 * @param limit Number of activities to fetch
 * @returns Array of processed activities
 */
export const fetchRecentActivities = async (limit = 5): Promise<Activity[]> => {
  try {
    const result = await fetchActivities({
      page: 1,
      itemsPerPage: limit,
    });
    return result.data;
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    return [];
  }
};

/**
 * Processes an audit log entry into an activity
 * @param entry The audit log entry
 * @param users Array of users for mapping UUIDs to user info
 * @returns Processed activity
 */
const processAuditLogEntry = (
  entry: AuditLogEntry,
  users: User[]
): Activity => {
  // Find the user who performed the action
  const user = users.find((u) => u.supabase_uuid === entry.performed_by);

  // Generate a user-friendly action description
  const action = generateActionDescription(entry);

  return {
    id: entry.id.toString(),
    user: {
      name: user ? `${user.first_name} ${user.last_name}` : "Άγνωστος Χρήστης",
      email: user ? user.email : "<EMAIL>",
      avatar: user && user.first_name ? user.first_name[0] : "U",
    },
    action,
    timestamp: entry.performed_at,
    table_name: entry.table_name,
    operation: entry.operation,
  };
};

/**
 * Generates a user-friendly description of the action
 * @param entry The audit log entry
 * @returns User-friendly action description
 */
const generateActionDescription = (entry: AuditLogEntry): string => {
  const { table_name, operation, changed_data } = entry;

  // Map table names to more user-friendly names in Greek
  const tableNameMap: Record<string, string> = {
    clients: "Πελάτη",
    candidates: "Υποψηφίου",
    deals: "Συμφωνίας",
    users: "Χρήστη",
    client_notes: "Σημείωση Πελάτη",
    candidate_notes: "Σημείωση Υποψηφίου",
  };

  // Map operations to more user-friendly verbs in Greek
  const operationMap: Record<string, string> = {
    INSERT: "Προσθήκη",
    UPDATE: "Επεξεργασία",
    DELETE: "Διαγραφή",
  };

  // Get the friendly table name or use the original
  const friendlyTableName = tableNameMap[table_name] || table_name;

  // Get the friendly operation or use the original
  const friendlyOperation = operationMap[operation] || operation;

  // Try to extract an ID or name from the changed data
  let identifier = "";
  if (changed_data) {
    if (changed_data.id) {
      identifier = changed_data.id;
    } else if (changed_data.form_data && changed_data.form_data.name) {
      identifier = `${changed_data.form_data.name} ${
        changed_data.form_data.surname || ""
      }`;
    }
  }

  // Format the identifier with the appropriate prefix based on the table
  if (identifier && table_name === "clients") {
    identifier = `CL-${identifier}`;
  } else if (identifier && table_name === "candidates") {
    identifier = `CAN-${identifier}`;
  } else if (identifier && table_name === "deals") {
    identifier = `DEAL-${identifier}`;
  }

  // Construct the action description
  return `${friendlyOperation} ${friendlyTableName}${
    identifier ? ` ${identifier}` : ""
  }`;
};
