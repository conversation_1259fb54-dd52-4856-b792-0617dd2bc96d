import { supabase } from "@/lib/supabase";
import { fetchCities } from "./cityService";

// Analytics data interfaces
export interface TopClientData {
  name: string;
  value: number;
  activeClients: number;
  pastClients: number;
  leads: number;
  rejected: number;
  totalRevenue: number;
  avgRevenue: number;
  topCity?: string;
  topCityCount?: number;
}

export interface CityData {
  name: string;
  value: number;
}

export interface ClientLeadData {
  name: string;
  value: number;
}

export interface MonthlyData {
  month: string;
  clients: number;
  leads: number;
}

/**
 * Fetches top clients data by month for a specific year
 * @param year The year to fetch data for
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @returns Array of top client data
 */
export const fetchTopClientsData = async (
  year: string,
  forceRefresh = false
): Promise<TopClientData[]> => {
  try {
    // Create proper UTC date objects for the start and end of the year
    const startDate = new Date(Date.UTC(parseInt(year), 0, 1));
    const endDate = new Date(Date.UTC(parseInt(year), 11, 31, 23, 59, 59, 999));

    // Format dates for Supabase query (ISO format)
    const startDateStr = startDate.toISOString();
    const endDateStr = endDate.toISOString();

    // Fetch clients created in the specified year with all necessary data
    const { data, error } = await supabase
      .from("client_deals")
      .select(" status, form_data, revenue")
      .gte("deal_date", startDateStr)
      .lte("deal_date", endDateStr)
      .order("revenue", { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    // Initialize all months with data structure
    const months = [
      "Ιαν",
      "Φεβ",
      "Μαρ",
      "Απρ",
      "Μαϊ",
      "Ιουν",
      "Ιουλ",
      "Αυγ",
      "Σεπ",
      "Οκτ",
      "Νοε",
      "Δεκ",
    ];

    // Create a map to store detailed data for each month
    const monthlyDetailedData: Record<
      string,
      {
        total: number;
        activeClients: number;
        pastClients: number;
        leads: number;
        rejected: number;
        totalRevenue: number;
        cities: Record<string, number>;
      }
    > = {};

    // Initialize all months with 0 values
    months.forEach((month) => {
      monthlyDetailedData[month] = {
        total: 0,
        activeClients: 0,
        pastClients: 0,
        leads: 0,
        rejected: 0,
        totalRevenue: 0,
        cities: {},
      };
    });

    // Process client data by month
    data.forEach((client) => {
      const date = new Date();
      const monthIndex = date.getMonth();
      const monthName = months[monthIndex];

      // Increment total count
      monthlyDetailedData[monthName].total++;

      // Categorize by status
      if (client.status === 1) {
        monthlyDetailedData[monthName].leads++;
      } else if (client.status === 2) {
        monthlyDetailedData[monthName].rejected++;
      } else if (client.status === 3) {
        monthlyDetailedData[monthName].pastClients++;
      } else if (client.status === 4) {
        monthlyDetailedData[monthName].activeClients++;
      }

      // Add revenue
      if (client.revenue) {
        monthlyDetailedData[monthName].totalRevenue += client.revenue;
      }

      // Track cities
      if (client.form_data && client.form_data.city) {
        const cityId = client.form_data.city;
        monthlyDetailedData[monthName].cities[cityId] =
          (monthlyDetailedData[monthName].cities[cityId] || 0) + 1;
      }
    });

    // Get all cities for reference
    const allCities = await fetchCities();
    const cityMap = new Map(allCities.map((city) => [city.id, city.labelEl]));

    // Convert to array format for the chart with additional data
    const result: TopClientData[] = months.map((name) => {
      const monthData = monthlyDetailedData[name];

      // Find top city
      let topCity = "";
      let topCityCount = 0;

      Object.entries(monthData.cities).forEach(([cityId, count]) => {
        if (count > topCityCount) {
          // Get city name from the map
          topCity = cityMap.get(cityId) || cityId;
          topCityCount = count;
        }
      });

      // Calculate average revenue
      const clientCount = monthData.activeClients + monthData.pastClients;
      const avgRevenue =
        clientCount > 0 ? monthData.totalRevenue / clientCount : 0;

      return {
        name,
        value: monthData.total,
        activeClients: monthData.activeClients,
        pastClients: monthData.pastClients,
        leads: monthData.leads,
        rejected: monthData.rejected,
        totalRevenue: monthData.totalRevenue,
        avgRevenue: Math.round(avgRevenue * 100) / 100,
        topCity,
        topCityCount: topCityCount > 0 ? topCityCount : undefined,
      };
    });

    return result;
  } catch (error) {
    console.error("Error fetching top clients data:", error);
    return [];
  }
};

/**
 * Fetches city data for leads in a specific month and year
 * @param month The month to fetch data for
 * @param year The year to fetch data for
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @returns Array of city data
 */
export const fetchCitiesData = async (
  month: string,
  year: string,
  forceRefresh = false
): Promise<CityData[]> => {
  try {
    // Get the month index (0-11)
    const months = [
      "Ιανουάριος",
      "Φεβρουάριος",
      "Μάρτιος",
      "Απρίλιος",
      "Μάιος",
      "Ιούνιος",
      "Ιούλιος",
      "Αύγουστος",
      "Σεπτέμβριος",
      "Οκτώβριος",
      "Νοέμβριος",
      "Δεκέμβριος",
    ];
    const monthIndex = months.indexOf(month);

    if (monthIndex === -1) {
      throw new Error(`Invalid month: ${month}`);
    }

    // Create proper UTC date objects for the start and end of the month
    const startDate = new Date(Date.UTC(parseInt(year), monthIndex, 1));
    const endDate = new Date(Date.UTC(parseInt(year), monthIndex + 1, 0)); // Last day of month

    // Format dates for Supabase query (ISO format)
    const startDateStr = startDate.toISOString();
    const endDateStr = endDate.toISOString();

    // Fetch clients created in the specified month with status = 1 (Lead)
    const { data, error } = await supabase
      .from("clients")
      .select("form_data, status")
      .eq("status", 1)
      .gte("created_at", startDateStr)
      .lte("created_at", endDateStr);

    if (error) {
      throw new Error(error.message);
    }

    // Get all cities for reference
    const allCities = await fetchCities();
    const cityMap = new Map(allCities.map((city) => [city.id, city.labelEl]));

    // Group leads by city
    const cityData: Record<string, number> = {};

    // Count leads by city
    data.forEach((client) => {
      const cityId = client.form_data?.city;
      if (cityId && cityMap.has(cityId)) {
        const cityName = cityMap.get(cityId) || cityId;
        cityData[cityName] = (cityData[cityName] || 0) + 1;
      }
    });

    // Convert to array format for the chart and sort by count
    const result: CityData[] = Object.entries(cityData)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 6); // Get top 6 cities

    // If we have fewer than 6 cities, add some default ones
    if (result.length < 6) {
      const defaultCities = [
        "Αθήνα",
        "Θεσσαλονίκη",
        "Πάτρα",
        "Ηράκλειο",
        "Λάρισα",
        "Βόλος",
      ];
      defaultCities.forEach((city) => {
        if (!cityData[city] && result.length < 6) {
          result.push({ name: city, value: 0 });
        }
      });
    }

    return result;
  } catch (error) {
    console.error("Error fetching cities data:", error);
    return [];
  }
};

/**
 * Fetches client/lead ratio data for a specific quarter and year
 * @param quarter The quarter to fetch data for
 * @param year The year to fetch data for
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @returns Array of client/lead ratio data
 */
export const fetchClientLeadData = async (
  quarter: string,
  year: string,
  forceRefresh = false
): Promise<ClientLeadData[]> => {
  try {
    // Get the quarter months
    const quarters = ["1ο Τρίμηνο", "2ο Τρίμηνο", "3ο Τρίμηνο", "4ο Τρίμηνο"];
    const quarterIndex = quarters.indexOf(quarter);

    if (quarterIndex === -1) {
      throw new Error(`Invalid quarter: ${quarter}`);
    }

    // Calculate the start and end months for the quarter (0-based)
    const startMonth = quarterIndex * 3;
    const endMonth = startMonth + 2;

    // Create proper UTC date objects for the start and end of the quarter
    const startDate = new Date(Date.UTC(parseInt(year), startMonth, 1));
    const endDate = new Date(Date.UTC(parseInt(year), endMonth + 1, 0)); // Last day of end month

    // Format dates for Supabase query (ISO format)
    const startDateStr = startDate.toISOString();
    const endDateStr = endDate.toISOString();

    // Fetch clients created in the specified quarter
    const { data, error } = await supabase
      .from("clients")
      .select("status")
      .gte("created_at", startDateStr)
      .lte("created_at", endDateStr);

    if (error) {
      throw new Error(error.message);
    }

    // Count clients and leads
    let clientCount = 0;
    let leadCount = 0;

    data.forEach((client) => {
      // Status 1 = Lead, Status 3 or 4 = Client
      if (client.status === 1 || client.status === 2) {
        leadCount++;
      } else if (client.status === 3 || client.status === 4) {
        clientCount++;
      }
    });

    // Create the result array
    const result: ClientLeadData[] = [
      { name: "Πελάτες", value: clientCount },
      { name: "Υποψήφιοι", value: leadCount },
    ];

    return result;
  } catch (error) {
    console.error("Error fetching client/lead data:", error);
    return [
      { name: "Πελάτες", value: 0 },
      { name: "Υποψήφιοι", value: 0 },
    ];
  }
};

/**
 * Fetches monthly client and lead data for a specific year
 * @param year The year to fetch data for
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @returns Array of monthly data
 */
export const fetchMonthlyData = async (
  year: string,
  forceRefresh = false
): Promise<MonthlyData[]> => {
  try {
    // Create proper UTC date objects for the start and end of the year
    const startDate = new Date(Date.UTC(parseInt(year), 0, 1));
    const endDate = new Date(Date.UTC(parseInt(year), 11, 31, 23, 59, 59, 999));

    // Format dates for Supabase query (ISO format)
    const startDateStr = startDate.toISOString();
    const endDateStr = endDate.toISOString();

    // Fetch clients created in the specified year
    const { data, error } = await supabase
      .from("clients")
      .select("created_at, status")
      .gte("created_at", startDateStr)
      .lte("created_at", endDateStr)
      .order("created_at");

    if (error) {
      throw new Error(error.message);
    }

    // Initialize monthly data
    const months = [
      "Ιαν",
      "Φεβ",
      "Μαρ",
      "Απρ",
      "Μαϊ",
      "Ιουν",
      "Ιουλ",
      "Αυγ",
      "Σεπ",
      "Οκτ",
      "Νοε",
      "Δεκ",
    ];
    const monthlyData: MonthlyData[] = months.map((month) => ({
      month,
      clients: 0,
      leads: 0,
    }));

    // Count clients and leads by month
    data.forEach((client) => {
      const date = new Date(client.created_at);
      const monthIndex = date.getMonth();

      // Status 1 = Lead, Status 3 or 4 = Client
      if (client.status === 1 || client.status === 2) {
        monthlyData[monthIndex].leads++;
      } else if (client.status === 3 || client.status === 4) {
        monthlyData[monthIndex].clients++;
      }
    });

    return monthlyData;
  } catch (error) {
    console.error("Error fetching monthly data:", error);
    return [];
  }
};
