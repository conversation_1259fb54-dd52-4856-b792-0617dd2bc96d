import { Candidate } from "@/components/nannies/models/Candidate";
import client from "@/lib/api";
import {
  PaginatedResponse,
  PaginationParams,
  paginateQuery,
} from "@/lib/paginationUtils";
import { cities, languages } from "@/lib/staticData";
import { supabase } from "@/lib/supabase";
import {
  candidateTypeOptions,
  positionOptions,
  startDateOptions,
} from "@/schemas/FormSchema";

// Debounce configuration
const DEBOUNCE_DELAY = 500; // 500ms debounce delay for text-based filters
const pendingDebounces: Record<string, NodeJS.Timeout> = {};

/**
 * Debounce function for text-based filters
 * @param key The filter key
 * @param value The filter value
 * @param callback The function to call after debounce
 */
const debounceTextFilter = (
  key: string,
  value: string,
  callback: (key: string, value: string) => void
): void => {
  // Clear any existing timeout for this key
  if (pendingDebounces[key]) {
    clearTimeout(pendingDebounces[key]);
  }

  // Set a new timeout
  pendingDebounces[key] = setTimeout(() => {
    callback(key, value);
    delete pendingDebounces[key];
  }, DEBOUNCE_DELAY);
};

/**
 * Handle debounced filter updates
 * @param key The filter key
 * @param value The filter value
 * @param pagination The current pagination state
 * @param setPagination Function to update pagination state
 * @param loadData Function to load data with the updated filters
 */
export const handleDebouncedFilterUpdate = (
  key: string,
  value: string,
  _pagination: PaginationParams, // Unused but kept for API consistency
  setPagination: (
    updater: (prev: PaginationParams) => PaginationParams
  ) => void,
  loadData: (
    forceRefresh: boolean,
    pagination: PaginationParams
  ) => Promise<any>
): void => {
  // For text-based filters, use debounce
  const isTextFilter =
    key === "name" || key === "surname" || key === "address" || key === "id";

  if (isTextFilter) {
    // Apply debounce for text filters to prevent excessive requests
    debounceTextFilter(key, value, (debouncedKey, debouncedValue) => {
      console.debug(
        `Debounced filter applied: ${debouncedKey} = ${debouncedValue}`
      );

      // Update pagination with the debounced value
      setPagination((prev) => {
        const newPagination = {
          ...prev,
          filters: {
            ...prev.filters,
            [debouncedKey]: debouncedValue,
          },
        };

        // Load data with the updated filters
        loadData(true, newPagination);

        return newPagination;
      });
    });
  } else {
    // For non-text filters, update immediately
    setPagination((prev) => {
      const newPagination = {
        ...prev,
        filters: {
          ...prev.filters,
          [key]: value,
        },
      };

      // Load data with the updated filters
      loadData(true, newPagination);

      return newPagination;
    });
  }
};

// Re-export Candidate type for convenience
export type { Candidate };

/**
 * Apply filters to a Supabase query based on pagination filters
 * @param query The Supabase query to apply filters to
 * @param pagination Pagination parameters with filters
 * @returns The modified query with filters applied
 */
const applyFilters = (query: any, pagination?: PaginationParams): any => {
  if (!pagination?.filters) return query;

  console.debug("Filter keys:", Object.keys(pagination.filters));

  // Track if we're using debounced text filters
  let usingDebouncedFilters = false;

  // Apply each filter
  Object.entries(pagination.filters).forEach(([key, value]) => {
    try {
      if (value && value !== "Όλα" && value !== "") {
        console.debug(`Applying filter: ${key} = ${value}`);

        // Check if this is a text-based filter that should be debounced
        const isTextFilter =
          key === "form_data.name" ||
          key === "form_data.surname" ||
          key === "form_data.address" ||
          key === "form_data.candidate_search" ||
          key === "id";

        if (isTextFilter) {
          // For text-based filters, we'll use debounce
          usingDebouncedFilters = true;

          if (key === "id") {
            // Extract numeric ID from format like "NANNY-123" or "TUTOR-123"
            const numericId = String(value).replace(/\D/g, "");
            if (numericId) {
              // Use ilike for partial matches
              query = query.eq("id", numericId);
            } else {
              // If we couldn't extract a numeric ID, search in the original ID
              query = query.ilike("id", `%${value}%`);
            }
          } else if (key === "form_data.candidate_search") {
            // Special case for searching both name and surname
            console.debug(`Applying candidate search filter: ${value}`);
            // Use OR condition to search in both name and surname fields
            query = query.or(
              `form_data->>name.ilike.%${value}%,form_data->>surname.ilike.%${value}%`
            );
          } else if (
            key === "form_data.name" ||
            key === "form_data.surname" ||
            key === "form_data.address"
          ) {
            // Apply the filter immediately for these text fields
            console.debug(`Applying text filter: ${key} = ${value}`);
            query = query.ilike(key.replace(".", "->>"), `%${value}%`);
          }

          // Set up debounce for subsequent requests
          debounceTextFilter(key, value, (debouncedKey, debouncedValue) => {
            console.debug(
              `Debounced filter applied: ${debouncedKey} = ${debouncedValue}`
            );
            // This callback is for future requests, not the current one
          });
        } else if (key === "form_data.city") {
          // Find the city ID from the displayed label
          const cityEntry = cities.find(
            (city) =>
              city.labelEl.toLowerCase().includes(value.toLowerCase()) ||
              city.labelEn.toLowerCase().includes(value.toLowerCase())
          );

          if (cityEntry) {
            // Use the ID value stored in the database
            query = query.eq("form_data->>city", cityEntry.id);
          } else {
            // If we couldn't find a city entry, try a direct match on the city field
            query = query.ilike("form_data->>city", `%${value}%`);
          }
        } else if (key === "is_rejected") {
          query = query.eq("is_rejected", value === "Ναι");
        } else if (key === "form_data.position_interests") {
          // Find the position ID from the displayed label
          const positionEntry = positionOptions.find((pos) =>
            pos.label.toLowerCase().includes(value.toLowerCase())
          );

          if (positionEntry) {
            // Use the ID value stored in the database
            try {
              query = query.contains("form_data", {
                position_interests: [positionEntry.id],
              });
            } catch (error) {
              console.error("Error applying position_interests filter:", error);
            }
          } else {
            // If we couldn't find a position entry, try a direct match
            try {
              query = query.filter(
                "form_data->position_interests",
                "cs",
                JSON.stringify([value])
              );
            } catch (error) {
              console.error("Error applying position_interests filter:", error);
              // Last resort fallback
              query = query.ilike(
                "form_data->position_interests",
                `%${value}%`
              );
            }
          }
        } else if (key === "form_data.languages") {
          // Find the language ID from the displayed label
          const languageEntry = languages.find(
            (lang) =>
              lang.labelEl.toLowerCase() === value.toLowerCase() ||
              lang.labelEn.toLowerCase() === value.toLowerCase()
          );

          // This is more complex as languages is an array of objects
          // We'll handle this in post-processing with the correct ID
          if (languageEntry) {
            // Store the language ID for post-processing
            try {
              query = query.filter("form_data->languages", "cs", [
                JSON.stringify([{ language: languageEntry.id }]),
              ]);
            } catch (error) {
              console.error("Error applying languages filter:", error);
              // Fallback to a safer approach
              try {
                const languageObject = { language: languageEntry.id };
                query = query.ilike(
                  "form_data->languages",
                  `%${JSON.stringify(languageObject)}%`
                );
              } catch (innerError) {
                console.error(
                  "Error in fallback for languages filter:",
                  innerError
                );
                // Last resort fallback
                query = query.ilike(
                  "form_data->languages",
                  `%${languageEntry.id}%`
                );
              }
            }
          }
        } else if (
          key === "form_data.schedule_interests" ||
          key === "schedule_interests"
        ) {
          // Handle different formats of schedule interests
          let formattedValue = value.toLowerCase();

          console.debug(
            `Applying schedule_interests filter with value: ${formattedValue}`
          );
          try {
            // Always use the form_data JSON field, never try to access schedule_interests directly
            query = query.contains("form_data", {
              schedule_interests: [formattedValue],
            });
          } catch (error) {
            console.error("Error applying schedule_interests filter:", error);
          }
        } else if (key === "is_approved") {
          // Handle approval status filter
          if (value === "Approved") {
            query = query.eq("is_approved", true);
          } else if (value === "Pending") {
            query = query.or("is_approved.is.null,is_approved.eq.false");
          } else if (value === "Rejected") {
            query = query.eq("is_rejected", true);
          }
        } else if (key === "form_data.position_interests") {
          query = query.contains("form_data", {
            position_interests: [value.toLowerCase()],
          });
        } else if (key === "form_data.duration_interests") {
          console.debug(
            `Applying duration_interests filter with value: ${value}`
          );
          try {
            query = query.contains("form_data", {
              duration_interests: [value.toLowerCase()],
            });
          } catch (error) {
            console.error("Error applying duration_interests filter:", error);
          }
        } else if (key === "form_data.level") {
          query = query.eq("form_data->>level", value.toLowerCase());
        } else if (key === "form_data.years_experience") {
          // Handle years of experience filter
          const years = parseInt(value, 10);
          if (!isNaN(years)) {
            query = query.gte("form_data->>years_experience", years);
          } else {
            console.error(`Invalid years of experience value: ${value}`);
          }
        } else if (key === "form_data.start_availability") {
          const dateOption = startDateOptions.find((pos) =>
            pos.labelEl.toLowerCase().includes(value.toLowerCase())
          );
          if (dateOption?.id === "weeks") {
            query = query.or(
              `form_data->>start_availability.eq.weeks,form_data->>start_availability.eq.immediately`
            );
          } else if (dateOption?.id === "flexible") {
            query = query.or(
              `form_data->>start_availability.eq.flexible,form_data->>start_availability.eq.immediately,form_data->>start_availability.eq.weeks,form_data->>start_availability.eq.other,form_data->>start_availability.eq.""`
            );
          } else {
            query = query.eq(
              "form_data->>start_availability",
              dateOption?.id || value.toLowerCase()
            );
          }
        } else if (key === "form_data.candidate_type") {
          const candidateTypeOption = candidateTypeOptions.find((pos) =>
            pos.label.toLowerCase().includes(value.toLowerCase())
          );
          query = query.eq(
            "form_data->>candidate_type",
            candidateTypeOption?.id
          );
        } else if (!isTextFilter) {
          // If that fails, try as a form_data field
          query = query.ilike(`${key}`, `%${value}%`);
        }
      }
    } catch (error) {
      console.error(`Error applying filter ${key}:`, error);
      // Try a simple ilike filter as a last resort
      try {
        if (key.includes("form_data")) {
          query = query.ilike(key, `%${value}%`);
        } else {
          query = query.ilike(`form_data->>${key}`, `%${value}%`);
        }
      } catch (fallbackError) {
        console.error(
          `Failed to apply fallback filter for ${key}:`,
          fallbackError
        );
        // Just continue without this filter
      }
    }
  });

  // If we're using debounced filters, log it
  if (usingDebouncedFilters) {
    console.debug("Using debounced text filters with 500ms delay");
  }

  return query;
};

/**
 * Processes raw candidate data from Supabase
 * @param candidateData Raw candidate data from Supabase
 * @returns Processed candidate data
 */
const processCandidate = (candidateData: Candidate): Candidate => {
  return {
    ...candidateData,
  };
};

/**
 * Fetches candidates (not approved)
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters with filters
 * @returns Array of candidates or paginated response if pagination is provided
 */
export const fetchCandidates = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Candidate[] | PaginatedResponse<Candidate>> => {
  // Main request logic
  try {
    // Create the base query
    let query = supabase
      .from("candidates")
      .select("*", { count: "exact" })
      .order("id");

    if (!pagination?.filters?.is_nanny_approved) {
      query = query.or("is_nanny_approved.is.null,is_nanny_approved.eq.false");
    }
    if (!pagination?.filters?.is_tutor_approved) {
      query = query.or("is_tutor_approved.is.null,is_tutor_approved.eq.false");
    }

    // Apply filters using the shared applyFilters function
    query = applyFilters(query, pagination);
    const { data, count: totalCount } = await query;

    // If pagination is requested, use the paginate utility
    if (pagination) {
      const paginatedResponse = await paginateQuery<Candidate>(
        data,
        pagination,
        totalCount,
        Math.ceil(totalCount / pagination.itemsPerPage)
      );

      // Process the data
      let processedData = paginatedResponse.data.map(processCandidate);

      // Apply post-processing filters for complex cases like languages

      // Create the paginated response
      let result: PaginatedResponse<Candidate>;
      result = {
        ...paginatedResponse,
        data: processedData,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / pagination.itemsPerPage),
      };

      return result;
    } else {
      // Otherwise, fetch all data
      const response = await query;

      if (response.error) {
        throw new Error(response.error.message);
      }

      // Process the data
      const candidates = response.data?.map(processCandidate) || [];
      return candidates;
    }
  } catch (error) {
    console.error("Error fetching candidates:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          totalCount: 0,
          page: pagination?.page || 1,
          itemsPerPage: pagination?.itemsPerPage || 10,
          totalPages: 0,
        }
      : [];
  }
};

/**
 * Fetches nannies (approved candidates)
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters with filters
 * @returns Array of nannies or paginated response if pagination is provided
 */
export const fetchNannies = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Candidate[] | PaginatedResponse<Candidate>> => {
  // Main request logic
  try {
    // Create the base query
    let query = supabase
      .from("candidates")
      .select("*", { count: "exact" })
      .eq("is_nanny_approved", true)
      .order("id");

    // Apply filters using the shared applyFilters function
    query = applyFilters(query, pagination);
    const { data, count: totalCount } = await query;

    // If pagination is requested, use the paginate utility
    if (pagination) {
      const paginatedResponse = await paginateQuery<Candidate>(
        data,
        pagination,
        totalCount,
        Math.ceil(totalCount / pagination.itemsPerPage)
      );

      // Process the data
      let processedData = paginatedResponse.data.map(processCandidate);

      // Apply post-processing filters for complex cases like languages

      // Create the paginated response
      let result: PaginatedResponse<Candidate>;

      result = {
        ...paginatedResponse,
        data: processedData,
        page: pagination.page,
        itemsPerPage: pagination.itemsPerPage,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / pagination.itemsPerPage),
      };

      return result;
    } else {
      // Otherwise, fetch all data
      const response = await query;

      if (response.error) {
        throw new Error(response.error.message);
      }

      // Process the data
      const nannies = response.data?.map(processCandidate) || [];
      return nannies;
    }
  } catch (error) {
    console.error("Error fetching nannies:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          page: pagination?.page || 1,
          itemsPerPage: pagination?.itemsPerPage || 10,
          totalCount: 0,
          totalPages: 0,
        }
      : [];
  }
};
/**
 * Fetches tutors (approved candidates)
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters with filters
 * @returns Array of tutors or paginated response if pagination is provided
 */
export const fetchTutors = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Candidate[] | PaginatedResponse<Candidate>> => {
  // Main request logic
  try {
    // Create the base query for tutors
    let query = supabase
      .from("candidates")
      .select("*", { count: "exact" })
      .eq("is_tutor_approved", true)
      .order("id");

    // Apply filters using the shared applyFilters function
    query = applyFilters(query, pagination);
    const { data, count: totalCount } = await query;

    // If pagination is requested, use the paginate utility
    if (pagination) {
      const paginatedResponse = await paginateQuery<Candidate>(
        data,
        pagination,
        totalCount,
        Math.ceil(totalCount / pagination.itemsPerPage)
      );

      // Process the data
      let processedData = paginatedResponse.data.map(processCandidate);

      // Apply post-processing filters for complex cases like languages

      // Create the paginated response
      let result: PaginatedResponse<Candidate>;

      result = {
        ...paginatedResponse,
        data: processedData,
        page: pagination.page,
        itemsPerPage: pagination.itemsPerPage,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / pagination.itemsPerPage),
      };

      return result;
    } else {
      // Otherwise, fetch all data
      const response = await query;

      if (response.error) {
        throw new Error(response.error.message);
      }

      // Process the data
      const tutors = response.data?.map(processCandidate) || [];
      return tutors;
    }
  } catch (error) {
    console.error("Error fetching tutors:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          page: pagination?.page || 1,
          itemsPerPage: pagination?.itemsPerPage || 10,
          totalCount: 0,
          totalPages: 0,
        }
      : [];
  }
};
/**
 * Fetches a single candidate by ID
 * @param id The candidate ID
 * @returns The candidate or null if not found
 */
export const fetchCandidateById = async (
  id: string
): Promise<Candidate | null> => {
  try {
    const response = await supabase
      .from("candidates")
      .select("*")
      .eq("id", id)
      .single();

    if (response.error) {
      throw new Error(response.error.message);
    }

    return processCandidate(response.data);
  } catch (error) {
    console.error(`Error fetching candidate with ID ${id}:`, error);
    return null;
  }
};

/**
 * Updates a candidate
 * @param candidate The candidate to update
 * @returns True if successful, false otherwise
 */
export const updateCandidate = async (
  candidate: Candidate
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("candidates")
      .update({
        schedule: candidate.schedule,
        form_data: candidate.form_data,
      })
      .eq("id", candidate.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating candidate:", error);
    return false;
  }
};

/**
 * Approves a candidate (changes is_approved to true)
 * @param id The candidate ID
 * @param userType The user type (Nanny or Tutor)
 * @returns True if successful, false otherwise
 */
export const approveCandidate = async (
  id: string,
  userType: string
): Promise<boolean> => {
  try {
    await client.post(`candidates/${id}/approve`, {
      user_type: userType,
    });
    return true;
  } catch (error) {
    console.error(`Error approving candidate with ID ${id}:`, error);
    return false;
  }
};

/**
 * Rejects a candidate (changes is_approved to false)
 * @param id The candidate ID
 * @returns True if successful, false otherwise
 */
export const rejectCandidate = async (id: string): Promise<boolean> => {
  try {
    await client.put(`candidates/${id}/reject`);
    return true;
  } catch (error) {
    console.error(`Error rejecting candidate with ID ${id}:`, error);
    return false;
  }
};

/**
 * Deletes a candidate
 * @param id The candidate ID
 * @returns True if successful, false otherwise
 */
export const deleteCandidate = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase.from("candidates").delete().eq("id", id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error(`Error deleting candidate with ID ${id}:`, error);
    return false;
  }
};
