import { supabase } from "@/lib/supabase";

export interface City {
  id: string;
  labelEl: string;
  labelEn: string;
  country: string;
  created_at?: string;
}

/**
 * Fetches all cities from Supabase
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @returns Array of cities
 */
export const fetchCities = async (forceRefresh = false): Promise<City[]> => {
  try {
    // Fetch cities from Supabase
    const { data, error } = await supabase
      .from("cities")
      .select("*")
      .order("country")
      .order("label_en");

    if (error) {
      throw new Error(error.message);
    }

    // Transform the data to match the City interface
    const cities: City[] = data.map((city) => ({
      id: city.id,
      labelEl: city.label_el,
      labelEn: city.label_en,
      country: city.country,
      created_at: city.created_at,
    }));

    return cities;
  } catch (error) {
    console.error("Error fetching cities:", error);
    return [];
  }
};

/**
 * Adds a new city to Supabase
 * @param city The city to add
 * @returns True if successful, false otherwise
 */
export const addCity = async (city: City): Promise<boolean> => {
  try {
    // Insert the city into Supabase
    const { error } = await supabase.from("cities").insert({
      id: city.id,
      label_el: city.labelEl,
      label_en: city.labelEn,
      country: city.country,
    });

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error adding city:", error);
    return false;
  }
};

/**
 * Updates an existing city in Supabase
 * @param city The city to update
 * @returns True if successful, false otherwise
 */
export const updateCity = async (city: City): Promise<boolean> => {
  try {
    // Update the city in Supabase
    const { error } = await supabase
      .from("cities")
      .update({
        label_el: city.labelEl,
        label_en: city.labelEn,
        country: city.country,
      })
      .eq("id", city.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating city:", error);
    return false;
  }
};

/**
 * Deletes a city from Supabase
 * @param cityId The ID of the city to delete
 * @returns True if successful, false otherwise
 */
export const deleteCity = async (cityId: string): Promise<boolean> => {
  try {
    // Delete the city from Supabase
    const { error } = await supabase.from("cities").delete().eq("id", cityId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error deleting city:", error);
    return false;
  }
};
