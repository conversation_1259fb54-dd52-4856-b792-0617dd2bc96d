import { supabase } from "@/lib/supabase";
import { useToast } from "@/components/ui/use-toast";

export interface ClientNote {
  id: string;
  client_id: string;
  user_id: string;
  note: string;
  note_type: string; // e.g., "call", "meeting", "email", "note"
  created_at: string;
  updated_at: string;
  user_email?: string;
}

/**
 * Fetches notes for a specific client
 * @param clientId The client ID
 * @returns Array of client notes
 */
export const fetchClientNotes = async (
  clientId: string
): Promise<ClientNote[]> => {
  try {
    // Fetch notes with user information using a join
    const { data, error } = await supabase
      .from("client_notes")
      .select(
        `
        *,
        users:user_id (email,id)
      `
      )
      .eq("client_id", clientId)
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    // Format the notes with user email
    const formattedNotes = data.map((note) => ({
      ...note,
      user_email: note.users?.email || "Unknown User",
      user_id: note.users?.id || "Unknown User",
    }));

    return formattedNotes;
  } catch (error) {
    console.error("Error fetching client notes:", error);
    return [];
  }
};

/**
 * Adds a new note for a client
 * @param clientId The client ID
 * @param userId The user ID
 * @param note The note content
 * @param noteType The type of note (call, meeting, email, note)
 * @returns The created note or null if there was an error
 */
export const addClientNote = async (
  clientId: string,
  userId: string,
  note: string,
  noteType: string = "note"
): Promise<ClientNote | null> => {
  try {
    const { data, error } = await supabase
      .from("client_notes")
      .insert([
        {
          client_id: clientId,
          user_id: userId,
          note,
          note_type: noteType,
        },
      ])
      .select();

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error("No data returned after adding note");
    }

    // Get user email for the newly created note
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("email")
      .eq("id", userId)
      .single();

    if (userError) {
      console.error("Error fetching user email:", userError);
    }

    // Return the created note with user email
    return {
      ...data[0],
      user_email: userData?.email || "Unknown User",
    };
  } catch (error) {
    console.error("Error adding client note:", error);
    return null;
  }
};

/**
 * Updates an existing client note
 * @param noteId The note ID
 * @param note The updated note content
 * @returns The updated note or null if there was an error
 */
export const updateClientNote = async (
  noteId: string,
  note: string
): Promise<ClientNote | null> => {
  try {
    const { data, error } = await supabase
      .from("client_notes")
      .update({ note, updated_at: new Date().toISOString() })
      .eq("id", noteId)
      .select();

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error("No data returned after updating note");
    }

    // Get user email for the updated note
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("email")
      .eq("id", data[0].user_id)
      .single();

    if (userError) {
      console.error("Error fetching user email:", userError);
    }

    // Return the updated note with user email
    return {
      ...data[0],
      user_email: userData?.email || "Unknown User",
    };
  } catch (error) {
    console.error("Error updating client note:", error);
    return null;
  }
};

/**
 * Deletes a client note
 * @param noteId The note ID
 * @returns True if the note was deleted successfully, false otherwise
 */
export const deleteClientNote = async (noteId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("client_notes")
      .delete()
      .eq("id", noteId);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error("Error deleting client note:", error);
    return false;
  }
};
