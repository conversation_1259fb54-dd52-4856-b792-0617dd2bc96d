import { Client, clientStatuses } from "@/components/crm/clients/ClientsTable";
import {
  PaginatedResponse,
  PaginationParams,
  paginateQuery,
} from "@/lib/paginationUtils";
import { cities, languages } from "@/lib/staticData";
import { supabase } from "@/lib/supabase";
import {
  childrenAgeOptions,
  positionOptions,
  startDateOptions,
} from "@/schemas/FormSchema";

/**
 * Apply filters to a Supabase query based on pagination filters
 * @param query The Supabase query to apply filters to
 * @param pagination Pagination parameters with filters
 * @returns The modified query with filters applied
 */
const applyFilters = (query: any, pagination?: PaginationParams): any => {
  if (!pagination?.filters) return query;

  console.debug("Applying filters in clientService:", pagination.filters);

  // Apply each filter
  Object.entries(pagination.filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      console.debug(`Applying filter: ${key} = ${value}`);

      // Handle different filter types
      if (key === "form_data.client_search") {
        // Special case for searching both father_name and mother_name
        console.debug(`Applying client search filter: ${value}`);
        // Use OR condition to search in both father_name and mother_name fields
        query = query.or(
          `form_data->>father_name.ilike.%${value}%,form_data->>mother_name.ilike.%${value}%`
        );
      } else if (key === "form_data.father_name") {
        console.debug("Applying filters to father_name ");
        // Search in form_data->father_name
        query = query.ilike("form_data->>father_name", `%${value}%`);
      } else if (key === "form_data.mother_name") {
        // Search in form_data->mother_name
        query = query.ilike("form_data->>mother_name", `%${value}%`);
      } else if (key === "form_data.address") {
        // Search in form_data->permanent_address
        query = query.ilike("form_data->>permanent_address", `%${value}%`);
      } else if (key === "form_data.position") {
        // Search in form_data->position_type
        query = query.ilike("form_data->>position_type", `%${value}%`);
      } else if (key === "total_revenue") {
        query = query.gte("total_revenue", value);
      } else if (
        key === "form_data.language" ||
        key === "form_data.languages"
      ) {
        const languageEntry = languages.find(
          (lang) =>
            lang.labelEl.toLowerCase() === value.toLowerCase() ||
            lang.labelEn.toLowerCase() === value.toLowerCase()
        );
        if (languageEntry) {
          // Store the language ID for post-processing
          try {
            query = query.filter("form_data->languages", "cs", [
              JSON.stringify([{ language: languageEntry.id }]),
            ]);
          } catch (error) {
            console.error("Error applying languages filter:", error);
            // Fallback to a safer approach
            try {
              const languageObject = { language: languageEntry.id };
              query = query.ilike(
                "form_data->languages",
                `%${JSON.stringify(languageObject)}%`
              );
            } catch (innerError) {
              console.error(
                "Error in fallback for languages filter:",
                innerError
              );
              // Last resort fallback
              query = query.ilike(
                "form_data->languages",
                `%${languageEntry.id}%`
              );
            }
          }
        }
      } else if (
        key === "form_data.position_duration" ||
        key === "form_data.duration_interests"
      ) {
        query = query.eq("form_data->>position_duration", value.toLowerCase());
      } else if (key === "status") {
        const status = clientStatuses.find(
          (status) => status.label.toLowerCase() === value.toLowerCase()
        );
        query = query.eq("status", status?.id);
      } else if (
        key === "form_data.start_date" ||
        key === "form_data.start_availability"
      ) {
        const dateOption = startDateOptions.find((pos) =>
          pos.labelEl.toLowerCase().includes(value.toLowerCase())
        );
        if (dateOption?.id === "weeks") {
          query = query.or(
            `form_data->>start_date.eq.weeks,form_data->>start_date.eq.immediately`
          );
        } else if (dateOption?.id === "flexible") {
          query = query.or(
            `form_data->>start_date.eq.flexible,form_data->>start_date.eq.immediately,form_data->>start_date.eq.weeks,form_data->>start_date.eq.other,form_data->>start_date.eq.""`
          );
        } else {
          query = query.eq("form_data->>start_date", dateOption?.id || value);
        }
      } else if (key === "form_data.position_interests") {
        // Find the position ID from the displayed label
        const positionEntry = positionOptions.find((pos) =>
          pos.label.toLowerCase().includes(value.toLowerCase())
        );
        if (positionEntry) {
          // Use the ID value stored in the database
          try {
            query = query.contains("form_data", {
              position_interests: [positionEntry.id],
            });
          } catch (error) {
            console.error("Error applying position_interests filter:", error);
          }
        } else {
          console.debug("Error applying position_interests filter:" + value);
        }
      } else if (key === "form_data.position_type") {
        try {
          query = query.contains("form_data", {
            position_type: value.toLowerCase(),
          });
        } catch (error) {
          console.error("Error applying position_type filter:", error);
        }
      } else if (key === "form_data.schedule_type") {
        try {
          query = query.contains("form_data", {
            schedule_type: value.toLowerCase(),
          });
        } catch (error) {
          console.error("Error applying schedule_type filter:", error);
        }
      } else if (key === "form_data.city") {
        const cityEntry = cities.find(
          (city) =>
            city.labelEl.toLowerCase().includes(value.toLowerCase()) ||
            city.labelEn.toLowerCase().includes(value.toLowerCase())
        );

        if (cityEntry) {
          // Use the ID value stored in the database
          query = query.eq("form_data->>city", cityEntry.id);
        } else {
          // If we couldn't find a city entry, try a direct match on the city field
          query = query.ilike("form_data->>city", `%${value}%`);
        }
      } else if (
        key === "form_data.children_age_experience" ||
        key === "children_age" ||
        key === "form_data.children_age"
      ) {
        const ageExperienceEntry = childrenAgeOptions.find((pos) =>
          pos.labelEl.toLowerCase().includes(value.toLowerCase())
        );
        if (ageExperienceEntry) {
          try {
            query = query.contains("form_data", {
              children_age: [ageExperienceEntry.id],
            });
          } catch (error) {
            console.error(
              "Error applying children_age_experience filter:",
              error
            );
          }
        } else {
          console.debug(
            "Error applying children_age_exprerience filter:" + value
          );
        }
      } else if (key === "form_data.level" || key === "working_level") {
        query = query.contains("form_data", {
          working_level: value.toLowerCase(),
        });
      } else if (key === "form_data.schedule_interests") {
        query = query.eq("form_data->>schedule_type", value.toLowerCase());
      } else if (key === "form_data.children") {
        // Search in form_data->children_count
        query = query.eq("form_data->children_count", value.toString());
      } else if (key === "client" || key === "id") {
        query = query.eq("id", value.replace("CL-"), "");
      } else {
        query = query.ilike(`form_data->>${key}`, `%${value}%`);
      }
    }
  });

  return query;
};

/**
 * Process raw client data from Supabase
 * @param rawClient The raw client data from Supabase
 * @returns Processed client data
 */
const processClient = (rawClient: any): Client => {
  // Parse form_data if it exists and is a string
  let parsedFormData = undefined;

  if (rawClient.form_data) {
    if (typeof rawClient.form_data === "string") {
      try {
        parsedFormData = JSON.parse(rawClient.form_data);
      } catch (e) {
        console.error("Error parsing form_data JSON:", e);
      }
    } else {
      parsedFormData = rawClient.form_data;
    }
  }

  return {
    ...rawClient,
    parsedFormData,
  };
};

export const fetchLeadsAndClients = async () => {
  try {
    // Fetch leads (contacts with status != 2)
    let query = supabase
      .from("clients")
      .select("*")
      .or("status.eq.1,status.eq.3,status.eq.4")
      .order("id");

    const leadsData = await query;

    return leadsData;
  } catch (error) {
    console.error("Error fetching leads and clients:", error);
    return [];
  }
};

/**
 * Fetches all clients
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters
 * @returns Array of clients or paginated response if pagination is provided
 */
export const fetchClients = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Client[] | PaginatedResponse<Client>> => {
  try {
    // Create a query that joins clients with deals
    // This will only return clients that have at least one deal
    let query = supabase
      .from("clients_with_revenue")
      .select("*", { count: "exact" })
      .order("total_revenue", { ascending: false });

    // We need to get distinct clients since a client might have multiple deals

    // Apply filters using a separate function
    query = applyFilters(query, pagination);
    const { data, count: totalCount } = await query;

    // If pagination is requested, use the paginate utility
    if (pagination) {
      console.debug("Using pagination with params:", pagination);
      const paginatedResponse = await paginateQuery<any>(
        data,
        pagination,
        totalCount,
        Math.ceil(totalCount / pagination.itemsPerPage)
      );

      // Process the data and remove duplicates
      // Since we're using a join, we might get the same client multiple times (once for each deal)
      const clientMap = new Map();
      paginatedResponse.data.forEach((client) => {
        // Remove the deals data from the client object
        const { deals, ...clientData } = client;
        // Only add the client if it's not already in the map
        if (!clientMap.has(clientData.id)) {
          clientMap.set(clientData.id, clientData);
        }
      });

      // Convert the map values to an array and process each client
      const uniqueClients = Array.from(clientMap.values());
      console.debug("Unique clients :", JSON.stringify(uniqueClients));
      const processedData = uniqueClients.map(processClient);

      return {
        ...paginatedResponse,
        data: processedData,
      };
    } else {
      // Otherwise, fetch all data
      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      // Convert the map values to an array and process each client
      const clients = data?.map(processClient) || [];
      return clients;
    }
  } catch (error) {
    console.error("Error fetching clients:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          totalCount: 0,
          page: pagination.page,
          itemsPerPage: pagination.itemsPerPage,
          totalPages: 0,
        }
      : [];
  }
};

/**
 * Fetches a single client by ID
 * @param id The client ID
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @returns The client or null if not found
 */
export const fetchClientById = async (
  id: string,
  forceRefresh = false
): Promise<Client | null> => {
  try {
    const idWithoutPrefix = id.replace("CL-", "");
    // Try to fetch by client_id first
    let { data, error: err } = await supabase
      .from("clients")
      .select("*")
      .eq("id", idWithoutPrefix)
      .single();

    // If not found by client_id, try with the ID prefix
    if (!data) {
      ({ data, error: err } = await supabase
        .from("clients")
        .select("*")
        .eq("id", id)
        .single());
    }

    if (err) throw err;

    if (!data) {
      return null;
    }

    // Process the data
    const client = processClient(data);
    return client;
  } catch (error) {
    console.error(`Error fetching client with ID ${id}:`, error);
    return null;
  }
};

/**
 * Creates a new client
 * @param client The client to create
 * @returns The created client or null if there was an error
 */
export const createClient = async (
  client: Partial<Client>
): Promise<Client | null> => {
  try {
    const { data, error } = await supabase
      .from("clients")
      .insert([client])
      .select();

    if (error) {
      throw new Error(error.message);
    }

    if (!data || data.length === 0) {
      throw new Error("No data returned after creating client");
    }

    // Process the data
    const newClient = processClient(data[0]);
    return newClient;
  } catch (error) {
    console.error("Error creating client:", error);
    return null;
  }
};

/**
 * Updates an existing client
 * @param id The client ID
 * @param updates The updates to apply
 * @returns The updated client or null if there was an error
 */
export const updateClient = async (
  id: string,
  updates: Partial<Client>
): Promise<Client | null> => {
  try {
    const { data, error } = await supabase
      .from("clients")
      .update(updates)
      .eq("id", id)
      .select();

    if (error) {
      throw new Error(error.message);
    }

    if (!data || data.length === 0) {
      throw new Error("No data returned after updating client");
    }

    // Process the data
    const updatedClient = processClient(data[0]);
    return updatedClient;
  } catch (error) {
    console.error(`Error updating client with ID ${id}:`, error);
    return null;
  }
};

/**
 * Deletes a client
 * @param id The client ID
 * @returns True if successful, false otherwise
 */
export const deleteClient = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase.from("clients").delete().eq("id", id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error(`Error deleting client with ID ${id}:`, error);
    return false;
  }
};

/**
 * Fetches clients without deals (contacts) with status = 1 (Lead)
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters
 * @returns Array of clients or paginated response if pagination is provided
 */
export const fetchLeads = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Client[] | PaginatedResponse<Client>> => {
  try {
    // Create the base query for clients with status = 1 (Lead)
    let query = supabase
      .from("clients")
      .select("*", { count: "exact" })
      .or("status.eq.1,status.eq.3,status.eq.4")
      .order("id");
    // Apply additional filters
    query = applyFilters(query, pagination);
    const { data, count: totalCount } = await query;

    // If pagination is requested, use the paginate utility
    if (pagination) {
      console.debug("Using pagination with params:", pagination);
      const paginatedResponse = await paginateQuery<any>(
        data,
        pagination,
        totalCount,
        Math.ceil(totalCount / pagination.itemsPerPage)
      );

      return paginatedResponse;
    } else {
      // Otherwise, fetch all data
      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      // Process the data
      const leads = data?.map(processClient) || [];
      return leads;
    }
  } catch (error) {
    console.error("Error fetching leads:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          totalCount: 0,
          page: pagination.page,
          itemsPerPage: pagination.itemsPerPage,
          totalPages: 0,
        }
      : [];
  }
};

/**
 * Fetches clients without deals (contacts) that don't belong to the clients_with_revenue view
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters
 * @returns Array of clients or paginated response if pagination is provided
 */
export const fetchContacts = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Client[] | PaginatedResponse<Client>> => {
  try {
    // Create the base query for clients that don't exist in clients_with_revenue view
    let query = supabase
      .from("clients_without_deals")
      .select("*", { count: "exact" })
      .order("id");

    // Apply additional filters
    query = applyFilters(query, pagination);
    const { data, count: totalCount } = await query;

    // If pagination is requested, use the paginate utility
    if (pagination) {
      const paginatedResponse = await paginateQuery<Client>(
        data,
        pagination,
        totalCount,
        Math.ceil(totalCount / pagination.itemsPerPage)
      );

      // Process the data

      // Create the paginated response
      const result = {
        ...paginatedResponse,
        data: paginatedResponse.data,
        totalCount,
        totalPages: Math.ceil(totalCount / pagination.itemsPerPage),
      };

      return result;
    } else {
      // Otherwise, fetch all data
      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      // Process the data
      const contacts = data?.map(processClient) || [];
      return contacts;
    }
  } catch (error) {
    console.error("Error fetching contacts:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          totalCount: 0,
          page: pagination.page,
          itemsPerPage: pagination.itemsPerPage,
          totalPages: 0,
        }
      : [];
  }
};

/**
 * Fetches both clients and contacts with status leads for the Connect view
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters
 * @returns Array of clients or paginated response if pagination is provided
 */
export const fetchConnectClients = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Client[] | PaginatedResponse<Client>> => {
  try {
    // Create a copy of the pagination object to avoid modifying the original
    const leadsPagination = pagination ? { ...pagination } : undefined;

    // Fetch leads (contacts with status = 1)
    const leadsResult = await fetchLeads(forceRefresh, leadsPagination);

    const leadsData = Array.isArray(leadsResult)
      ? leadsResult
      : leadsResult.data;

    // Combine the results - no need to filter leads as fetchLeads already does that
    const combinedData = [...leadsData];
    const totalCount = Array.isArray(leadsResult)
      ? leadsResult.length
      : leadsResult.totalCount;
    const totalPages = Array.isArray(leadsResult) ? 1 : leadsResult.totalPages;

    // If pagination was requested, create a paginated response
    if (pagination) {
      const result = {
        data: combinedData,
        totalCount,
        page: pagination.page,
        itemsPerPage: pagination.itemsPerPage,
        totalPages,
      };

      return result;
    } else {
      return combinedData;
    }
  } catch (error) {
    console.error("Error fetching connect clients:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          totalCount: 0,
          page: pagination.page,
          itemsPerPage: pagination.itemsPerPage,
          totalPages: 0,
        }
      : [];
  }
};
