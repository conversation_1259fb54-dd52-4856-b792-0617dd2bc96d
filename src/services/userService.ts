import client from "@/lib/api";
import { supabase } from "@/lib/supabase";

// Permission codes as provided
export const PERMISSION_CODES = {
  post_crm: 31,
  put_crm: 32,
  delete_crm: 33,
  get_crm: 34,
  get_analytics: 35,
  get_activities: 36,
  put_analytics: 37,
  get_connect: 38,
  post_nannies: 39,
  put_nannies: 40,
  delete_nannies: 41,
  get_nannies: 42,
  post_tutors: 43,
  put_tutors: 44,
  delete_tutors: 45,
  get_tutors: 46,
  post_candidates: 47,
  put_candidates: 48,
  delete_candidates: 49,
  get_candidates: 50,
  post_deals: 51,
  put_deals: 52,
  delete_deals: 53,
  get_deals: 54,
};

// Permission mapping to page and action
export interface PermissionMapping {
  page: string;
  label: string;
  action: "view" | "create" | "edit" | "delete";
}

// Map permission codes to their respective page and action
export const PERMISSION_MAPPING: Record<number, PermissionMapping> = {
  // CRM permissions
  31: { page: "crm", label: "CRM", action: "create" },
  32: { page: "crm", label: "CRM", action: "edit" },
  33: { page: "crm", label: "CRM", action: "delete" },
  34: { page: "crm", label: "CRM", action: "view" },

  // Analytics permissions
  35: { page: "analytics", label: "Analytics", action: "view" },
  36: { page: "activities", label: "Activities Feed", action: "view" },
  37: { page: "analytics", label: "Analytics", action: "edit" },
  38: { page: "connect", label: "Connect", action: "view" },

  // Nannies permissions
  39: { page: "nannies", label: "Nannies", action: "create" },
  40: { page: "nannies", label: "Nannies", action: "edit" },
  41: { page: "nannies", label: "Nannies", action: "delete" },
  42: { page: "nannies", label: "Nannies", action: "view" },

  // Tutors permissions
  43: { page: "tutors", label: "Tutors", action: "create" },
  44: { page: "tutors", label: "Tutors", action: "edit" },
  45: { page: "tutors", label: "Tutors", action: "delete" },
  46: { page: "tutors", label: "Tutors", action: "view" },

  // Candidates permissions
  47: { page: "candidates", label: "Candidates", action: "create" },
  48: { page: "candidates", label: "Candidates", action: "edit" },
  49: { page: "candidates", label: "Candidates", action: "delete" },
  50: { page: "candidates", label: "Candidates", action: "view" },

  // Deals permissions
  51: { page: "deals", label: "Deals", action: "create" },
  52: { page: "deals", label: "Deals", action: "edit" },
  53: { page: "deals", label: "Deals", action: "delete" },
  54: { page: "deals", label: "Deals", action: "view" },
};

export const pages = [
  "dashboard",
  "crm",
  "nannies",
  "tutors",
  "candidates",
  "deals",
  "connect",
  "settings",
  "analytics",
  "activities",
];

// User interface
export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  date_joined: string;
  last_login: string | null;
  user_type: number;
  supabase_uuid?: string;
}

// User with permissions
export interface UserWithPermissions extends User {
  permissions: number[];
}

/**
 * Fetches all users from Supabase
 * @returns Array of users
 */
export const fetchUsers = async (): Promise<User[]> => {
  try {
    const { data, error } = await supabase
      .from("users")
      .select(
        "id, username, email, first_name, last_name, date_joined, last_login, user_type, supabase_uuid"
      )
      .or("user_type.eq.1,user_type.eq.2")
      .order("user_type");

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching users:", error);
    return [];
  }
};

/**
 * Fetches a user's permissions using the API endpoint
 * @param userId The user ID
 * @returns Array of permission IDs
 */
export const fetchUserPermissions = async (
  userId: number
): Promise<number[]> => {
  try {
    // Use the API endpoint to get user permissions
    const response = await client.get(`/users/${userId}/`);

    // The response should contain the user's permissions
    if (response.data && Array.isArray(response.data.permissions)) {
      return response.data.permissions;
    }

    return [];
  } catch (error) {
    console.error(`Error fetching permissions for user ${userId}:`, error);
    return [];
  }
};

/**
 * Fetches a user with their permissions using the API endpoint
 * @param userId The user ID
 * @returns User with permissions or null if not found
 */
export const fetchUserWithPermissions = async (
  userId: number
): Promise<UserWithPermissions | null> => {
  try {
    // Fetch user
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select(
        "id, username, email, first_name, last_name, date_joined, last_login, user_type, supabase_uuid"
      )
      .eq("id", userId)
      .single();

    if (userError) {
      throw new Error(userError.message);
    }

    if (!userData) {
      return null;
    }

    // Fetch user permissions using the API endpoint
    const permissions = await fetchUserPermissions(userId);

    return {
      ...userData,
      permissions,
    };
  } catch (error) {
    console.error(
      `Error fetching user with permissions for user ${userId}:`,
      error
    );
    return null;
  }
};

/**
 * Updates a user's permissions using the API endpoint
 * @param userId The user ID
 * @param permissions Array of permission IDs
 * @returns True if successful, false otherwise
 */
export const updateUserPermissions = async (
  userId: number,
  permissions: number[]
): Promise<boolean> => {
  try {
    // Use the API endpoint to update user permissions
    await client.put(`/users/${userId}/`, {
      permissions: permissions,
    });

    return true;
  } catch (error) {
    console.error(`Error updating permissions for user ${userId}:`, error);
    return false;
  }
};

/**
 * Converts permission IDs to a page permissions object
 * @param permissionIds Array of permission IDs
 * @returns Record of page permissions
 */
export const permissionsToPagePermissions = (
  permissionIds: number[]
): Record<
  string,
  { edit: boolean; create: boolean; delete: boolean; view: boolean }
> => {
  const pagePermissions: Record<
    string,
    { edit: boolean; create: boolean; delete: boolean; view: boolean }
  > = {};

  // Initialize pages with all permissions set to false
  const pages = [
    "crm",
    "nannies",
    "tutors",
    "candidates",
    "deals",
    "connect",
    "analytics",
    "activities",
  ];
  pages.forEach((page) => {
    pagePermissions[page.toLowerCase()] = {
      edit: false,
      create: false,
      delete: false,
      view: false,
    };
  });

  // Set permissions based on permission IDs
  permissionIds.forEach((id) => {
    const mapping = PERMISSION_MAPPING[id];
    if (mapping) {
      pagePermissions[mapping.page.toLowerCase()][mapping.action] = true;
    }
  });

  return pagePermissions;
};

/**
 * Converts page permissions to permission IDs
 * @param pagePermissions Record of page permissions
 * @returns Array of permission IDs
 */
export const pagePermissionsToPermissions = (
  pagePermissions: Record<
    string,
    { edit: boolean; create: boolean; delete: boolean; view: boolean }
  >
): number[] => {
  const permissionIds: number[] = [];

  // Iterate through all permission mappings
  Object.entries(PERMISSION_MAPPING).forEach(([idStr, mapping]) => {
    const id = parseInt(idStr);
    const { page, action } = mapping;

    // Check if the page exists in pagePermissions and the action is true
    if (
      pagePermissions[page.toLowerCase()] &&
      pagePermissions[page.toLowerCase()][action]
    ) {
      permissionIds.push(id);
    }
  });

  return permissionIds;
};
