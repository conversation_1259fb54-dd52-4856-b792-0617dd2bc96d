export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      auth_group: {
        Row: {
          id: number;
          name: string;
        };
        Insert: {
          id?: number;
          name: string;
        };
        Update: {
          id?: number;
          name?: string;
        };
        Relationships: [];
      };
      auth_group_permissions: {
        Row: {
          group_id: number;
          id: number;
          permission_id: number;
        };
        Insert: {
          group_id: number;
          id?: number;
          permission_id: number;
        };
        Update: {
          group_id?: number;
          id?: number;
          permission_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: "auth_group_permissio_permission_id_84c5c92e_fk_auth_perm";
            columns: ["permission_id"];
            isOneToOne: false;
            referencedRelation: "auth_permission";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "auth_group_permissions_group_id_b120cbf9_fk_auth_group_id";
            columns: ["group_id"];
            isOneToOne: false;
            referencedRelation: "auth_group";
            referencedColumns: ["id"];
          }
        ];
      };
      auth_permission: {
        Row: {
          codename: string;
          content_type_id: number;
          id: number;
          name: string;
        };
        Insert: {
          codename: string;
          content_type_id: number;
          id?: number;
          name: string;
        };
        Update: {
          codename?: string;
          content_type_id?: number;
          id?: number;
          name?: string;
        };
        Relationships: [
          {
            foreignKeyName: "auth_permission_content_type_id_2f476e4b_fk_django_co";
            columns: ["content_type_id"];
            isOneToOne: false;
            referencedRelation: "django_content_type";
            referencedColumns: ["id"];
          }
        ];
      };
      candidates: {
        Row: {
          cv_generated: boolean;
          form_data: Json | null;
          id: number;
          is_nanny_approved: boolean;
          is_rejected: boolean;
          is_tutor_approved: boolean;
          schedule: Json | null;
          storage_id: string | null;
          user_id: number | null;
        };
        Insert: {
          cv_generated: boolean;
          form_data?: Json | null;
          id?: number;
          is_nanny_approved: boolean;
          is_rejected: boolean;
          is_tutor_approved: boolean;
          schedule?: Json | null;
          storage_id?: string | null;
          user_id?: number | null;
        };
        Update: {
          cv_generated?: boolean;
          form_data?: Json | null;
          id?: number;
          is_nanny_approved?: boolean;
          is_rejected?: boolean;
          is_tutor_approved?: boolean;
          schedule?: Json | null;
          storage_id?: string | null;
          user_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: "candidates_user_id_38b3e027_fk_users_id";
            columns: ["user_id"];
            isOneToOne: true;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      cities: {
        Row: {
          country: string;
          created_at: string;
          id: string;
          label_el: string;
          label_en: string;
        };
        Insert: {
          country: string;
          created_at?: string;
          id: string;
          label_el: string;
          label_en: string;
        };
        Update: {
          country?: string;
          created_at?: string;
          id?: string;
          label_el?: string;
          label_en?: string;
        };
        Relationships: [];
      };
      clients: {
        Row: {
          created_at: string | null;
          form_data: Json | null;
          id: number;
          status: number;
          storage_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          form_data?: Json | null;
          id?: number;
          status: number;
          storage_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          form_data?: Json | null;
          id?: number;
          status?: number;
          storage_id?: string | null;
        };
        Relationships: [];
      };
      deals: {
        Row: {
          candidate_id: number;
          candidate_salary: number;
          client_id: number;
          contract_url: string | null;
          deal_date: string;
          id: number;
          is_active: boolean;
          name: string;
          revenue: number;
        };
        Insert: {
          candidate_id: number;
          candidate_salary: number;
          client_id: number;
          contract_url?: string | null;
          deal_date: string;
          id?: number;
          is_active: boolean;
          name: string;
          revenue: number;
        };
        Update: {
          candidate_id?: number;
          candidate_salary?: number;
          client_id?: number;
          contract_url?: string | null;
          deal_date?: string;
          id?: number;
          is_active?: boolean;
          name?: string;
          revenue?: number;
        };
        Relationships: [
          {
            foreignKeyName: "deals_candidate_id_17d75efa_fk_candidates_id";
            columns: ["candidate_id"];
            isOneToOne: false;
            referencedRelation: "candidates";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "deals_client_id_31be102f_fk_clients_id";
            columns: ["client_id"];
            isOneToOne: false;
            referencedRelation: "clients";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "deals_client_id_31be102f_fk_clients_id";
            columns: ["client_id"];
            isOneToOne: false;
            referencedRelation: "clients_with_revenue";
            referencedColumns: ["id"];
          }
        ];
      };
      django_admin_log: {
        Row: {
          action_flag: number;
          action_time: string;
          change_message: string;
          content_type_id: number | null;
          id: number;
          object_id: string | null;
          object_repr: string;
          user_id: number;
        };
        Insert: {
          action_flag: number;
          action_time: string;
          change_message: string;
          content_type_id?: number | null;
          id?: number;
          object_id?: string | null;
          object_repr: string;
          user_id: number;
        };
        Update: {
          action_flag?: number;
          action_time?: string;
          change_message?: string;
          content_type_id?: number | null;
          id?: number;
          object_id?: string | null;
          object_repr?: string;
          user_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: "django_admin_log_content_type_id_c4bce8eb_fk_django_co";
            columns: ["content_type_id"];
            isOneToOne: false;
            referencedRelation: "django_content_type";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "django_admin_log_user_id_c564eba6_fk_users_id";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      django_content_type: {
        Row: {
          app_label: string;
          id: number;
          model: string;
        };
        Insert: {
          app_label: string;
          id?: number;
          model: string;
        };
        Update: {
          app_label?: string;
          id?: number;
          model?: string;
        };
        Relationships: [];
      };
      django_migrations: {
        Row: {
          app: string;
          applied: string;
          id: number;
          name: string;
        };
        Insert: {
          app: string;
          applied: string;
          id?: number;
          name: string;
        };
        Update: {
          app?: string;
          applied?: string;
          id?: number;
          name?: string;
        };
        Relationships: [];
      };
      django_session: {
        Row: {
          expire_date: string;
          session_data: string;
          session_key: string;
        };
        Insert: {
          expire_date: string;
          session_data: string;
          session_key: string;
        };
        Update: {
          expire_date?: string;
          session_data?: string;
          session_key?: string;
        };
        Relationships: [];
      };
      portal_custompermission: {
        Row: {
          action: string;
          id: number;
          section: string;
        };
        Insert: {
          action: string;
          id?: number;
          section: string;
        };
        Update: {
          action?: string;
          id?: number;
          section?: string;
        };
        Relationships: [];
      };
      users: {
        Row: {
          address: string | null;
          credentials_email_sent: boolean;
          date_joined: string;
          email: string;
          first_name: string;
          id: number;
          is_active: boolean;
          is_staff: boolean;
          is_superuser: boolean;
          last_login: string | null;
          last_name: string;
          password: string | null;
          phone_number: string | null;
          supabase_uuid: string | null;
          user_type: number;
          username: string | null;
        };
        Insert: {
          address?: string | null;
          credentials_email_sent: boolean;
          date_joined: string;
          email: string;
          first_name: string;
          id?: number;
          is_active: boolean;
          is_staff: boolean;
          is_superuser: boolean;
          last_login?: string | null;
          last_name: string;
          password?: string | null;
          phone_number?: string | null;
          supabase_uuid?: string | null;
          user_type: number;
          username?: string | null;
        };
        Update: {
          address?: string | null;
          credentials_email_sent?: boolean;
          date_joined?: string;
          email?: string;
          first_name?: string;
          id?: number;
          is_active?: boolean;
          is_staff?: boolean;
          is_superuser?: boolean;
          last_login?: string | null;
          last_name?: string;
          password?: string | null;
          phone_number?: string | null;
          supabase_uuid?: string | null;
          user_type?: number;
          username?: string | null;
        };
        Relationships: [];
      };
      users_groups: {
        Row: {
          group_id: number;
          id: number;
          user_id: number;
        };
        Insert: {
          group_id: number;
          id?: number;
          user_id: number;
        };
        Update: {
          group_id?: number;
          id?: number;
          user_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: "users_groups_group_id_2f3517aa_fk_auth_group_id";
            columns: ["group_id"];
            isOneToOne: false;
            referencedRelation: "auth_group";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "users_groups_user_id_f500bee5_fk_users_id";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      users_permissions: {
        Row: {
          custompermission_id: number;
          id: number;
          user_id: number;
        };
        Insert: {
          custompermission_id: number;
          id?: number;
          user_id: number;
        };
        Update: {
          custompermission_id?: number;
          id?: number;
          user_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: "users_permissions_custompermission_id_c93072a9_fk_portal_cu";
            columns: ["custompermission_id"];
            isOneToOne: false;
            referencedRelation: "portal_custompermission";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "users_permissions_user_id_2c381ffc_fk_users_id";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      users_user_permissions: {
        Row: {
          id: number;
          permission_id: number;
          user_id: number;
        };
        Insert: {
          id?: number;
          permission_id: number;
          user_id: number;
        };
        Update: {
          id?: number;
          permission_id?: number;
          user_id?: number;
        };
        Relationships: [
          {
            foreignKeyName: "users_user_permissio_permission_id_6d08dcd2_fk_auth_perm";
            columns: ["permission_id"];
            isOneToOne: false;
            referencedRelation: "auth_permission";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "users_user_permissions_user_id_92473840_fk_users_id";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      clients_with_revenue: {
        Row: {
          form_data: Json | null;
          id: number | null;
          status: number | null;
          storage_id: string | null;
          total_revenue: number | null;
        };
        Relationships: [];
      };
      users_materialized: {
        Row: {
          created_at: string | null;
          email: string | null;
          id: string | null;
        };
        Relationships: [];
      };
      users_view: {
        Row: {
          created_at: string | null;
          email: string | null;
          id: string | null;
        };
        Insert: {
          created_at?: string | null;
          email?: string | null;
          id?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string | null;
          id?: string | null;
        };
        Relationships: [];
      };
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const;
