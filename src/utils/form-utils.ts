// Function to extract text based on language
export const getText = (text: string, language: "el" | "en") => {
  if (!text.includes("/")) return text;

  const parts = text.split("/");
  if (parts.length !== 2) return text;

  return language === "el" ? parts[0].trim() : parts[1].trim();
};

// Add more utility functions as needed

// Validation helpers
export const validateStep = async (
  validateFn: () => Promise<boolean>,
  onSuccess: () => void,
  onError: () => void
) => {
  const isValid = await validateFn();
  if (isValid) {
    onSuccess();
  } else {
    onError();
  }
};

// Helper to add dynamic fields
export const addDynamicField = <T>(items: T[], defaultItem: T): T[] => {
  return [...items, defaultItem];
};

// Helper to remove dynamic fields
export const removeDynamicField = <T>(items: T[], index: number): T[] => {
  const newItems = [...items];
  newItems.splice(index, 1);
  return newItems;
};

// Helper to update dynamic fields
export const updateDynamicField = <T>(
  items: T[],
  index: number,
  field: keyof T,
  value: any
): T[] => {
  const newItems = [...items];
  newItems[index] = {
    ...newItems[index],
    [field]: value,
  };
  return newItems;
};
