/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
            DEFAULT: "hsl(var(--primary))",
            foreground: "hsl(var(--primary-foreground))",
            light: "hsl(210, 70%, 30%)",   // Lighter version
            hover: "hsl(210, 100%, 10%)",   // Darker for hover
          },
          // Taupe/Brown family
          secondary: {
            DEFAULT: "hsl(var(--secondary))",
            foreground: "hsl(var(--secondary-foreground))",
            light: "hsl(24, 15%, 60%)",   // Lighter version
            hover: "hsl(24, 10%, 40%)",   // Darker for hover
          },
          // Beige family
          tertiary: {
            DEFAULT: "hsl(var(--tertiary))",
            foreground: "hsl(var(--tertiary-foreground))",
            light: "hsl(33, 25%, 85%)",   // Lighter version
            hover: "hsl(33, 20%, 65%)",   // Darker for hover
          },
          // Light Blue family
          accent: {
            DEFAULT: "hsl(var(--accent))",
            foreground: "hsl(var(--accent-foreground))",
            light: "hsl(214, 60%, 90%)",   // Lighter version
            hover: "hsl(214, 50%, 74%)",   // Darker for hover
          },
          // Cream family
          light: {
            DEFAULT: "hsl(var(--light))",
            foreground: "hsl(var(--light-foreground))",
            light: "hsl(36, 80%, 96%)",   // Lighter version
            hover: "hsl(36, 70%, 82%)",   // Darker for hover
          },

        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
          hover: "hsl(0, 84%, 50%)", // Darker red for hover
          light: "hsl(0, 84%, 70%)", // Lighter red
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
