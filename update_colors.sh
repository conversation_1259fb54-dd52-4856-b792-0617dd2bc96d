#!/bin/bash

# Update text colors
sed -i '' 's/text-amber-800/text-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/text-amber-700/text-secondary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/text-amber-500/text-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/text-rose-500/text-destructive/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/text-rose-600/text-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx

# Update background colors
sed -i '' 's/bg-amber-50\/60/bg-light\/10/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/bg-rose-50\/60/bg-light\/10/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/bg-rose-50\/40/bg-light\/10/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx

# Update border colors
sed -i '' 's/border-amber-200/border-light\/30/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/border-rose-200/border-light\/30/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/border-rose-300/border-primary\/30/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx

# Update focus colors
sed -i '' 's/focus:border-amber-400/focus:border-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/focus:ring-amber-400/focus:ring-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/focus:border-rose-400/focus:border-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/focus:ring-rose-400/focus:ring-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
sed -i '' 's/focus:ring-rose-500/focus:ring-primary/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx

# Update hover colors
sed -i '' 's/hover:bg-rose-50\/70/hover:bg-light\/20/g' src/components/forms/steps/nannyRequest/FamilyInfoStep.tsx
